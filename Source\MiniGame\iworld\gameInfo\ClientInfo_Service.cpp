#include "ClientInfo_Service.h"

#include "ICloudProxy.h"
#include "OgreTimer.h"


#ifdef  IWORLD_SERVER_BUILD
extern bool g_bGameQuit;

#include "ClientAccount.h"
#include "AccountFBData/AccountFBData.h"
#include "WorldManager.h"
#include "ClientGameStandaloneServer.h"
#include "RoomManager.h"
#include "ClientUrl.h"
#include "MacroControlStatistics.h"
#include "LuaInterface.h"
#include "version.h"
#include "OgreStringUtil.h"

#ifdef USE_SENTRY
#include <iostream>
#include <fstream>
#include "sentry.h"
#endif //USE_SENTRY


using namespace MINIW;

PLATFORM_TYPE ClientInfo_Service::getPlatform()
{
	return EX_PLATFORM_LINUXPC;
}

const char* ClientInfo_Service::getPlatformStr()
{
	return "linuxpc";
}

int ClientInfo_Service::getPlatformId()
{
	return 1;
}

bool ClientInfo_Service::isPureServer()
{
	return true;
}

void ClientInfo_Service::setGameExit()
{
	g_bGameQuit = true;
}

bool ClientInfo_Service::isStudioServer()
{
#if STUDIO_SERVER
	return true;
#else
	return false;
#endif
}

void ClientInfo_Service::setHashParam(const char* key, const char* value)
{

}

bool ClientInfo_Service::startServerRoom()
{
	if (!StandaloneServer::m_bInitingEnterRoom)
	{
		AccountFBData::GetInstance().loadUinData();
		ClientAccountMgr::GetInstance().enterGame();

		//节约流量使用： 节约等级
		const char* flow_char_ = getEnterParam("flow_level");
		if (strlen(flow_char_) > 0)
		{
			int flow__ = atoi(flow_char_);
			setFlowLevel(flow__);
		}
		m_ServerClosing = false;
		return true;
	}
	return false;
}

bool ClientInfo_Service::stopServerRoom()
{
	if (!StandaloneServer::m_bInitingEnterRoom)
	{
		gotoGame("none");
		return true;
	}
	return false;

}

long long ClientInfo_Service::getCurrentGameMapId()
{
	if (GetWorldManagerPtr())
	{
		WorldDesc* desc = ClientAccountMgr::GetInstance().getCurWorldDesc();
		if (desc == NULL)
		{
			return 0;
		}
		else
		{
			return desc->fromowid;
		}

	}
	else
	{
		return 0;
	}
}


bool ClientInfo_Service::isStartingRoom()
{
	return StandaloneServer::m_bInitingEnterRoom;
}

void ClientInfo_Service::setStartingRoom(bool set)
{
	StandaloneServer::m_bInitingEnterRoom = set;
}

void ClientInfo_Service::setDataDir(const char* datadir)
{
	//todo check
	//m_datadir = (datadir ? String(datadir) : "");
	//FileManager::getSingleton().removePackage("save");
	//FileManager::getSingleton().addPackage(FILEPKG_DIR, "save", datadir, 0, false);
}

bool ClientInfo_Service::onShowImagePicker(std::string path, int type, bool crop /*= true*/, int x /*= 1280*/, int y /*= 1280*/)
{
	return false;
}

//******** 音频文件的选择框 codeby:wangshuai
bool ClientInfo_Service::onShowAudioPicker(std::string path, int type, std::string& fileName)
{
	return false;
}

int ClientInfo_Service::getSeverProxyOwindex()
{
	return GetRoomManager().getSeverProxyOwindex();
}

long long ClientInfo_Service::getOWlistUinOWID()
{
	return OWorldList::GetInstancePtr()->getUinOWID();
}

CSMYOWLIST* ClientInfo_Service::getOWList()
{
	return OWorldList::GetInstancePtr()->getOWList();
}

std::string ClientInfo_Service::getUploadTempPreUrl(int type)
{
	if (OWorldList::GetInstance().m_CSOWorld)
		return OWorldList::GetInstance().m_CSOWorld->getUploadTempPreUrl(type);
	return "";
}

std::string ClientInfo_Service::GetRoomServerUrl()
{
	return ClientUrl::GetRoomServerUrl();
}

void ClientInfo_Service::SendPvpStatisticsEvent(const jsonxx::Object& params)
{
	jsonxx::Object* jsonobj = STATISTICS_INTERFACE_EXEC(createStatisticsJsonObj(), 0);
	if (jsonobj)
	{
		jsonobj->import(params);

		STATISTICS_INTERFACE_EXEC(post(jsonobj->json(), true), 0);
		STATISTICS_INTERFACE_EXEC(send(false, true, true), 0);
		OGRE_DELETE(jsonobj);
	}
}

void ClientInfo_Service::hostAutoExit() {
	GetLuaInterface().callLuaString("__handle_autoexit__()");
	setClosing();
}

MINIW::ClientInfo_Service::ClientInfo_Service():ClientInfo(PLATFORM_TYPE::EX_PLATFORM_LINUXPC)
{
} 

MINIW::ClientInfo_Service::~ClientInfo_Service()
{
	CloseSentry();
}

std::string s_StartCmd;
std::string s_serverUser;
std::string s_version;

#ifdef USE_SENTRY
static sentry_value_t setSentryUser() {
	sentry_value_t user = sentry_value_new_object();
	sentry_value_set_by_key(user, "id", sentry_value_new_string(s_serverUser.c_str()));
	sentry_value_set_by_key(user, "ip_address", sentry_value_new_string("{{auto}}"));
	sentry_value_set_by_key(user, "version", sentry_value_new_string(s_version.c_str()));
	sentry_value_set_by_key(user, "params", sentry_value_new_string(s_StartCmd.c_str()));
	sentry_set_user(user);
	return user;
}

static void createCustomLogFile(int logSize) {
	// const std::string cusLogPath = "./CustomGameApp.log";
	// std::string strCusLogPath(cusLogPath.begin(), cusLogPath.end());
	// std::ofstream fout(strCusLogPath.c_str(), std::ios::out | std::ios::binary);
	//
	// const std::string logPath = "./GameApp.log";
	// std::string strLogPath(logPath.begin(), logPath.end());
	//
	// std::ifstream fin(strLogPath.c_str(), std::ios::in | std::ios::binary);
	// if (!fin.good())
	// {
	// 	if (fout.good()) fout.close();
	// 	return;
	// }
	//
	// fin.seekg(0, fin.end);
	// int length = fin.tellg();
	// fin.seekg(0, fin.beg);
	// if (length == 0)
	// {
	// 	fin.close();
	// 	if (fout.good()) fout.close();
	// 	return;
	// }
	//
	// int fileSize = std::min(logSize, length);
	// if (length > fileSize)
	// {
	// 	fileSize =logSize;
	// 	fin.seekg(length - fileSize);
	// }
	// std::string buffer(fileSize, ' ');
	// fin.read(&buffer[0], fileSize);
	// fout.write(buffer.c_str(), fileSize);
	//
	// fin.close();
	// fout.close();
}

static sentry_value_t sentry_before_send_handler(sentry_value_t event, void* hint, void* userdata) {
	sentry_value_t user = setSentryUser();
	// //裁剪log文件到另一个文件sentry.log， 截取最后20k
	// createCustomLogFile(20480);
	return user;
}

static void sentry_log_handler(sentry_level_t level, const char* message, va_list args, void* userdata)
{
	//LOG_INFO(message);
}

static sentry_options_t* m_sentryOptions = nullptr;
#endif

std::string getParamVal(const core::string& param)
{
	size_t idx = param.find('=');
	if (idx != string::npos)
		return param.substr(idx + 1).data();
	return "";
}

void ClientInfo_Service::InitSentry(const char* cmdstr) {
	s_StartCmd = cmdstr;
	std::string env = "";
	std::vector<core::string> vcmd;
	Rainbow::StringUtil::split(vcmd, s_StartCmd, " ");
	ostringstream os;
	for (auto& s : vcmd)
	{
		if (s.find("password") == string::npos && s.find("auth") == string::npos)
			os << s.c_str() << ' ';
		if (s_version.empty() && s.find("ver=") != string::npos)
		{
			size_t idx = s.find('=');
			size_t idxE = s.rfind('.');
			if (idx != string::npos && idxE != string::npos && idxE > idx)
				s_version = std::string("gameserver@") + core::string(s.substr(idx + 1, idxE - idx - 1)).data();
		}
		if (env.empty() && s.find("game_env=") != string::npos)
		{
			env = getParamVal(s);
		}
		// server的user用mapid
		if (s_serverUser.empty() && s.find("toloadmapid") != string::npos)
		{
			std::string mapid = getParamVal(s);
			ostringstream useros;
			useros << mapid << '-' << Rainbow::Timer::getTimeUS() / 1000;
			s_serverUser = useros.str();
		}
	}
	if (s_version.empty()) s_version = std::string("gameserver@") + PROJECT_VER_STR;
	s_StartCmd = os.str();

#ifdef USE_SENTRY
	sentry_options_t* options = nullptr;
	if (env == "10")
	{
		options = sentry_options_new();
		sentry_options_set_dsn(options, "https://<EMAIL>/43");
	}
	else if (env == "1")
	{
		options = sentry_options_new();
		sentry_options_set_dsn(options, "https://<EMAIL>/43");
	}
	else if (env == "0")
	{
		options = sentry_options_new();
		sentry_options_set_dsn(options, "https://<EMAIL>/43");
	}
	else
	{
		return;
	}
	
#ifdef IWORLD_UNIVERSE_BUILD
	sentry_options_set_dsn(options, "https://<EMAIL>/43");
#else
	if (env == "1")
		sentry_options_set_dsn(options, "https://<EMAIL>/43");
	else
		sentry_options_set_dsn(options, "https://<EMAIL>/43");
#endif

	std::string environment = "production";
	if (env == "1"){
		environment = "test";
	}

	//sentry_options_set_ca_certs(options, "/etc/pki/tls/certs/Sentry_CA.crt");
	sentry_options_set_release(options, s_version.c_str());
	sentry_options_set_debug(options, 1);
	sentry_options_set_environment(options, environment.c_str());

	const std::string databasePath = "./.sentry-native";
	sentry_options_set_database_path(options, databasePath.c_str());

	const std::string handlerPath =  "./crashpad_handler";
	sentry_options_set_handler_path(options, handlerPath.c_str());

	sentry_set_level(SENTRY_LEVEL_DEBUG);
	//sentry_options_set_logger(options, sentry_log_handler, nullptr);

	sentry_options_set_auto_session_tracking(options, 1);

	sentry_options_set_sample_rate(options, 1);

	// const std::string cusLogPath = "./CustomGameApp.log";
	// sentry_options_add_attachment(options, cusLogPath.c_str());

	int retInit = sentry_init(options);
	LOG_INFO("sentry_inti result :%d", retInit);
	
	if (retInit != 0)
	{
		return;
	}

	setSentryUser();

	sentry_options_set_before_send(options, sentry_before_send_handler, nullptr);

	m_sentryOptions = options;

	
	for (auto& s : vcmd)
	{
		if (s.find("toloadmapid") != string::npos
			|| s.find("rentserver") != string::npos
			|| s.find("personal") != string::npos
			|| s.find("appid") != string::npos
			|| s.find("ip=") != string::npos
		)
		{
			size_t idx = s.find('=');
			if (idx != string::npos)
				sentry_set_tag(core::string(s.substr(0, idx)).c_str(),core::string( s.substr(idx + 1).data()).c_str());
		}
	}
#endif
}

void ClientInfo_Service::CloseSentry() {
#ifdef USE_SENTRY
	if (m_sentryOptions != nullptr)
	{
		sentry_close();
		m_sentryOptions = nullptr;
	}
#endif
}

void ClientInfo_Service::SendSentryEvent(const char* msg)
{
#ifdef USE_SENTRY
	if (msg == nullptr)
	{
		return;
	}

	if (m_sentryOptions != nullptr) {
		return;
	}

	if (m_sentryOptions != nullptr) {
		sentry_options_set_before_send(m_sentryOptions, nullptr, nullptr);
	}

	setSentryUser();

	createCustomLogFile(1024);

	sentry_value_t event = sentry_value_new_event();
	sentry_value_set_by_key(event, "message", sentry_value_new_string(msg));

	sentry_value_t info = sentry_value_new_object();
	sentry_value_set_by_key(info, "device_id", sentry_value_new_string(s_version.c_str()));

	sentry_value_t contexts = sentry_value_new_object();
	sentry_value_set_by_key(contexts, "info", info);

	sentry_value_set_by_key(event, "contexts", contexts);
	sentry_capture_event(event);

	if (m_sentryOptions != nullptr) {
		sentry_options_set_before_send(m_sentryOptions, sentry_before_send_handler, nullptr);
	}
#endif
}

extern bool g_bGameQuit;
void ClientInfo_Service::setServerGameQuit()
{
	g_bGameQuit = true;
}

void ClientInfo_Service::SendLuaErrMsg(const char* msg) {
#ifdef USE_SENTRY
	SendSentryEvent(msg);
#endif
#ifdef DEDICATED_SERVER
	SLOG(INFO) << "[LUA-ERROR]" << msg;
	Rainbow::GetICloudProxyPtr()->SimpleErrLog(0, 0, "lua_error", msg);
#endif
}


#endif
