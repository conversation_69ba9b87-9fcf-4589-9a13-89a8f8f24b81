#include "ActorCubeChest.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "PlayerStateController.h"
#include "world.h"
#include "ClientActorManager.h"
#include "LivingLocoMotion.h"
#include "BlockMaterialMgr.h"
#include "WorldManager.h"
#include "WorldRender.h"
#include "PlayerLocoMotion.h"
#include "EffectManager.h"
#include "special_blockid.h"
#include "DefManagerProxy.h"
#include "MpActorManager.h"
#include "worlddisplay/BlockScene.h"

#include "GameNetManager.h"
#include "Entity/OgreEntity.h"
#include "Entity/OgreModel.h"
#include "OgrePhysXManager.h"
#include "ObserverEventManager.h"
#include "ChestLocomotion.h"
#include "BindActorComponent.h"
#include "OgreUtils.h"
#include "SoundComponent.h"
#include "BindActorComponent.h"
#include "BindActorComponent.h"
#include "ParticlesComponent.h"
#include "ActorBodySequence.h"
#include "ActorVehicleAssemble.h"
#include "VehicleWorld.h"
#include "UGCEntity.h"
#include "UGCModelLoader.h"
#include "SandboxGameDef.h"
#include "UgcAssetHeader.h"
using namespace MINIW;
IMPLEMENT_SCENEOBJECTCLASS(ActorCubeChest)
using namespace Rainbow;
using namespace MNSandbox;

ActorCubeChest::ActorCubeChest() : m_BallEntity(NULL)
{
	CreateComponent<ChestLocoMotion>("ChestLocoMotion");
 
	m_SpawnPos = WCoord(0, 0, 0);
	m_InvalidCatchTick = 0;
	model_id = 0;

	m_Dribbing = false;
 	createEvent();

	def_Width = 2;
	def_Height = 3;
	def_Deepth = 6;

	chest_id = 0;
	m_StorageBox = nullptr;
}

ActorCubeChest::~ActorCubeChest()
{
	Entity::Destory(m_BallEntity);m_BallEntity = nullptr;
 
	SANDBOX_DELETE(m_StorageBox); m_StorageBox = nullptr;
	
	LOG_INFO("==ActorCubeChest::~ActorCubeChest()");
}

int ActorCubeChest::getMass()
{
	return 6000000;
}


void ActorCubeChest::init()
{
#ifndef IWORLD_SERVER_BUILD
	m_BallEntity = Entity::Create();
	//  11000049  礼盒
	UGCEntity* ugcEntity = UGCEntity::Create();
	m_BallEntity = ugcEntity;
	ugcEntity->LoadModelAsync("ugcModel/11000049/body.obj", UgcAssetType::OBJ, [this](bool success, UGCModelLoader* modelLoader)->void
		{
			if (success && m_BallEntity && modelLoader)
			{
				Rainbow::Model* model = modelLoader->GetModel();
				if (model)
				{
					m_BallEntity->Load(model);
					//m_ModelIsLoading = false;
				}
			}
		});


	m_BallEntity->PlayAnim(100100);
	m_BallEntity->SetInstanceAmbient(ColourValue(0.2f, 0.2f, 0.2f));
	if (m_pWorld != nullptr && m_pWorld->getScene())
	{
		m_pWorld->getScene()->AddGameObject(m_BallEntity->GetGameObject());
	}
	float modelScale = 300.0f; // 使用与def->ModelScale相同的缩放比例
	m_BallEntity->SetScale(Rainbow::Vector3f(modelScale, modelScale, modelScale));

#endif


	// 设置模型缩放比例
	// 游戏中BLOCK_SIZE=100表示1个方块是100厘米（1米）
	// WCoord类使用厘米作为单位


	// 根据模型实际尺寸设置碰撞盒参数（单位：厘米）
	// X轴方向尺寸: 约 0.4093厘米
	// Y轴方向尺寸: 约 0.5213厘米
	// Z轴方向尺寸: 约 1.0007厘米
	float Height = this->def_Height;// 高度对应Y轴方向（约0.52f厘米）
	float Width = this->def_Width; // 宽度对应X轴方向（约0.41厘米）
	float depth = this->def_Deepth;


	if (getLocoMotion())
	{
		getLocoMotion()->setBoundCuboid((int)(Height * 100), (int)(Width * 100), (int)(depth * 100));
		getLocoMotion()->setAttackBound((int)(Height * 100), (int)(Width * 100), (int)(depth * 100));
		getLocoMotion()->m_yOffset = getLocoMotion()->m_BoundHeight / 2;
	}

	m_SpawnPos = getPosition();
	m_ServerYawCmp = getLocoMotion()->m_RotateQuat.ToUInt32();
	setItemId(ms_itemID);

	if (m_pWorld && !m_pWorld->isRemoteMode()) {
		initStorageBox();
	}
}

void ActorCubeChest::createEvent()
{
}


int ActorCubeChest::getObjType() const
{
	return OBJ_TYPE_CUBECHEST;
}

void ActorCubeChest::tick()
{
	ClientActor::tick();
	if(getPosition().y < -64*BLOCK_SIZE)
	{
		kill();
	}

	if(!m_pWorld->isRemoteMode())
	{
		CollideAABB box;
		getCollideBox(box);
		box.expand(20, 0, 20);

		std::vector<IClientActor *>actors;
		m_pWorld->getActorsInBoxExclude(actors, box, this);

		for(size_t i=0; i<actors.size(); i++)
		{
			ClientActor* pactor = actors[i]->GetActor();
			if(pactor->canBePushed())
			{ 
				pactor->applyActorCollision(this);
			}
		}
	}

#ifndef IWORLD_SERVER_BUILD
	Vector4f lightparam(0,0,0,0);
	m_pWorld->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(getPosition()));
	m_BallEntity->SetInstanceData(lightparam);
#endif
}

void ActorCubeChest::update(float dtime)
{
	ClientActor::update(dtime);

	{
		ChestLocoMotion *loc = static_cast<ChestLocoMotion *>(getLocoMotion());
		if (!m_BallEntity)
			return;
		m_BallEntity->SetPosition(loc->m_UpdatePos);
		//m_BallEntity->setRotation(loc->m_UpdateRot);

		ClientPlayer *player = nullptr;
		auto bindAComponent = getBindActorCom();
		if (bindAComponent)
		{
			player = dynamic_cast<ClientPlayer *>(bindAComponent->getTarget());
		}
		if (player && m_Dribbing)
		{
			/*float addangle = m_DribbingMotion.length()*0.15;
			LOG_INFO("kekeke addangle:%f  dtime:%f", addangle, dtime);
			m_DribbingAngle += m_DribbingMotion.length()*15 * dtime;

			m_DribbingAngle = (int)m_DribbingAngle % 360;

			m_BallEntity->SetRotation(player->getLocoMotion()->m_RotateYaw, -m_DribbingAngle, 0);*/
		}
		else if (!player)
		{
			m_BallEntity->SetRotation(loc->m_UpdateRot);
		}
		// TODO 控件内部自己有Tick
		//m_BallEntity->Update(TimeToTick(dtime));
	}
}

void ActorCubeChest::enterWorld(World *pworld)
{
	ClientActor::enterWorld(pworld);
	if (m_BallEntity && pworld->getScene())
	{
		pworld->getScene()->AddGameObject(m_BallEntity->GetGameObject());
	}
	if (m_pWorld && m_StorageBox)
	{
		m_StorageBox->enterWorld(m_pWorld);
	}
#ifdef USE_PHYSX
	static_cast<ChestLocoMotion *>(getLocoMotion())->checkPhysWorld();
	static_cast<ChestLocoMotion *>(getLocoMotion())->attachPhysActor();
#endif
}

void ActorCubeChest::leaveWorld(bool keep_inchunk)
{
#ifdef USE_PHYSX
	static_cast<ChestLocoMotion *>(getLocoMotion())->detachPhysActor();
	static_cast<ChestLocoMotion *>(getLocoMotion());
#endif

	ClientActor::leaveWorld(keep_inchunk);
}

flatbuffers::Offset<FBSave::SectionActor> ActorCubeChest::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	auto actor = FBSave::CreateActorCubeChest(builder, basedata, model_id);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorCubeChest, actor.Union());

}

bool ActorCubeChest::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorCubeChest *>(srcdata);
	loadActorCommon(src->basedata());

	model_id = src->itemid();
	init();
	return true;
}

void ActorCubeChest::onCollideWithPlayer(ClientActor *player)
{
	 
}

void ActorCubeChest::collideWithActor(ClientActor *actor)
{
	ChestLocoMotion *loc = static_cast<ChestLocoMotion *>(getLocoMotion());
	if (loc->m_Motion.Length() > GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.can_knock_up_motion && actor->getObjType() == OBJ_TYPE_MONSTER)
	{
		Rainbow::Vector3f dir = loc->m_Motion;
		dir.y = 0;
		actor->getLocoMotion()->addMotion(loc->m_Motion.x*GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.ball_knock_up_motionX, GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.ball_knock_up_motionY*dir.Length(), loc->m_Motion.z * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.ball_knock_up_motionZ);

		ParticlesComponent::playParticles(actor, "ball_hit.ent");

		auto soundComp = actor->getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound("ent.3420.hit", 1, 1, 6);
		}

		return;
	}

	//如果是和载具碰撞，检测碰撞到的方块是否是触碰方块，如果是触碰方块，需要进行触发相应的效果
	if (actor->getObjType() == OBJ_TYPE_VEHICLE)
	{
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
		int x = -1, y = -1, z = -1;
		vehicle->intersect(this, x, y, z);
		VehicleWorld* vehicleWorld = vehicle->getVehicleWorld();
		int blockid = vehicleWorld->getBlockID(x, y, z);
		if (blockid == BLOCK_BALLCOLLIDER)
		{
			auto material = g_BlockMtlMgr.getMaterial(blockid);
			if (material)
				material->onActorCollidedWithBlock(vehicleWorld, WCoord(x, y, z), this);
		}
	}

	applyActorCollision(this);
}


void ActorCubeChest::playBodyEffect(const char* fxname, bool sync/* =true */, float loopPlayTime /* = -1.0f */)
{
	if(m_BallEntity) m_BallEntity->PlayMotion(fxname, true, 0, loopPlayTime);

	//主机才去做广播
	if (!m_pWorld->isRemoteMode())
	{
		PB_PlayEffectHC playEffectHC;
		playEffectHC.set_effecttype(PB_EFFECT_STRINGACTORBODY);

		PB_EffectStringActorBody* actorBody = playEffectHC.mutable_stringactorbody();
		actorBody->set_objid(getObjId());
		actorBody->set_effectname(fxname);
		actorBody->set_status(0);
		actorBody->set_loopplaytime(loopPlayTime);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYEFFECT_HC, playEffectHC, this, true);
	}
}

void ActorCubeChest::playBodyEffectClient(const char* fxname, float scale, float loopPlayTime /* = -1.0f */)
{
	if (m_BallEntity) m_BallEntity->PlayMotion(fxname, true, 0, loopPlayTime);

	//客机才发送到主机
	if (m_pWorld->isRemoteMode())
	{
		PB_PlayEffectCH playEffectCH;
		playEffectCH.set_effecttype(PB_EFFECT_STRINGACTORBODY);
		playEffectCH.set_effectscale(int(scale * 1000));

		PB_EffectStringActorBody* actorBody = playEffectCH.mutable_stringactorbody();
		if (actorBody == NULL) return;
		actorBody->set_objid(getObjId());
		if (fxname) actorBody->set_effectname(fxname);
		actorBody->set_status(0);
		actorBody->set_loopplaytime(loopPlayTime);

		GetGameNetManagerPtr()->sendToHost(PB_PLAYEFFECT_CH, playEffectCH);
	}
}

void ActorCubeChest::stopBodyEffect(const char* fxname, bool sync /* = true */)
{
	if(m_BallEntity) m_BallEntity->StopMotion(fxname);
}

void ActorCubeChest::kickedByPlayer(int type, float charge, ClientPlayer *player)
{
}

ActorCubeChest *ActorCubeChest::create(World *pworld, int x, int y, int z, float vx, float vy, float vz,int chestid)
{
	ActorCubeChest *actor = SANDBOX_NEW(ActorCubeChest);

	if (vx > 0 && vy > 0 && vz>0) {
		actor->def_Width = vx;
		actor->def_Height = vy;
		actor->def_Deepth = vz;
	}
	actor->m_pWorld = pworld;
	actor->setChestId(chestid);
	actor->init();
	actor->getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
	if (!pworld) return actor;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return actor;
	actorMgr->spawnActor(actor, x, y, z, 0.0f, 0.0f);

	actor->m_SpawnPos = actor->getPosition();
	return actor;
}

void ActorCubeChest::bePushWithPlayer(ClientActor *player)
{
	ChestLocoMotion *loc = static_cast<ChestLocoMotion *>(getLocoMotion());
	if (loc->m_PhysActor)
	{
		int r = loc->m_BoundHeight / 2;
		int d2 = r + player->getLocoMotion()->m_BoundSize / 2;
		WCoord dp = getPosition() - player->getPosition();

		Rainbow::Vector3f force = dp.toVector3();
		force.y = 0;
		force = MINIW::Normalize(force);

		float radio = 2000.0f*GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.phys_mass*GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.mass_scale;
		loc->m_PhysActor->AddForce(force*radio);//
	}
}

void ActorCubeChest::moveToPosition(const WCoord &pos, Rainbow::Quaternionf &rot, int interpol_ticks)
{
	ChestLocoMotion *loc = static_cast<ChestLocoMotion *>(getLocoMotion());

	loc->m_PosRotationIncrements = interpol_ticks;
	loc->m_ServerPos = pos;
	loc->m_ServerRot = rot;
}

void ActorCubeChest::setMotionChange(const Rainbow::Vector3f &motion, bool addmotion, bool changepos, bool sync_pos)
{
	ChestLocoMotion *ballloc = static_cast<ChestLocoMotion *>(getLocoMotion());

	ClientActor::setMotionChange(motion, addmotion, changepos, sync_pos);

	if(ballloc->m_PhysActor)
	{
		ballloc->m_PhysActor->SetLinearVelocity(ballloc->m_Motion * MOTION2VELOCITY);
		if(changepos)
		{
			//ballloc->m_PhysActor->SetPos(ballloc->m_Position.toVector3(), ballloc->m_RotateQuat);
		}
	}
}

void ActorCubeChest::resetRound()
{
 
}

bool ActorCubeChest::interact(ClientActor *pPlayer, bool onshift /* = false */, bool isMobile)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(pPlayer);
	if (nullptr != pTempPlayer)
	{
		return leftClickInteract(pTempPlayer);
	}
	else
	{
		return false;
	}
}

bool ActorCubeChest::leftClickInteract(ClientActor *player)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (pTempPlayer && m_StorageBox)
	{
		pTempPlayer->openContainer(m_StorageBox);
	}
	return true;
 
}


void ActorCubeChest::playSoundByPhysCollision()
{
	auto physActor = static_cast<ChestLocoMotion *>(getLocoMotion())->m_PhysActor;
	if (physActor && physActor->GetLinearVelocity().Length() > GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.threshold)
	{
		//char soundname[32];
		//sprintf(soundname, "ent.3420.kick%d", GenRandomInt(1, 6));
		m_pWorld->getEffectMgr()->playSound(getPosition(), "ent.3420.kick1", 1.0f, 1.0f, PLAYSND_SYNC | PLAYSND_LONGDIST);
	}
}

void ActorCubeChest::dribblingRotate(Rainbow::Vector3f motion, float yaw)
{
 
}

bool ActorCubeChest::isPhysics()
{
	ChestLocoMotion* pLoco = dynamic_cast<ChestLocoMotion*>(getLocoMotion());
	if (pLoco) {
		return pLoco->m_hasPhysActor;
	}

	return false;
}

void ActorCubeChest::onClear()
{
	if (this->isPhysics())
	{
		ParticlesComponent::playParticles(this, "10021.ent");
	}
}

BindActorComponent*  ActorCubeChest::getBindActorCom()
{
 
	return nullptr;
	 
}


long long ActorCubeChest::getBindTargetID()
{
	return 0;
}


 
void ActorCubeChest::initStorageBox()
{
	if (!m_StorageBox)
	{
		m_StorageBox = SANDBOX_NEW(AirDropStorageBox);
		m_StorageBox->setBaseIndex(STORAGE_START_INDEX);
		m_StorageBox->m_GridCount = 20;
		m_StorageBox->m_isRideContainer = false;
		WCoord pos = WCoord(getPosition().x / BLOCK_SIZE, getPosition().y / BLOCK_SIZE, getPosition().z / BLOCK_SIZE);
		m_StorageBox->m_BlockPos = pos;
		if (chest_id == 0) chest_id = 2331;
		m_StorageBox->setChestId(chest_id);

		ChunkRandGen* randgen = GetDefaultRandGen();

		std::vector<GenerateItemDesc>items;
		WorldContainerMgr::generateChestItems(items, chest_id, randgen, 0);

		this->intNumber(20);

		for (size_t i = 0; i < items.size(); i++)
		{
			std::string userData;

			int index = this->popNumber(*randgen);
			if (index < 0) break;

			m_StorageBox->setItem(index, items[i].itemid, items[i].itemnum, userData.c_str());
		}
	}
}

void ActorCubeChest::intNumber(int n)
{
	for (int i = 0; i < n; i++) m_Numbers.push_back(i);
}

int ActorCubeChest::popNumber(ChunkRandGen& randgen)
{
	if (m_Numbers.empty()) return -1;

	int i = randgen.get(int(m_Numbers.size()));
	int num = m_Numbers[i];
	m_Numbers[i] = m_Numbers.back();
	m_Numbers.pop_back();

	return num;
}
