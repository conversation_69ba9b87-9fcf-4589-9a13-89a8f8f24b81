
#include "ActorBoat.h"
#include "BlockMaterialMgr.h"
#include "special_blockid.h"
#include "world.h"
#include "ClientActorLiving.h"
#include "LivingLocoMotion.h"
#include "ClientPlayer.h"
#include "ClientActorManager.h"
#include "OgrePrerequisites.h"
#include "DefManagerProxy.h"
#include "ObserverEventManager.h"
#include "Entity/OgreModel.h"
//#include "OgreGameScene.h"
#include "WorldManager.h"
#include "BlockScene.h"
#include "BoatLocomotion.h"
#include "SandboxListener.h"
#include "RiddenComponent.h"
#include "TriggerComponent.h"
#include "DropItemComponent.h"
#include "SwarmComponent.h"
#include "PlayerControl.h"
#include "ActorBody.h"
#include "ActorAttrib.h"
#include "SandBoxManager.h"
#include "ClientMob.h"
#include "UGCEntity.h"
#include "UGCModelLoader.h"
#include "SandboxGameDef.h"
#include "UgcAssetHeader.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

static Rainbow::Vector3f s_MotionDecay(0.9f, 0.98f, 0.9f);
static float s_InWaterForce = 1.0f;
IMPLEMENT_SCENEOBJECTCLASS(ActorBoat)
ActorBoat::ActorBoat() : m_ItemID(0), m_HostMotivate(true), m_Model(NULL)
{
	createEvent();

	CreateComponent<SwarmComponent>("SwarmComponent");
	CreateComponent<ActorAttrib>("ActorAttrib");
	CreateComponent<BoatLocomotion>("BoatLocomotion");

	getLocoMotion()->setBound(60, 150);
	//getLocoMotion()->m_yOffset = getLocoMotion()->m_BoundHeight/2;

	//memset(m_OtherRiddens, 0, sizeof(m_OtherRiddens));

	auto RidComp = sureRiddenComponent();
	if (RidComp) RidComp->initOtherRiddens(2);
}

ActorBoat::~ActorBoat()
{
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_Model);
}

void ActorBoat::init(int itemid)
{
	float scale = 2.0f;
	m_ItemID = itemid;
	const MonsterDef *mdef = GetDefManagerProxy()->getMonsterDef(itemid - 10000);
	if(mdef)
	{
		getAttrib()->initHP(mdef->Life);
		getAttrib()->initMaxHP(mdef->Life);
	}
#ifndef IWORLD_SERVER_BUILD
	if(itemid == 13806)
	{
		m_Model = g_BlockMtlMgr.getModel("entity/120008/body.omod");
		//m_NumRidePos = 2;
		auto RidComp = getRiddenComponent();
		if (RidComp) RidComp->setNumRiddenPos(2);

		const MonsterDef *def = GetDefManagerProxy()->getMonsterDef(itemid-10000);
		if(def)
		{
			getLocoMotion()->m_HitBoundHeight	= (int)(def->HitHeight*def->ModelScale);
			getLocoMotion()->m_HitBoundWidth	= (int)(def->HitWidth*def->ModelScale );
			getLocoMotion()->m_BoundSize		= (int)(def->Width*def->ModelScale    );
			getLocoMotion()->m_BoundHeight		= (int)(def->Height*def->ModelScale   );
		}
	}
	else if (itemid == 14519) {
		m_Model = g_BlockMtlMgr.getModel("entity/110093/body.omod");
		const MonsterDef *def = GetDefManagerProxy()->getMonsterDef(itemid-10000);
		if(def)
		{
			getLocoMotion()->m_HitBoundHeight	= (int)(def->HitHeight*def->ModelScale);
			getLocoMotion()->m_HitBoundWidth	= (int)(def->HitWidth*def->ModelScale );
			getLocoMotion()->m_BoundSize		= (int)(def->Width*def->ModelScale    );
			getLocoMotion()->m_BoundHeight		= (int)(def->Height*def->ModelScale   );
		}

	}
	else if(itemid == 11000122)
	{
		UGCEntity* ugcEntity = UGCEntity::Create();
			ugcEntity->LoadModelAsync("ugcModel/11000122/body.obj", UgcAssetType::OBJ, [this](bool success, UGCModelLoader* modelLoader)->void
				{
					if (success && modelLoader)
					{
						Rainbow::Model* model = modelLoader->GetModel();
						if (model)
						{
							m_Model = model;
						}
					}
				});
			scale = 500.0f;
			{
				// getLocoMotion()->m_HitBoundHeight	= (int)(0.33 * scale);
				// getLocoMotion()->m_HitBoundWidth	= (int)(1.0 *   scale);
				// getLocoMotion()->m_BoundSize		= (int)(1.0 *   scale);
				// getLocoMotion()->m_BoundHeight		= (int)(0.33 *   scale);
			}
	}
	
	else if (itemid == ITEM_BAMBOO_RAFT) //��
	{
		m_Model = g_BlockMtlMgr.getModel("entity/390088/body.omod"); //�滻����ʽ��ģ��
		auto RidComp = getRiddenComponent();
		if (RidComp) RidComp->setNumRiddenPos(1);
	}
	else
	{
		//m_Model = g_BlockMtlMgr.getModel("entity/120004/body.omod");
		//m_NumRidePos = 1;
		UGCEntity* ugcEntity = UGCEntity::Create();
		// 加载模型
		// 11000084/body.obj 警车 
		// 11000090/body.obj 方程式赛车
		ugcEntity->LoadModelAsync("ugcModel/11000090/body.obj", UgcAssetType::OBJ, [this](bool success, UGCModelLoader* modelLoader)->void
			{
				if (success && modelLoader)
				{
					Rainbow::Model* model = modelLoader->GetModel();
					if (model)
					{
						m_Model = model;
					}
				}
			});

		auto RidComp = getRiddenComponent();
		if (RidComp) RidComp->setNumRiddenPos(1);
	}

	if (!m_Model)
		return;
	
	// 设置模型整体缩放
    m_Model->SetScale(Vector3f(scale, scale, scale));
    
	m_Model->SetInstanceAmbient(ColourValue(0.2f, 0.2f, 0.2f));
	if (m_Model->IsKindOf<ModelLegacy>())
	{
		dynamic_cast<ModelLegacy*>(m_Model)->InitWaterMaskMaterial("chuan_box");
	}
#else
	if(itemid == 13806 || itemid == 14519)
	{
		//m_NumRidePos = 2;
		auto RidComp = getRiddenComponent();
		if (RidComp)
		{
			RidComp->setNumRiddenPos(2);
		}
		const MonsterDef *def = GetDefManagerProxy()->getMonsterDef(itemid-10000);
		if(def)
		{
			getLocoMotion()->m_HitBoundHeight = def->HitHeight*def->ModelScale;
			getLocoMotion()->m_HitBoundWidth = def->HitWidth*def->ModelScale;
			getLocoMotion()->m_BoundSize = def->Width*def->ModelScale;
			getLocoMotion()->m_BoundHeight = def->Height*def->ModelScale;
		}
	}
	else if(itemid == 11000122)
	{
		getLocoMotion()->m_HitBoundHeight = 0.33 * scale;
		getLocoMotion()->m_HitBoundWidth = 1.0 * scale;
		getLocoMotion()->m_BoundSize = 1.0 * scale;
		getLocoMotion()->m_BoundHeight = 0.33 * scale;
	}
	else
	{
		//m_NumRidePos = 1;
		auto RidComp = getRiddenComponent();
		if (RidComp)
		{
			RidComp->setNumRiddenPos(1);
		}
	}
	m_Model = NULL;
#endif
}

void ActorBoat::createEvent()
{
	//Rainbow::Vector3f getRiddenBindPos(ClientActor *ridden)
	typedef ListenerFunctionRef<Rainbow::Vector3f&, ClientActor*> ListenerGetRiddenBindPos;
	ListenerGetRiddenBindPos* listenerGetRiddenBindPos = SANDBOX_NEW(ListenerGetRiddenBindPos, [&](Rainbow::Vector3f& ret, ClientActor* ridden) -> void {
		ret = this->getRiddenBindPos(ridden);

		});
	Event2().Subscribe("getRiddenBindPos", listenerGetRiddenBindPos);

	//bool getRiddenChangeFPSView()
	typedef ListenerFunctionRef<bool&> ListenerGetRiddenChangeFPSView;
	ListenerGetRiddenChangeFPSView* listenerGetRiddenChangeFPSView = SANDBOX_NEW(ListenerGetRiddenChangeFPSView, [&](bool& ret) -> void {
		ret = this->getRiddenChangeFPSView();
		});
	Event2().Subscribe("getRiddenChangeFPSView", listenerGetRiddenChangeFPSView);

	//bool canBeRided(ClientPlayer *player)
	typedef ListenerFunctionRef<bool&, ClientPlayer*> ListenerCanBeRided;
	ListenerCanBeRided* listenerCanBeRided = SANDBOX_NEW(ListenerCanBeRided, [&](bool& ret, ClientPlayer *player) -> void {
		ret = this->canBeRided(player);
		});
	Event2().Subscribe("canBeRided", listenerCanBeRided);

	//WCoord getRiderPosition(ClientActor *ridden)
	typedef ListenerFunctionRef<WCoord&, ClientActor*> ListenerGetRiderPosition;
	ListenerGetRiderPosition* listenerGetRiderPosition = SANDBOX_NEW(ListenerGetRiderPosition, [&](WCoord& ret, ClientActor *ridden) -> void {
		ret = this->getRiderPosition(ridden);
		});
	Event2().Subscribe("getRiderPosition", listenerGetRiderPosition);
}
flatbuffers::Offset<FBSave::SectionActor> ActorBoat::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	flatbuffers::Offset<flatbuffers::Vector<uint64_t>> otherriddens = 0;
	WORLD_ID RiddenByActorID = 0;
	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		if (RidComp->getNumRiddenPos() > 1)
		{
			uint64_t riddens[16];
			for (int i = 1; i < RidComp->getNumRiddenPos(); i++)
			{
				//riddens[i-1] = m_OtherRiddens[i-1];
				riddens[i - 1] = RidComp->getRiddenByActorID(i);
			}
			otherriddens = builder.CreateVector(riddens, RidComp->getNumRiddenPos() - 1);
		}
		RiddenByActorID = RidComp->getRiddenByActorID();
	}

	auto actor = FBSave::CreateActorBoat(builder, basedata, m_ItemID, RiddenByActorID, otherriddens, getAttrib()->getHP());

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorBoat, actor.Union());
}

bool ActorBoat::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorBoat *>(srcdata);
	loadActorCommon(src->basedata());

	init(src->itemid());

	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		RidComp->setRiddenByActorObjId(src->ridden());

		auto otherriddens = src->otherriddens();
		if (otherriddens)
		{
			for (size_t i = 0; i < otherriddens->size(); i++)
			{
				//m_OtherRiddens[i] = otherriddens->Get(i);
				RidComp->setRiddenByActorObjId(otherriddens->Get(i), i + 1);
			}
		}
		RidComp->clearRiddenActor(); //��ֹ�ؽ���ͼ��Ƥ���������� code-by:lizb
	}
	getAttrib()->initHP(src->hp());
	if (version == 0) getAttrib()->initHP(getAttrib()->getHP()* 5.0f);

	if (getAttrib()->getHP() == 0 || getAttrib()->getHP() > getAttrib()->getMaxHP()) getAttrib()->initHP(getAttrib()->getMaxHP());
	return true;
}

ActorBoat *ActorBoat::create(int itemid, World *pworld, int x, int y, int z)
{
	ActorBoat *boat = SANDBOX_NEW(ActorBoat);
	boat->init(itemid);
	if (!pworld) return NULL;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return NULL;
	actorMgr->spawnActor(boat, x, y, z, 0.0f, 0.0f);

	auto triggerComponent = boat->getTriggerComponent();
	if (triggerComponent)
	{
		triggerComponent->ActorCreateOnTrigger(itemid, boat->getObjType());
	}
	return boat;
}

bool ActorBoat::canBeRided(ClientPlayer *player)
{
	auto RidComp = getRiddenComponent();
	if (RidComp)
		return (RidComp->findRiddenIndex(player) >= 0 || RidComp->findEmptyRiddenIndex() >= 0);
	return false;
}

//WORLD_ID ActorBoat::getRiddenByActorID(int i)
//{
//	if(i == 0) return ClientActor::getRiddenByActorID();
//	else
//	{
//		assert(i < getNumRiddenPos());
//		return m_OtherRiddens[i-1];
//	}
//}

//void ActorBoat::setRiddenByActor(ClientActor *p, int i)
//{
//	if(i == 0) ClientActor::setRiddenByActor(p, 0);
//	else
//	{
//		assert(i < getNumRiddenPos());
//		if(p) m_OtherRiddens[i-1] = p->getObjId();
//		else m_OtherRiddens[i-1] = 0;
//	}
//}

Rainbow::Vector3f ActorBoat::getRiddenBindPos(ClientActor *ridden)
{
	auto RidComp = getRiddenComponent();
	if (m_Model && RidComp && RidComp->getNumRiddenPos() > 1)
	{
		int i = RidComp->findRiddenIndex(ridden);
		return m_Model->GetAnchorWorldMatrix(200+i).GetPosition();
	}
	else if (RidComp) return RidComp->getRiddenBindPos_Base(ridden);
	return Rainbow::Vector3f(0, 0, 0);
}

WCoord ActorBoat::getRiderPosition(ClientActor *ridden)
{
	auto RidComp = getRiddenComponent();
	if (m_Model && RidComp && RidComp->getNumRiddenPos() > 1)
	{
		int i = RidComp->findRiddenIndex(ridden);
		return WCoord(m_Model->GetAnchorWorldMatrix(200+i).GetPosition());
	}
	else
	{
		if (RidComp)
		{
			return RidComp->getRiderPosition_Base(ridden);
		}
		else return WCoord(0, 0, 0);
	}
}

bool ActorBoat::interact(ClientActor *player, bool onshift/* =false */, bool isMobile)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		auto RidComp = getRiddenComponent();
		if (RidComp)
		{
			int i = 0;
			for (; i < RidComp->getNumRiddenPos(); i++)
			{
				WORLD_ID ridden = RidComp->getRiddenByActorID(i);
				if (ridden == 0 || ridden == pTempPlayer->getObjId())
				{
					break;
				}
			}
			if (i == RidComp->getNumRiddenPos()) return false;
		}
		return pTempPlayer->mountActor(this);
		return true;
	}
	else
	{
		return false;
	}
}

//void ActorBoat::onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum)
//{
//	if (m_Model)
//	presult->addRenderable(m_Model, RL_SCENE, NULL);
//}

void ActorBoat::tick()
{
	ClientActor::tick();

	/*if (getPosition().y < -64 * BLOCK_SIZE)
	{
		kill();
	}
	*/

	if(!m_pWorld->isRemoteMode())
	{
		std::vector<IClientActor *>actors;
		CollideAABB bbox;
		getCollideBox(bbox);
		bbox.expand(20, 0, 20);
		m_pWorld->getActorsInBoxExclude(actors, bbox, this);
		for(size_t i=0; i<actors.size(); i++)
		{
			ClientActor* actor = actors[i]->GetActor();
			auto RidComp = actor->getRiddenComponent();
			if (/*actor->m_RidingActor!=getObjId()*/!(RidComp && RidComp->checkRidingByActorObjId(getObjId())) && actor->canBePushed() && dynamic_cast<ActorBoat *>(actor) != NULL)
			{
				actor->applyActorCollision(this);
			}
		}

		WCoord pos = getPosition();
		for(int i=0; i<4; i++)
		{
			int x = CoordDivBlock(pos.x + (i%2)*80 - 40);
			int z = CoordDivBlock(pos.z + (i/2)*80 - 40);

			for(int j=0; j<2; j++)
			{
				int y = CoordDivBlock(pos.y) + j;
				int blockid = m_pWorld->getBlockID(x, y, z);

				if(blockid == BLOCK_SNOW)
				{
					m_pWorld->setBlockAir(WCoord(x,y,z));
				}
				/*
				else if(blockid == BLOCK_WATER)
				{
					m_pWorld->destroyBlock(x, y, z, true);
				}*/
			}
		}
		auto RidComp = getRiddenComponent();
		if (RidComp)
		{
			for (int i = 0; i < RidComp->getNumRiddenPos(); i++)
			{
				ClientActor *ridden = RidComp->getRiddenByActor(i);

				if (ridden && (ridden->isDead() || ridden->needClear()))
				{
					auto RiddenComp = ridden->getRiddenComponent();
					//if(ridden->m_RidingActor == getObjId())
					if (RiddenComp && RiddenComp->checkRidingByActorObjId(getObjId()))
					{
						RiddenComp->setRidingActor(NULL);
					}

					RidComp->clearRiddenActor(ridden);
				}
			}
		}
		if(getAttrib()->isDead())
		{
			if (RidComp)
			{
				for (int i = 0; i < RidComp->getNumRiddenPos(); i++)
				{
					ClientActor *ridden = RidComp->getRiddenByActor(i);
					if (ridden)
					{
						auto RiddenComp = ridden->getRiddenComponent();
						if (RiddenComp &&/*ridden->m_RidingActor==getObjId()*/RiddenComp->checkRidingByActorObjId(getObjId()))
						{
							RiddenComp->mountActor(NULL);
						}
					}
				}
			}

			/*
			setNeedClear();
			auto dropComponent = GetComponent<DropItemComponent>();
			if (dropComponent)
			{
				dropComponent->dropItem(m_ItemID, 1);
			}*/
		}
	}
}

void ActorBoat::update(float dtime)
{
	ClientActor::update(dtime);

	if(m_Model)
	{
		m_Model->SetPosition(getLocoMotion()->getFramePosition());

		float t = getLocoMotion()->m_TickPosition.m_TickOffsetTime/GAME_TICK_TIME;
		float yaw = Rainbow::Lerp(getLocoMotion()->m_PrevRotateYaw, getLocoMotion()->m_RotateYaw, t);
		float pitch = Rainbow::Lerp(getLocoMotion()->m_PrevRotatePitch, getLocoMotion()->m_RotationPitch, t);

		m_Model->SetRotation(yaw, -pitch, 0.0f);

		m_Model->UpdateTick(TimeToTick(dtime));
	}
}

int ActorBoat::getObjType() const
{
	return OBJ_TYPE_BOAT;
}

void ActorBoat::moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks)
{
	BoatLocomotion *locmove = static_cast<BoatLocomotion *>(getLocoMotion());
	if(m_HostMotivate)
	{
		locmove->boatPosRotationIncrements = interpol_ticks + 5;
	}
	else
	{
		WCoord dpos = pos - locmove->m_Position;
		if(dpos.lengthSquared() <= BLOCK_SIZE*BLOCK_SIZE)
		{
			return;
		}
		locmove->boatPosRotationIncrements = 3;
	}

	locmove->boatPos = pos;
	locmove->boatYaw = yaw;
	locmove->boatPitch = pitch;
}

void ActorBoat::enterWorld(World* pworld)
{
	ClientActor::enterWorld(pworld);
	if (m_pWorld && m_Model)
	{
		if (m_pWorld->getScene())
			m_pWorld->getScene()->AddGameObject(m_Model->GetGameObject());
	}
}

void ActorBoat::leaveWorld(bool keep_inchunk)
{
	ClientActor::leaveWorld(keep_inchunk);
	if (m_Model)
	{
		m_Model->DetachFromScene();
	}
}

void  ActorBoat::AttackFromSharkBite(ClientMob* actor) {
	if (// g_pPlayerCtrl &&
		m_pWorld && !m_pWorld->isRemoteMode())
	{
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", actor->getObjId());
		context << "objid" << objid_str;
		char objid_str2[128];
		RiddenComponent* riddenCom = getRiddenComponent();
		WORLD_ID  actorId = 0;
		if (riddenCom)
		{
			actorId = riddenCom->getRiddenByActorID();
		}
		//WORLD_ID  actorId = getRiddenByActorID();
		sprintf(objid_str2, "%lld", actorId);
		context << "objid2" << objid_str2;
		context << "data" << actor->getData();
		ClientActor* actor = g_WorldMgr->findActorByWID(actorId) ? g_WorldMgr->findActorByWID(actorId)->GetActor() : NULL;
		if (actor != NULL && actor->isPlayer())
		{
			ClientPlayer* cPlayer = dynamic_cast<ClientPlayer*>(actor);
			if (cPlayer)
				SandBoxManager::getSingleton().sendToClient(cPlayer->getUin(), "PB_ACTOR_SHARK_BITE_PLAYER_MOVE", context.bin(), context.binLen());
		}
	}
	else
	{
		setPosition(actor->getPosition());
	}

	if (actor->getData() == 1 && actor->getBody())
	{
#ifndef IWORLD_SERVER_BUILD		
		WCoord bindPos = actor->getBody()->getBindPointPos(206);
#else
		WCoord bindPos = actor->getLocoMotion()->getPosition();
#endif		
		getLocoMotion()->m_Motion.y = 0;
		setPosition(bindPos);//������������
	}
}

void ActorBoat::onDie()
{
	//DieComponent::onDieBase()��Ĭ���߼�
	if (OnHurtByActor() && getWorld() && !getWorld()->isRemoteMode())
	{
		// �۲����¼��ӿ�
		ObserverEvent_Actor obevent((long long)getObjId(), (long long)getBeHurtTargetID());
		obevent.SetData_Actor(getDefID());
		ObserverEventManager::getSingleton().OnTriggerEvent("Actor.Die", &obevent);

		//���Ŀ��
		ClientActor* pActor = getBeHurtTarget();
		if (pActor)
		{
			ObserverEvent_Actor obevent(pActor->getObjId(), getObjId());
			obevent.SetData_Actor(pActor->getDefID());
			if (IsTriggerCreature())
			{
				obevent.SetData_TargetActorID(getObjType());
			}
			ObserverEventManager::getSingleton().OnTriggerEvent("Actor.Beat", &obevent);
		}
	}
}

int ActorBoat::getRiddenAnim()
{
	if (m_ItemID == ITEM_BAMBOO_RAFT) 
		return SEQ_STAND;

	return SEQ_SITDOWN;
}
