#include "PCControl.h"
#include "PlayerControl.h"
#include "ClientActor.h"
#include "GameCamera.h"
#include "CameraModel.h"
#include "VehicleWorld.h"
#include "MinimapRenderer.h"
#include "IGameMode.h"
#include "blocks/special_blockid.h"
#include "GunUseComponent.h"
#include "CustomGunUseComponent.h"
#include "IClientGameManagerInterface.h"
#include "PlayManagerInterface.h"
#include "Input/OgreInputManager.h"
#include "Input/InputManager.h"
#include "UILib/ui_framemgr.h"
#include "UILib/ui_scriptfunc.h"
#include "GameUI.h"
#include "xml_uimgr.h"
#include "CommonUtil.h"
#include "DebugDataMgr.h"
#include "RecordPkgManager.h"
#include "ClientInfoProxy.h"
#include "Graphics/ScreenManager.h"
#include "Console/Console.h"
#include "AdventureGuideMgrProxy.h"

#if (defined BUILD_MINI_EDITOR_APP)
#include "SandboxCfg.h"
#include "SandboxPlayersRoot.h"
#include "SandboxPlayerObject.h"
#endif
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
#include "tchar.h"
#endif

#include "GameSettings.h"
#include "KeyBinding.h"
#include "InputInfo.h"
#include "PlayerInputHelper.h"
#include "SandboxMouseService.h"
#include "UIRenderer.h"
#include "ActorBody.h"
#include "LuaInterfaceProxy.h"
#include "IWorldConfigProxy.h"
#include "WorldManager.h"
#include "OgrePhysXManager.h"
#include "RiddenComponent.h"
#include "ClientActorFuncWrapper.h"
#include "PlayerAttrib.h"
#include "ItemIconManager.h"
#include "ActorVehicleAssemble.h"
#include "VehicleControlInputs.h"
#include "container_driverseat.h"
#include "VehicleWorld.h"
#include "worldMesh/BlockPlacementPreview.h"
#include "display/WorldRender.h"
#include "display/worlddisplay/BlockScene.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;
using namespace Rainbow::UILib;

//IMPLEMENT_GETMETHOD_MANUAL_INIT(PCControl)

const unsigned char MAX_SPEEDUP_TIMES = 4;

#ifdef DEBUG_RENDER
//#include "OgreMaterialManager.h"
#endif

extern bool checkNeedColoreCursor(PlayerControl* pPlayerCtrl, int& interType, IntersectResult& intersectRt, WCoord& outPos, int& outColor);
EXPORT_SANDBOXGAME extern void ColorGunInhale(bool bAimToColorable, int nInterType, WCoord block, ClientActor* actor);

PCControl::PCControl() : m_showOperateUI(false),
m_TipDuration(0),
m_TipTrans(0),
m_AimToColorableObject(false),
m_InterType(0),
m_fBaskeBallFocusX(0),
m_fBaskeBallFocusY(0),
m_iAutoFireState(0), m_iAutoAimTime(0), m_iMaxAimTime(1),
m_SpeedUpTimes(1),
m_CamLerpType(0),
m_MouseSmooth(3)
{
	m_BarDirection = 1;
	m_sensityvity = 3;
	m_isSetSensitivity = false;
	m_reversalY = false;
	m_sightModel = true;
	m_drawSight = true;
	m_isForward = true;
	m_isCanMove = false;
	m_beforeDir = 0;
	m_ControlJumpTime = 0;
	m_ShowUseBtn = false;
	m_GuideTick = -1;
	m_AccumulatorProgress = -1;

	m_LeftMouse_PressTime = 0;
	m_RightMouse_PressTime = 0;
	m_WheelScrollDist = -1;

	//InputManager::GetInstance().getCursorPos(m_MousePos.x, m_MousePos.y);

	//提取公共文件夹后，图集被放到texture0了
	UIRenderer& uiRenderer = UIRenderer::GetInstance();
	m_TouchRes = uiRenderer.CreateTexture("ui/mobile/texture0/ingame.png");
	m_TouchRes2 = uiRenderer.CreateTexture("ui/mobile/texture0/operate.png");
	m_SightingTeleScope = uiRenderer.CreateTexture("ui/mobile/texture0/bigtex/img_sighting_telescope.png");

	m_BaojiUIRes = uiRenderer.CreateTexture("ui/cursor/czjm_baoji.png");
	m_DeadUIRes = uiRenderer.CreateTexture("ui/cursor/czjm_dead.png");
	m_NormalUIRes = uiRenderer.CreateTexture("ui/cursor/czjm_jizhong.png");
	m_ColorableUIRes = uiRenderer.CreateTexture("ui/cursor/czjm_colorable.png");

	m_newGunNormalUIRes = uiRenderer.CreateTexture("ui/cursor/icon_shoot_normal.png");
	m_newGunHeadUIRes = uiRenderer.CreateTexture("ui/cursor/icon_shoot_head.png");
	m_newGunDeathUIRes = uiRenderer.CreateTexture("ui/cursor/icon_shoot_death.png");
	m_newGunCursorUIRes = uiRenderer.CreateTexture("ui/cursor/cursor_newgun.png");

	m_PreMousePos = m_MousePos;

	m_EnbaleNormalshotTip = false;
	m_EnableHeadshotTip = false;
	m_EnbaleDeadshotTip = false;
	m_TipFadeInTime = 50;
	m_TipFadeOutTime = 250;;
	m_TipStartTime = -1;

	m_NeedShowColorableCursor = false;
	m_HasShownColorTips = false;

	m_PreColor = -1;

	m_LetterFrameOpened = false;

	m_BasketBallLockState = false;
	m_BasketBallLockTime = 0;
	m_TriggerTick = 0;

	m_DeveloperPos.SetElement(35, 218);
	m_DeveloperBtnState = false;
	isOpenDeveloperFrame = false;
	m_DeveloperBtnSize = 60;
	isLockAllKey = false;
	this->material_BaojiUI = UIRenderer::GetInstance().CreateInstance();
	this->material_DeadUI = UIRenderer::GetInstance().CreateInstance();
	this->material_Cursor = UIRenderer::GetInstance().CreateInstance();
	this->material_NormalUI = UIRenderer::GetInstance().CreateInstance();
	this->material_TeleScope = UIRenderer::GetInstance().CreateInstance();
	this->material_Touch = UIRenderer::GetInstance().CreateInstance();
	this->material_Touch2 = UIRenderer::GetInstance().CreateInstance();
	this->material_Huires = UIRenderer::GetInstance().CreateInstance();

	material_newGunNormal = UIRenderer::GetInstance().CreateInstance();
	material_newGunHead = UIRenderer::GetInstance().CreateInstance();
	material_newGunDeath = UIRenderer::GetInstance().CreateInstance();

	m_selfUpdateCount = 0;
	m_waitTickRotationCamera = false;
	m_CameraTargetValue = Rainbow::Vector2f::zero;
	m_ShowMiddlePoint = false;
}

PCControl::~PCControl()
{
	for (auto item : mInputControls)
	{
		OGRE_DELETE(item.second);
	}
	mInputControls.clear();
}

bool PCControl::GetButton(UIButtonKey key)
{
	return false;
}

bool PCControl::onInputEvent(const Rainbow::InputEvent& inevent)
{
	
	if (nullptr == g_pPlayerCtrl)
	{
		return false;
	}
	g_pPlayerCtrl->m_InputHelper->triggerPCVehicleControlKeys(inevent);

	//物理机械驾驶模式下，禁用部分键位
	auto RidComp = g_pPlayerCtrl->getRiddenComponent();
	if (RidComp && RidComp->isVehicleController() && KeyBanInVehicleMode(inevent))
	{
		return false;
	}

	if (inevent.type == InputEvent::kKeyDown || inevent.type == InputEvent::kKeyUp)
	{
		//演奏时无法移动、无法攻击、无法使用快捷键打开其他界面
		bool success = false;
		bool ret = false;
		bool succ = MNSandbox::GetGlobalEvent().Emit<bool&, bool&>("Music_CheckisCanMove", success, ret);

		//演奏时根据配置，不允许移动
		if (succ && !success)
		{
			if (inevent.type == InputEvent::kKeyDown)
			{
				//按下esc 出去
				if (SDLK_ESCAPE == inevent.keycode)
				{
					int tmp = GetClientInfoProxy()->getGameData("hideui");
					MINIW::ScriptVM::game()->callFunction("AccelKey_Escape", "b", g_pPlayerCtrl->isDead());
					if (tmp)
					{
						if (GetIClientGameManagerInterface()->getICurGame()->isInGame())
						{
							GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->ResetOperateState();
						}
					}
				}
				//VK_OEM_3
				else if (96 == inevent.keycode)
				{
					//编辑录像不会切换鼠标\准星 bycode:Logo
					if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
					{
						return true;
					}
					int nMode = g_pPlayerCtrl->getPCControl()->isSightMode() ? 1 : 0;
					g_pPlayerCtrl->getPCControl()->setSightModel(!nMode);
					SandboxMouseService::GetSingletonPtr()->setMode(nMode);
					if (GetIClientGameManagerInterface()->getICurGame()->isInGame())
					{
						MINIW::ScriptVM::game()->callFunction("AccelKey_OEM_3", "");
						GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->ResetOperateState();

						bool isGuideState = true;
						MNSandbox::GetGlobalEvent().Emit<bool&, const char*>("ClientAccountMgr_getNoviceGuideState", isGuideState, "guidekey");
						if (!isGuideState)
						{
							MNSandbox::GetGlobalEvent().Emit<const char*, bool>("ClientAccountMgr_setNoviceGuideState", "guidekey", true);
							MINIW::ScriptVM::game()->callFunction("GuideKey_Finish", "");
						}
					}
				}
				else
				{
					SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
						Emit("Music_keyInputCheck", SandboxContext(nullptr).SetData_Number(inevent.keycode));
					if (result.IsExecSuccessed())
					{
						return false;
					}
				}
			}
			else if (inevent.type == InputEvent::kKeyUp)
			{
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
					Emit("Music_keyInputCheck", SandboxContext(nullptr).SetData_Number(-inevent.keycode));
				if (result.IsExecSuccessed())
				{
					return false;
				}
			}
			return false;
		}
		else
		{
			//按下R打开音乐演奏界面
			if (SDLK_r == inevent.keycode)
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_MusicNumber", "ii", SDLK_r, 0);
			}
		}
	}

	for (auto& item : mInputControls)
	{
		if ((item.second)->onInputEvent(inevent))
		{
			return true;
		}
	}

	bool flymode = g_pPlayerCtrl->getFlying();

	int w, h = 0;
	GetClientInfoProxy()->getClientWindowSize(w, h);

	if (inevent.type == InputEvent::kMouseMove
		|| inevent.type == InputEvent::kMouseDrag)
	{
		if (isSightMode() && !MinimapRenderer::GetInstancePtr()->IsEnable() && g_pPlayerCtrl->m_EnableInput)
		{
			//特用于过场模式或冒险强制新手引导控制转向
			if (GetAdventureGuideMgrProxy()->canPlayerTurn())
			{
				bool isLockFPSMouse = GetScreenManager().GetLockCursor() == kCursorConfined;
				bool isNotLockCamera = !isLockCamera();

				auto funcWrapper = g_pPlayerCtrl->getFuncWrapper();
				bool isNoBtreeCtrl = !(funcWrapper ? funcWrapper->getBtreeControl() : false);
				if (isLockFPSMouse && isNotLockCamera && isNoBtreeCtrl && !m_waitTickRotationCamera)
				{
			
				mouseControlCameraRotLocal(inevent);
					//LOG_INFO("mouse dx=%d dy=%d  x=%d y=%d\n", dx, dy, inevent.mouse.x, inevent.mouse.y);
				}
				if (m_waitTickRotationCamera) m_waitTickRotationCamera = false;
			}
		}

		if (MinimapRenderer::GetInstancePtr()->IsEnable())
		{
			int dx = inevent.mousePosition.x - m_PreMousePos.x;
			int dy = inevent.mousePosition.y - m_PreMousePos.y;
			if (!m_KeyMap[SDLK_LBUTTON])
			{
				m_PreMousePos.x = inevent.mousePosition.x;
				m_PreMousePos.y = inevent.mousePosition.y;
			}
			else if ((abs(dx) >= 2 || abs(dy) >= 2))
			{
				g_pPlayerCtrl->m_pCamera->rotate(float(dx) * m_sensityvity * 0.75f * 0.75f / w, float(dy) * m_sensityvity * 0.75f * 0.75f / h);

				m_PreMousePos.x = inevent.mousePosition.x;
				m_PreMousePos.y = inevent.mousePosition.y;
			}
			//LOG_INFO("mouse2 dx=%d dy=%d  x=%d y=%d\n", dx, dy, inevent.mouse.x, inevent.mouse.y);
		}


		CheckColorCursor();

		if (m_DeveloperBtnState)
		{
			float x = inevent.mousePosition.x / UILib::GetScreenUIScaleX();
			float y = inevent.mousePosition.y / UILib::GetScreenUIScaleY();
			const float scale = UILib::GetScreenUIScale();

			const Rainbow::RectInt& safeArea = GetGameUIPtr()->GetSafeArea();
			const int width = safeArea.GetWidth() / UILib::GetScreenUIScaleX();
			const int height = safeArea.GetHeight() / UILib::GetScreenUIScaleY();
			
			int size = m_DeveloperBtnSize / 2;
			if (x < size * scale) x = size * scale;
			if (x > (width - size)) x = (width - size);
			if (y < size * scale) y = size * scale;
			if (y > (height - size)) y = (height - size);
			//限制按钮移动范围
			m_DeveloperPos.SetElement(x, y);

			isOpenDeveloperFrame = false;
		}
	}
	else if (inevent.type == InputEvent::kScrollWheel)
	{
		//坐在物理载具驾驶座上时，屏蔽快捷栏相关操作
		auto RidComp = g_pPlayerCtrl->sureRiddenComponent();
		//禁止操作状态，屏蔽快捷栏
		auto attrib = g_pPlayerCtrl->getPlayerAttrib();
		if (RidComp && !RidComp->isVehicleController() && (!attrib || !attrib->getBuffEffectBankInfo(BUFFATTRT_FORBID_OPERATE)))
		{
			s_WheelDist -= inevent.delta.y;
			if (Abs(s_WheelDist) > 0.5f && !GetIClientGameManagerInterface()->getICurGame()->isOperateUI())
			{
				if (g_pPlayerCtrl->getCurToolID() != ITEM_PAINTTANK)
				{
					if (s_WheelDist > 0)	g_pPlayerCtrl->setCurShortcut(g_pPlayerCtrl->getCurShortcut() - 1);
					else g_pPlayerCtrl->setCurShortcut(g_pPlayerCtrl->getCurShortcut() + 1);
				}
				else
				{
					if (s_WheelDist > 0)	g_pPlayerCtrl->setCurSprayPaint(true);
					else g_pPlayerCtrl->setCurSprayPaint(false);
				}

				s_WheelDist = 0;
			}
		}
	}
	else if (inevent.type == InputEvent::kMouseDown && inevent.button == InputEvent::kLeftButton)
	{
		//开发者悬浮窗开关
		int developerSwitch = GetIWorldConfigProxy()->getGameData("developerfloat");
#if defined(BUILD_MINI_EDITOR_APP)
		developerSwitch = 0;
#endif	//BUILD_MINI_EDITOR_APP
		//是否不在家园场景
		bool isNotHomeLandWorld = true;

		if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
		{
			isNotHomeLandWorld = false;
		}
		//适应屏幕缩放，乘以缩放系数
		int posX = m_DeveloperPos.x * UILib::GetScreenUIScaleX();
		int posY = m_DeveloperPos.y * UILib::GetScreenUIScaleY();
		if (developerSwitch == 1 &&
			IsInDeveloperBtn(
				inevent.mousePosition.x, inevent.mousePosition.y, Point2D(posX, posY), m_DeveloperBtnSize) &&
			isNotHomeLandWorld && GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerMode() &&
			GetWorldManagerPtr()->getRealOwnerUin() == GetWorldManagerPtr()->getWorldOwnerUin())
		{
			if (!MinimapRenderer::GetInstancePtr()->IsEnable() && GetClientInfoProxy()->getMultiPlayer() <= 0)
			{
				isOpenDeveloperFrame = true;
				m_DeveloperBtnState = true;
			}
		}
		m_KeyMap[SDLK_LBUTTON] = true;
		m_KeyDownMap[SDLK_LBUTTON] = true;
		m_KeyDownMarks[SDLK_LBUTTON] = m_selfUpdateCount;

	}
	else if (inevent.type == InputEvent::kMouseUp && inevent.button == InputEvent::kLeftButton)
	{
		if (m_DeveloperBtnState)
		{
			if (isOpenDeveloperFrame)
			{
				MINIW::ScriptVM::game()->callFunction("OpenDeveloperModeSetFrame", "");
			}
			m_DeveloperBtnState = false;
			isOpenDeveloperFrame = false;
		}
		//LOG_INFO("GIE_LBTNUP");
		m_XInputKeyMap[SDLK_LBUTTON] = false;
		m_KeyMap[SDLK_LBUTTON] = false;
		m_KeyUpMap[SDLK_LBUTTON] = true;
		m_KeyUpMarks[SDLK_LBUTTON] = m_selfUpdateCount;
	}
	else if (inevent.type == InputEvent::kMouseDown && inevent.button == InputEvent::kRightButton)
	{
		m_XInputKeyMap[SDLK_RBUTTON] = false;
		m_KeyMap[SDLK_RBUTTON] = true;
		m_KeyDownMap[SDLK_RBUTTON] = true;
		m_KeyDownMarks[SDLK_RBUTTON] = m_selfUpdateCount;
	}
	else if (inevent.type == InputEvent::kMouseUp && inevent.button == InputEvent::kRightButton)
	{
		m_XInputKeyMap[SDLK_RBUTTON] = false;
		m_KeyMap[SDLK_RBUTTON] = false;
		m_KeyUpMap[SDLK_RBUTTON] = true;
		m_KeyUpMarks[SDLK_RBUTTON] = m_selfUpdateCount;
	}
	else if (inevent.type == InputEvent::kMouseDown && inevent.button == InputEvent::kMiddleButton)
	{
		ColorGunInhale(m_AimToColorableObject, m_InterType, m_IntersectResult.block, m_IntersectResult.actor ? m_IntersectResult.actor->GetActor() : nullptr);
		/*if (m_AimToColorableObject && !(g_WorldMgr && g_WorldMgr->isGameMakerRunMode()))
		{

			int color = 0;
			if (m_InterType == 1)
			{
				int blockid = g_pPlayerCtrl->getIWorld()->getBlockID(m_IntersectResult.block);
				int userdata = g_pPlayerCtrl->getIWorld()->getBlockData(m_IntersectResult.block);
				MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", blockid, userdata, &color);
			}
			else if (m_InterType == 2)
			{
				ClientMob* mob = dynamic_cast<ClientMob*>(m_IntersectResult.actor);
				if (mob && mob->getDef()->ID == 3403)
				{
					color = mob->getColor();
				}
			}
			else
			{
				assert(false);
			}
			g_pPlayerCtrl->setSelectedColor(color);
			MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", 0, 0);
		}*/
	}
	else if (inevent.type == InputEvent::kKeyDown && inevent.keycode != 0)
	{
		return onKeyDownEvent(inevent);
	}
	else if (inevent.type == InputEvent::kKeyUp && inevent.keycode != 0)
	{
		return onKeyUpEvent(inevent);
	}
	else if (inevent.type == InputEvent::kLostFocus)
	{
		onLostFocus(inevent);
	}
	
	//SandBoxInputEvent inputEvent;
	//ConvertToPlayerInputEvent(inevent, inputEvent);
	//SandboxEventDispatcherManager::GetGlobalInstance().Emit("onInputEvent", SandboxContext(nullptr).SetData_Userdata("SandBoxInputEvent", "inputEvt", &inputEvent));
	return false;
}



//20210825: 触发器新API长按按键扩展到A-Z  codeby:wangshuai
void PCControl::setTriggerKeys(int vkey, char ktype)
{
	//教育版本 玩法模式地图
	bool flag = false;

	if (vkey == SDLK_SPACE || vkey == SDLK_SHIFT || (SDLK_a <= vkey || vkey <= SDLK_z))
		flag = true;

	if (!flag) return;

	if (ktype == 'D') //新增
	{
		m_Triggerkeys[vkey] = 0;
	}
	else if (ktype == 'U') //删除
	{
		auto iter = m_Triggerkeys.find(vkey);
		if (iter != m_Triggerkeys.end())
		{
			m_Triggerkeys.erase(iter);
		}
	}
}
//C++挖掘进度开关
float posX_tmp = 110.0f;
float posY_tmp = 136.0f;
float posX_tmp_3 = -120.0f;
float posY_tmp_3 = -150.0f;
float posX_tmp_4 = -90.0f;
float posY_tmp_4 = -120.0f;
bool showProLock = false;
void PCControl::renderUI(bool hideUI, SharePtr<Texture2D> cursorHandle)
{
	//编辑录像不会切换鼠标\准星 bycode:Logo
	if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		return;
	}

	float scale = GetScreenUIScale();
	if (hideUI)
	{
		return;
	}
	//自己角色身上需要展示的ui相关
	if (g_pPlayerCtrl)
		g_pPlayerCtrl->renderUI();

	ScreenManager& screenManager = GetScreenManager();
	
	FrameManager* frameManager = GetGameUIPtr()->GetFrameManagerPtr();
	const int screenWidth = screenManager.GetWidth(); //frameManager->GetScreenWidth();
	const int screenHeight = screenManager.GetHeight();// frameManager->GetScreenHeight();
	UIRenderer& display = UIRenderer::GetInstance();
	XMLManager& xmlManager = GetGameUIPtr()->GetXMLManager();

	//关闭新手引导期间人物视角不能转动时的准心
	if (!GetAdventureGuideMgrProxy()->canPlayerTurn()) { return; }

	int toolId = g_pPlayerCtrl->getCurToolID();
	if (isSightMode() && g_pPlayerCtrl && g_pPlayerCtrl->m_EnableInput
		&& (g_pPlayerCtrl->getViewMode() == CAMERA_FPS || g_pPlayerCtrl->getViewMode() == CAMERA_TPS_BACK || g_pPlayerCtrl->getViewMode() == CAMERA_TPS_BACK_2 || g_pPlayerCtrl->getViewMode() == CAMERA_TPS_BACK_SHOULDER || (g_pPlayerCtrl->getViewMode() == CAMERA_CUSTOM_VIEW) && g_pPlayerCtrl->getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_SCREEN_CENTER)
		&& (GetWorldManagerPtr() != NULL) && (!GetWorldManagerPtr()->isCustomGame() || GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SHOW_SIGHT) == 1))
	{
		
		int cursorw = int(32 * scale);
		int x = int(0.5f * screenWidth);
		int y = int(0.5f * screenHeight);

		// 普通准心
		if (g_pPlayerCtrl->getUsingEmitter())//手动发射器
		{
			unsigned int color = 0xffffffff;
			display.BeginDraw(this->material_Cursor, cursorHandle);
			display.StretchRect(
				(float)(x - cursorw / 2),
				(float)(y + (0.8f) * cursorw / 2),
				(float)(cursorw),
				(float)(cursorw), color);
			display.EndDraw();
		}
		else if (g_pPlayerCtrl->m_GunHoleState == GunHoldState::NOGUN)
		{
			unsigned int color = m_NeedShowColorableCursor ? 0x8800FF00 : 0xffffffff;

			if (m_EnableHeadshotTip && toolId != ITEM_COLORED_GUN && toolId != ITEM_COLORED_EGG && toolId != ITEM_COLORED_EGG_SMALL)
			{
				unsigned int c = (m_TipTrans << 24) | (m_TipTrans << 16) | (m_TipTrans << 8) | (m_TipTrans);
				display.BeginDraw(this->material_BaojiUI, m_BaojiUIRes);
				display.StretchRect(
					(float)(x - cursorw),
					(float)(y - cursorw),
					(float)(2 * cursorw),
					(float)(2 * cursorw), c);

				UIRenderer::GetInstance().EndDraw();
			}

			display.BeginDraw(this->material_Cursor, cursorHandle);
			display.StretchRect(
				(float)(x - cursorw / 2),
				(float)(y - cursorw / 2),
				(float)(cursorw),
				(float)(cursorw), color);
			display.EndDraw();
		}
		// 枪械准心
		else if (g_pPlayerCtrl->m_GunHoleState == GunHoldState::GUN)
		{
			int crosshairWidth = (int)(1 * scale);
			if (crosshairWidth == 0) crosshairWidth = 1;

			int crosshairHeight = (int)(15 * scale);
			int spread = (int)(g_pPlayerCtrl->getGunSpread() * scale);

			// 没有开镜的情况下，才显示准心
			if (!g_pPlayerCtrl->getZoom())
			{
				if (g_pPlayerCtrl->GetGunCrosshair() > 0)
				{
					unsigned int color = m_NeedShowColorableCursor ? 0x8800FF00 : 0x88DAE290;
					display.BeginDraw(this->material_Cursor, cursorHandle);
					//right
					display.StretchRect(
						(float)(x + spread),
						(float)(y - crosshairWidth),
						(float)(crosshairHeight),
						(float)(2 * crosshairWidth), color);
					//top
					display.StretchRect(
						(float)(x - crosshairWidth),
						(float)(y + spread),
						(float)(2 * crosshairWidth),
						(float)(crosshairHeight), color);
					//left
					display.StretchRect(
						(float)(x - crosshairHeight - spread),
						(float)(y - crosshairWidth),
						(float)(crosshairHeight),
						(float)(2 * crosshairWidth), color);
					//bottom
					display.StretchRect(
						(float)(x - crosshairWidth),
						(float)(y - spread - crosshairHeight),
						(float)(2 * crosshairWidth),
						(float)(crosshairHeight), color);
					display.EndDraw();
				}
			}
			else
			{
				// 通过动态渲染实现狙击枪开镜效果
				float weight, height;
				float x_off, y_off, delta;

				weight = screenWidth * scale;
				height = screenHeight * scale;
				if (height - screenHeight < weight - screenWidth)
				{
					delta = height - screenHeight;
				}
				else
				{
					delta = weight - screenWidth;
				}
				delta /= scale;

				weight = (screenWidth + Rainbow::Abs(delta)) * scale;
				height = (screenHeight + Rainbow::Abs(delta)) * scale;

				x_off = (weight - screenWidth) / 2;
				y_off = (height - screenHeight) / 2;

				// 通过动态渲染实现狙击枪开镜效果
				display.BeginDraw(this->material_TeleScope, m_SightingTeleScope);
				//display.StretchRect(-x_off, -y_off, weight, height, 0xffffffff);
				display.StretchRect(0, 0, (float)screenWidth, (float)screenHeight, 0xffffffff);
				display.EndDraw();
			}
		}
		// 新枪械的准心
		else if (g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN)
		{
			// 腰射的情况下，才显示准心
			bool isAimState = g_pPlayerCtrl->isAimState();
			auto comp = g_pPlayerCtrl->getCustomGunComponent();
			if ((!isAimState && comp) || CameraModel::IsDebugHandPosOffset())
			{
				int crosshairWidth = (int)(1.0f * scale);
				if (crosshairWidth == 0) crosshairWidth = 1;

				int crosshairHeight = (int)(15.0f * scale);
				int spread = (int)(comp->getCurrentSightSpread() * scale);

				unsigned int color = m_NeedShowColorableCursor ? 0x8800FF00 : 0xffffffff;//0x88DAE290
				display.BeginDraw(this->material_Cursor, m_newGunCursorUIRes);//cursorHandle
				//right
				display.StretchRect(
					(float)(x + spread),
					(float)(y - crosshairWidth),
					(float)(crosshairHeight),
					(float)(2 * crosshairWidth), color);
				//top
				display.StretchRect(
					(float)(x - crosshairWidth),
					(float)(y + spread),
					(float)(2 * crosshairWidth),
					(float)(crosshairHeight), color);
				//left
				display.StretchRect(
					(float)(x - crosshairHeight - spread),
					(float)(y - crosshairWidth),
					(float)(crosshairHeight),
					(float)(2 * crosshairWidth), color);
				//bottom
				display.StretchRect(
					(float)(x - crosshairWidth),
					(float)(y - spread - crosshairHeight),
					(float)(2 * crosshairWidth),
					(float)(crosshairHeight), color);
				display.EndDraw();
			}

			if (m_ShowMiddlePoint)
			{
				display.BeginDraw(this->material_Cursor, cursorHandle);
				display.StretchRect(
					(float)(x - 2 / 2),
					(float)(y - 2 / 2),
					(float)(2),
					(float)(2), 0x8800FF00);
				display.EndDraw();
			}
		}

		if (toolId != ITEM_COLORED_GUN && toolId != ITEM_COLORED_EGG && toolId != ITEM_COLORED_EGG_SMALL && (m_EnableHeadshotTip || m_EnbaleNormalshotTip))
		{
			unsigned int c = (m_TipTrans << 24) | (m_TipTrans << 16) | (m_TipTrans << 8) | (m_TipTrans);
			bool newGun = g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN;
			if (m_EnbaleDeadshotTip)
			{
				display.BeginDraw(newGun ? material_newGunDeath : this->material_DeadUI, newGun ? m_newGunDeathUIRes : m_DeadUIRes);
				display.StretchRect(
					(float)(x - cursorw),
					(float)(y - cursorw),
					(float)(2 * cursorw),
					(float)(2 * cursorw), c);
			}
			else if (m_EnbaleNormalshotTip)
			{
				display.BeginDraw(newGun ? material_newGunNormal : this->material_NormalUI, newGun ? m_newGunNormalUIRes : m_NormalUIRes);
				display.StretchRect(x - 0.75f * cursorw, y - 0.75f * cursorw, 1.5f * cursorw, 1.5f * cursorw, c);
			}
			else
			{
				display.BeginDraw(newGun ? material_newGunHead : this->material_BaojiUI, newGun ? m_newGunHeadUIRes : m_BaojiUIRes);
				display.StretchRect(
					(float)(x - cursorw),
					(float)(y - cursorw),
					(float)(2 * cursorw),
					(float)(2 * cursorw), c);
			}

			UIRenderer::GetInstance().EndDraw();
		}
	}

	display.BeginDraw(this->material_Touch2, m_TouchRes2);
	if (m_AccumulatorProgress >= 0)
	{
		int size = (int)(scale * (52 - m_AccumulatorProgress * 48));
		int x = int(0.5f * screenWidth);
		int y = int(0.5f * screenHeight);

		auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "sight.png");
		if (element)
			display.StretchRect(
				(float)(x - size / 2),
				(float)(y - size / 2),
				(float)(size),
				(float)(size), 0xffffffff, element->x, element->y, element->w, element->h);
		display.EndDraw();
	}

	//篮球锁定目标 蓄力动画
	if (m_BasketBallLockState)
	{
		int size = (int)(scale * (54 - (20 - m_BasketBallLockTime) * 2));
		//int x = int(0.5f * screenWidth);
		//int y = int(0.5f * screenHeight);

		auto bblment = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "sight.png");
		if (bblment)
			display.StretchRect(m_fBaskeBallFocusX - size / 2, m_fBaskeBallFocusY - size / 2, (float)size, (float)size, 0xffffffff, bblment->x, bblment->y, bblment->w, bblment->h);
	}

	if (m_iAutoFireState > 0)
	{
		int x = int(0.5f * screenWidth);
		int y = int(0.5f * screenHeight);
		int size = 28;
		if (m_iAutoFireState == 1)
		{
			size = (int)(scale* (54 - (m_iMaxAimTime - m_iAutoAimTime) * 3));
		}

		auto bblment = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", m_iAutoFireState == 1 ? "aiming.png" : (m_iAutoFireState == 2 ? "firewait.png" : "fire.png"));
		if (bblment)
			display.StretchRect(x - size / 2, y - size / 2, (float)size, (float)size, 0xffffffff, bblment->x, bblment->y, bblment->w, bblment->h);
	}

	display.EndDraw();

	//是否不在家园场景
	bool isNotHomeLandWorld = true;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		isNotHomeLandWorld = false;
	}

	//更新挖掘进度LLTODO
	if (g_pPlayerCtrl)
	{
		float pro = g_pPlayerCtrl->getDigProgress();
		if (pro >= 0)
		{
			auto blockDef = GetDefManagerProxy()->getBlockDef(g_pPlayerCtrl->getDigBlockID());
			unsigned long cor = 0xffffffff;
			float proX = 570;
			float proY = 733;
			float proX_2 = 570;
			float proY_2 = 733;
			float proX_3 = 570;
			float proY_3 = 733;
			float proX_4 = 570;
			float proY_4 = 733;
			auto element_g = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_schedule_black.png");
			if (element_g)
			{
				proX = (float)element_g->x;
				proY = (float)element_g->y;
			}
			bool isRightTool = true;
			int rightToolX = 0;
			int rightToolY = 0;
			bool isExploit = false; //是否显示"开垦中" 

			if (blockDef)
			{
				auto curToolDef = GetDefManagerProxy()->getToolDef(toolId);
				//auto mineToolDef = GetDefManagerProxy()->getToolDef(blockDef->MineTool);

				//auto element_r = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_schedule_white.png");
				//if (element_r)
				{
					int blockid = g_pPlayerCtrl->GetExploitCheckBlockId(true);

					if (!curToolDef/* && element_r*/ && blockDef->MineTool != 0)
					{
						//proX = (float)element_r->x;
						//proY = (float)element_r->y;
						//proX_2 = proX + element_r->w / 2;
						//proY_2 = proY;
						//proX_3 = proX + element_r->w / 2;
						//proY_3 = proY + element_r->w / 2;
						//proX_4 = proX;
						//proY_4 = proY + element_r->w / 2;
						isRightTool = false;
					}
					else if (curToolDef && (curToolDef->Type != blockDef->MineTool || curToolDef->Level < blockDef->ToolLevel))
					{
						if (!(DoByProgress(toolId, blockid) && g_pPlayerCtrl->isExploiting()))//开垦的时候 进度条用的是绿色的 不用显示工具图标 code-by:liwentao
						{
							//proX = (float)element_r->x;
							//proY = (float)element_r->y;
							//proX_2 = proX + element_r->w / 2;
							//proY_2 = proY;
							//proX_3 = proX + element_r->w / 2;
							//proY_3 = proY + element_r->w / 2;
							//proX_4 = proX;
							//proY_4 = proY + element_r->w / 2;
							isRightTool = false;
						}
					}

					if (checkCanExploit(toolId, blockid) && g_pPlayerCtrl->isExploiting())
						isExploit = true;
				}
			}

			bool isShowDigPro = true;
			if (!isExploit)
				isShowDigPro = isShowDigProgress();

			// 家园里拿着锄头 开垦或还原耕地时 显示进度条
			if (!isNotHomeLandWorld && toolId == ITEM_HOMELAND_HOE)
			{
				int blockid = g_pPlayerCtrl->GetExploitCheckBlockId(true);
				if (blockid == BLOCK_DIRT || blockid == BLOCK_GRASS || blockid == BLOCK_FARMLAND)
				{
					isRightTool = true;
					isShowDigPro = true;
					isExploit = blockid == BLOCK_FARMLAND ? false : true;
					if (element_g)
					{
						proX = (float)element_g->x;
						proY = (float)element_g->y;
					}
				}
			}
			g_pPlayerCtrl->setExploiting(isExploit);
			const UIFont& oneUIFontConfig = GetGameUIPtr()->GetFrameManagerPtr()->getUIFontByIndex(5);
			//char tmpbuf[64];

			//不显示进度条的话进度条边上的工具也不显示
			if (!isShowDigPro)
				isRightTool = true;
			g_pPlayerCtrl->setIsRightTool(isRightTool);//设置是否是正确挖掘工具
			//不是正确的工具，设置ID
			if (!isRightTool)
			{
				int toolid = 0;
				//家园回收工具没有配在tool表里，这里单独处理
				SandboxResult homelandret = SandboxEventDispatcherManager::GetGlobalInstance().
					Emit("Homeland_GetRightTool", SandboxContext(nullptr).SetData_Number("type", 1));
				if (homelandret.IsSuccessed())
				{
					toolid = g_pPlayerCtrl->getCurToolID();
				}
				else
				{
					std::map<int, int>& mineToolIcon = GetDefManagerProxy()->getMineToolIcon();
					auto it = mineToolIcon.find(blockDef->MineTool * 100 + blockDef->ToolLevel);
					if (it != mineToolIcon.end())
					{
						toolid = it->second;
					}
				}
				g_pPlayerCtrl->setRightToolId(toolid);
			}
			if (isShowDigPro && showProLock)
			{
				//不是正确的工具，绘制正确工具图标
				if (!isRightTool)
				{
					int toolid = 0;

					//家园回收工具没有配在tool表里，这里单独处理
					SandboxResult homelandret = SandboxEventDispatcherManager::GetGlobalInstance().
						Emit("Homeland_GetRightTool", SandboxContext(nullptr).SetData_Number("type", 1));
					if (homelandret.IsSuccessed())
					{
						toolid = g_pPlayerCtrl->getCurToolID();
					}
					else
					{
						std::map<int, int>& mineToolIcon = GetDefManagerProxy()->getMineToolIcon();
						auto it = mineToolIcon.find(blockDef->MineTool * 100 + blockDef->ToolLevel);
						if (it != mineToolIcon.end())
						{
							toolid = it->second;
						}
					}
					int u, v, w, h, r, g, b;
					auto huires = GetItemIconMgr().getItemIcon(toolid, u, v, w, h, r, g, b);
					if (huires != 0)
					{
						display.BeginDraw(this->material_Huires, huires);
						display.StretchRect((float)rightToolX, (float)rightToolY, 35, 35, 0xffffffff, u, v, w, h);
						display.EndDraw();
					}
				}
			}


		}
		else
		{
			g_pPlayerCtrl->setRightToolId(0);
		}
	}

	// 防御状态下玩家的生物韧性 code-by:liya
	if (g_pPlayerCtrl && g_pPlayerCtrl->IsInDefanceState())
	{
		auto attrib = g_pPlayerCtrl->getPlayerAttrib();
		if (attrib)
		{
			int x = int(0.5f * screenWidth);
			int y = int(0.5f * screenHeight);
			display.BeginDraw(this->material_Touch2, m_TouchRes2);

			int width = (int)(156 * scale);
			int height = (int)(10 * scale);

			int offsetx = width / 2;
			int offsety = height / 2 + 30 * scale;
			// 绘制底板
			auto element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_schedule02.png");
			if (element)
			{
				display.StretchRect(x - offsetx, y - offsety, (float)width, (float)height, 0xffffffff, element->x, element->y, element->w, element->h);
			}

			int toughnessMax = attrib->getToughnessTotalMax();
			int toughness = attrib->getToughnessTotal();

			float per = toughness * 1.0f / toughnessMax;

			// 绘制进度
			element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "img_schedule01.png");
			if (element)
			{
				display.StretchRect(x - offsetx, y - offsety, (float)width * per, (float)height, 0xffffffff, element->x, element->y, element->w * per, element->h);
			}

			width = (int)(30 * scale);
			height = (int)(30 * scale);
			// 绘制图标
			element = xmlManager.requestPackElement("ui/mobile/texture2/operate.xml", "icon_shield.png");
			if (element)
			{
				display.StretchRect(x - offsetx - width - 10 * scale, y - offsety - height / 2 + 5 * scale, (float)width, (float)height, 0xffffffff, element->x, element->y, element->w, element->h);
			}

			display.EndDraw();

			// 剩余韧性百分比
			//const UIFont& oneUIFontConfig = GetGameUIPtr()->GetFrameManagerPtr()->getUIFontByIndex(5);
			//char tmpbuf[64];
			//std::sprintf(tmpbuf, "%d%%", int(per * 100));
			//float top = (float)y + 20 * scale - oneUIFontConfig.height / 2;
			//float left = (float)x - width / 2 - 50 * scale;
			//float bottom = (float)top + oneUIFontConfig.height;
			//float right = (float)left + 45 * scale;
			//Rectf rc(left, top, right - left, bottom - top);
			//display.renderTextRect(oneUIFontConfig.h, FONTSYTLE_NORMAL, tmpbuf, rc, 0, 0, false, ColorRGBA32(255, 255, 255, 255), 1.0f);
		}
	}

	//开发者悬浮窗开关
    static Rainbow::NoFreeFixedString developerfloat("developerfloat");
	int developerSwitch = GetIWorldConfigProxy()->getGameData(developerfloat);
#if defined(BUILD_MINI_EDITOR_APP)
	developerSwitch = 0;
#endif
	//开发者悬浮图标拖动
	if (developerSwitch == 1 && isNotHomeLandWorld && GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerMode() && GetClientInfoProxy()->getMultiPlayer() <= 0
		&& GetWorldManagerPtr()->getRealOwnerUin() == GetWorldManagerPtr()->getWorldOwnerUin())
	{
		display.BeginDraw(this->material_Touch, m_TouchRes, BlendMode::kBlendModeTranslucent, nullptr, 1.0f);
		int size = m_DeveloperBtnSize * scale;
		float x = scale * m_DeveloperPos.x;
		float y = scale * m_DeveloperPos.y;
		auto element = xmlManager.requestPackElement("ui/mobile/texture0/ingame.xml", "btn_develop_tool.png");
		if (element)
			display.StretchRect(x - size / 2, y - size / 2, size, size, 0xffffffff, element->x, element->y, element->w, element->h);

		display.EndDraw();
	}
}

void PCControl::addBeforeDir()
{
	if ((m_beforeDir & 1) && g_pPlayerCtrl)
	{
		g_pPlayerCtrl->m_MoveForward = 1.0f;
		g_pPlayerCtrl->m_MoveRight = 0.0f;
	}
}

bool PCControl::isLockCamera()
{
	if (g_pPlayerCtrl && g_pPlayerCtrl->getOWID() != NEWBIEWORLDID)
		return false;

	int level = GetClientInfoProxy()->getCurGuideLevel();
	int step = GetClientInfoProxy()->getCurGuideStep();

	if (level == 1)
	{
		if (step == 9 || step == 11 || step == 12 || step == 15)
		{
			return true;
		}
	}

	return false;
}

bool PCControl::canOnclick()
{
	if (g_pPlayerCtrl == NULL)
	{
		return false;
	}

	if (g_pPlayerCtrl->getOWID() != NEWBIEWORLDID)
		return true;

	int level = GetClientInfoProxy()->getCurGuideLevel();
	int step = GetClientInfoProxy()->getCurGuideStep();

	if (level == 1)
	{
		if (step != 14)
		{
			return false;
		}
	}

	return true;
}

bool PCControl::canPunch()
{
	if (g_pPlayerCtrl == NULL)
	{
		return false;
	}

	if (g_pPlayerCtrl->getOWID() != NEWBIEWORLDID)
		return true;

	int level = GetClientInfoProxy()->getCurGuideLevel();
	int step = GetClientInfoProxy()->getCurGuideStep();

	if (level == 1)
	{
		if (step != 9 && step != 15)
		{
			return false;
		}
	}

	return true;
}

void PCControl::setAccumulatorState(float progress)
{
	m_AccumulatorProgress = progress;
}

void PCControl::tick()
{
	if (g_pPlayerCtrl == NULL)
	{
		return;
	}

	if (m_GuideTick >= 0)
		m_GuideTick++;
	if (m_sightModel)
	{
		g_pPlayerCtrl->m_CurMouseX = 0.5f;
		g_pPlayerCtrl->m_CurMouseY = 0.5f;
	}


	int opreateNum = 0;

	if (opreateNum >= 1 && m_GuideTick < 0)
		m_GuideTick = 0;

	if (m_GuideTick >= 15)
	{
		m_GuideTick = -1;
		m_KeyMap[SDLK_w] = false;
		m_KeyMap[SDLK_s] = false;
		m_KeyMap[SDLK_a] = false;
		m_KeyMap[SDLK_d] = false;
		g_pPlayerCtrl->checkNewbieWorldProgress(1, GetClientInfoProxy()->getCurGuideStep());
	}

	if (m_BasketBallLockState)
	{
		if (m_BasketBallLockTime > 0)
			m_BasketBallLockTime -= 1;
		else
			m_BasketBallLockTime = 20;
	}

	if (m_iAutoFireState == 1)
	{
		if (m_iAutoAimTime > 0)
			m_iAutoAimTime--;
	}

	if (!m_Triggerkeys.empty())
	{
		auto iter = m_Triggerkeys.begin();
		for (; iter != m_Triggerkeys.end(); iter++)
		{
			if (iter->second >= 5)
			{
				iter->second = 0;
				g_pPlayerCtrl->triggerInputEvent(iter->first, "OnPress");
			}
			else
			{
				iter->second++;
			}
		}
	}
}

void PCControl::update(float dtime)
{
	if (m_CamLerpType == 1)
	{
		UpdateControlCameraRotLocal();
	}

	for (auto iter = m_KeyDownMarks.begin(); iter != m_KeyDownMarks.end(); ++iter)
	{
		if (iter->second != -1 && iter->second != m_selfUpdateCount)
		{
			//LOG_INFO("m_KeyDownMarks REFRESH %d frame %d", iter->first, GetClientInfoProxy()->getFrameCount());
			m_KeyDownMap[iter->first] = false;
			iter->second = -1;
		}
	}

	for (auto iter = m_KeyUpMarks.begin(); iter != m_KeyUpMarks.end(); ++iter)
	{
		if (iter->second != -1 && iter->second != m_selfUpdateCount)
		{
			m_KeyUpMap[iter->first] = false;
			iter->second = -1;
		}
	}

	if (m_EnableHeadshotTip || m_EnbaleNormalshotTip)
	{
		m_TipDuration += (int)(dtime * 1000);
		if (m_TipDuration < m_TipFadeInTime)
		{
			m_TipTrans = Clamp((int)(255.0f * m_TipDuration / m_TipFadeInTime), 0, 255);
		}
		else
		{
			m_TipTrans = Clamp(255 - (int)(255.0f * (m_TipDuration - m_TipFadeInTime) / m_TipFadeOutTime), 0, 255);
		}

		if (m_TipDuration > m_TipFadeInTime + m_TipFadeOutTime)
		{
			m_TipTrans = 0;
			m_EnableHeadshotTip = false;
			m_EnbaleNormalshotTip = false;
			m_EnbaleDeadshotTip = false;
		}
	}

	m_selfUpdateCount++;
}

void PCControl::setSensitivity(int senval)
{
	ConstAtLua* pConstAtLua = GetLuaInterfaceProxy().get_lua_const();
	float sensitivity_coef_pc = 2;
	if (pConstAtLua)
	{
		sensitivity_coef_pc = pConstAtLua->sensitivity_coef_pc;
	}
	m_sensityvity = sensitivity_coef_pc + senval / 50.0f;
	m_isSetSensitivity = true;
	if (g_pPlayerCtrl && g_pPlayerCtrl->getCustomGunComponent())
	{
		g_pPlayerCtrl->getCustomGunComponent()->SetSensitivity();
	}
}

void PCControl::setSensitivity2(float senval)
{
	m_sensityvity = senval;
	m_isSetSensitivity = false;
}

void PCControl::setReversalY(int reval)
{
	m_reversalY = reval != 0;
}

void PCControl::setSightModel(int sightModel)
{
#if (defined BUILD_MINI_EDITOR_APP)
	//工具编辑模式禁止切准心模式
	if (MNSandbox::SandBoxCfg::GetInstancePtr()->getRun() == MNSandbox::RunMode::editor_edit && sightModel)
	{
		return;
	}
#endif

	if (sightModel)
	{
		int w, h = 0;
		GetClientInfoProxy()->getClientWindowSize(w, h);
		Rainbow::SetCursorPos(w / 2, h / 2);
		m_PreMousePos.x = w / 2;
		m_PreMousePos.y = h / 2;

		m_MousePos = m_PreMousePos;
	}
	m_sightModel = sightModel != 0;
	GetGameUIPtr()->ShowCursor(!m_sightModel);
	Rainbow::UILib::UIRenderer::GetInstance().showCursor(!m_sightModel);
}

void PCControl::showOperateUI(bool state)
{
	m_showOperateUI = state;
}
bool PCControl::getOperateUIState()
{
	return m_showOperateUI;
}
void PCControl::GetDpadValueRaw(float& moveForward, float& moveStrafe)
{
	moveForward = GetAxis(0);
	moveStrafe = GetAxis(1);
}

void PCControl::GetDpadValue(float& forward, float& strafe)
{
}

bool PCControl::GetMouseButton(int id)
{
	return false;
}

bool PCControl::GetMouseButtonUp(int id)
{
	return false;
}

bool PCControl::GetMouseButtonDown(int id)
{
	return false;
}
void PCControl::triggerNormalshotTip(bool isDead)
{
	m_EnbaleNormalshotTip = true;
	m_EnbaleDeadshotTip = isDead;
	m_TipTrans = 0;
	m_TipStartTime = Timer::getSystemTick();
	m_TipDuration = 0;
}

void PCControl::triggerHeadshotTip(bool isDead)
{
	m_EnableHeadshotTip = true;
	m_EnbaleDeadshotTip = isDead;
	m_TipTrans = 0;
	m_TipStartTime = Timer::getSystemTick();
	m_TipDuration = 0;
}

float PCControl::GetAxis(int axisId)
{
	//vertical
	if (axisId == 0)
	{
		if (m_KeyMap[SDLK_w])
			return 1;
		if (m_KeyMap[SDLK_s])
		{
			if (m_KeyMap[SDLK_w])
			{
				return 0;
			}
			return -1;
		}
	}
	//horizontal
	else if (axisId == 1)
	{
		if (m_KeyMap[SDLK_d])
			return 1;
		if (m_KeyMap[SDLK_a])
		{
			if (m_KeyMap[SDLK_d])
			{
				return 0;
			}
			return -1;
		}
	}
	return 0;
}

bool PCControl::GetButtonDown(UIButtonKey key)
{
	return false;
}

bool PCControl::GetButtonUp(UIButtonKey key)
{
	return false;
}

bool PCControl::CheckKeyEnable(int keycode)
{
	if (g_pPlayerCtrl == NULL)
	{
		return false;
	}

	if (g_pPlayerCtrl->getOWID() == NEWBIEWORLDID)
	{
		int level = GetClientInfoProxy()->getCurGuideLevel();
		int step = GetClientInfoProxy()->getCurGuideStep();
		if (level == 1)
		{
			switch (keycode)
			{
			case SDLK_w:	//W
				if (step == 2 || step == 3 || step > 6)
				{
					if (step <= 6 && m_GuideTick < 0)
						m_GuideTick = 0;
					return true;
				}
				break;
			case SDLK_s:	//S
				if (step == 2 || step == 4 || step > 6)
				{
					if (step <= 6 && m_GuideTick < 0)
						m_GuideTick = 0;
					return true;
				}
				break;
			case SDLK_a:	//A
				if (step == 2 || step == 5 || step > 6)
				{
					if (step <= 6 && m_GuideTick < 0)
						m_GuideTick = 0;
					return true;
				}
				break;
			case SDLK_d:	//D
				if (step == 2 || step >= 6)
				{
					if (step <= 6 && m_GuideTick < 0)
						m_GuideTick = 0;
					return true;
				}
				break;
			case SDLK_F1:
				return true;
			case SDLK_SPACE:
				return true;
			case SDLK_ESCAPE:
				return true;
			case SDLK_LEFT:	//←左
				return true;
				break;
			case SDLK_RIGHT:	//右
				return true;
				break;
			case SDLK_1:	//1
				if (step == 13)
					return true;
				break;
			case SDLK_b:	//B
				if (step == 19)
					return true;
				break;
			default:
				return false;
			}

			return false;
		}
	}

	return true;
}

bool PCControl::GetKey(int id)
{
	if (!g_pPlayerCtrl->GetAllowInput(InputEvent::kKeyDown, id))
		return false;
	if (id < 0) return false;

	if (id == SDLK_SHIFT)
		return GetKey(SDLK_RSHIFT) || GetKey(SDLK_LSHIFT);
	if (id == SDLK_CONTROL)
		return GetKey(SDLK_RCTRL) || GetKey(SDLK_LCTRL);
	if (id == SDLK_ALT)
		return GetKey(SDLK_RALT) || GetKey(SDLK_LALT);

	return m_KeyMap[id];
}

bool PCControl::GetKeyUp(int key)
{
	if (!g_pPlayerCtrl->GetAllowInput(InputEvent::kKeyUp, key))
		return false;
	if (key < 0) return false;

	if (key == SDLK_SHIFT)
		return GetKeyUp(SDLK_RSHIFT) || GetKeyUp(SDLK_LSHIFT);
	if (key == SDLK_CONTROL)
		return GetKeyUp(SDLK_RCTRL) || GetKeyUp(SDLK_LCTRL);
	if (key == SDLK_ALT)
		return GetKeyUp(SDLK_RALT) || GetKeyUp(SDLK_LALT);

	return m_KeyUpMap[key];
}

bool PCControl::GetKeyDown(int key)
{
	if (!g_pPlayerCtrl->GetAllowInput(InputEvent::kKeyDown, key))
		return false;
	if (key < 0) return false;

	if (key == SDLK_SHIFT)
		return GetKeyDown(SDLK_RSHIFT) || GetKeyDown(SDLK_LSHIFT);
	if (key == SDLK_CONTROL)
		return GetKeyDown(SDLK_RCTRL) || GetKeyDown(SDLK_LCTRL);
	if (key == SDLK_ALT)
		return GetKeyDown(SDLK_RALT) || GetKeyDown(SDLK_LALT);

	std::map<short, bool>::iterator iter = m_KeyDownMap.find(key);
	return (iter != m_KeyDownMap.end() && iter->second);
}

bool PCControl::GetXInputKey(int id) {
	if (!g_pPlayerCtrl->GetAllowInput(InputEvent::kKeyDown, id))
		return false;

	std::map<short, bool>::iterator iter = m_XInputKeyMap.find(id);
	if (iter != m_XInputKeyMap.end()) {
		return m_XInputKeyMap[id];
	}
	else
	{
		return false;
	}
}

void PCControl::ResetKey(int id)
{
	m_KeyMap[id] = false;
}

void PCControl::ResetAllInput()
{
	m_KeyMap.clear();
	m_KeyUpMap.clear();
	m_KeyDownMap.clear();

	m_KeyDownTime.clear();
	m_KeyUpMarks.clear();
	m_KeyDownMarks.clear();

	// 这两个需要成对出现，否则会导致鼠标右键抬起状态无法及时重置
	m_KeyUpMap[SDLK_RBUTTON] = true;
	m_KeyUpMarks[SDLK_RBUTTON] = m_selfUpdateCount;
	isLockAllKey = false;
	m_LockOnceKeyMap.clear();
}

void PCControl::setBasketBallLockState(bool state, float wx, float wy)
{
	m_BasketBallLockState = state;
	if (state)
	{
		m_fBaskeBallFocusX = wx;
		m_fBaskeBallFocusY = wy;
	}
	else
		m_BasketBallLockTime = 0;
}

void PCControl::setAutoFireState(int iState, int iTime)
{
	m_iAutoFireState = iState;
	if (m_iAutoFireState == 1)
	{
		m_iAutoAimTime = iTime;
		m_iMaxAimTime = iTime;
	}
}

bool PCControl::checkIsAiming()
{
	if (m_iAutoFireState == 1 && m_iAutoAimTime > 0)
		return true;

	return false;
}

void PCControl::CheckColorCursor()
{
	if (g_pPlayerCtrl == NULL) return;
	if (g_pPlayerCtrl->getViewMode() != CAMERA_FPS && g_pPlayerCtrl->getViewMode() != CAMERA_TPS_BACK && g_pPlayerCtrl->getViewMode() != CAMERA_TPS_BACK_2 && g_pPlayerCtrl->getViewMode() != CAMERA_TPS_BACK_SHOULDER)
		return;

	int color;
	WCoord pos;
	if (checkNeedColoreCursor(g_pPlayerCtrl, m_InterType, m_IntersectResult, pos, color))
	{
		m_AimToColorableObject = true;
		m_NeedShowColorableCursor = true;

		float dis = g_pPlayerCtrl->GetPlayerControlPosition().distanceTo(pos);
		if (dis < 500.f && (!m_HasShownColorTips || m_PreColor != color)
			&& !(g_WorldMgr && g_WorldMgr->isGameMakerRunMode()))
		{
			m_PreColor = color;
			m_HasShownColorTips = true;
			MINIW::ScriptVM::game()->callFunction("ShowColorTip", "i", color);
		}
	}
	else
	{
		// 重置
		m_HasShownColorTips = false;
		m_AimToColorableObject = false;
		m_NeedShowColorableCursor = false;
	}
}

bool IsCurrentSessionRemoteable()
{
	bool fIsRemoteable = false;
#if (defined(IWORLD_DEV_BUILD) || defined(_DEBUG)) && defined(_WINDOWS)

#define TERMINAL_SERVER_KEY _T("SYSTEM\\CurrentControlSet\\Control\\Terminal Server\\")
#define GLASS_SESSION_ID    _T("GlassSessionId")

	if (GetSystemMetrics(SM_REMOTESESSION))
	{
		fIsRemoteable = TRUE;
	}
	else
	{
		HKEY hRegKey = NULL;
		LONG lResult;

		lResult = RegOpenKeyEx(
			HKEY_LOCAL_MACHINE,
			TERMINAL_SERVER_KEY,
			0, // ulOptions
			KEY_READ,
			&hRegKey
		);

		if (lResult == ERROR_SUCCESS)
		{
			DWORD dwGlassSessionId;
			DWORD cbGlassSessionId = sizeof(dwGlassSessionId);
			DWORD dwType;

			lResult = RegQueryValueEx(
				hRegKey,
				GLASS_SESSION_ID,
				NULL, // lpReserved
				&dwType,
				(BYTE*)&dwGlassSessionId,
				&cbGlassSessionId
			);

			if (lResult == ERROR_SUCCESS)
			{
				DWORD dwCurrentSessionId;

				if (ProcessIdToSessionId(GetCurrentProcessId(), &dwCurrentSessionId))
				{
					fIsRemoteable = (dwCurrentSessionId != dwGlassSessionId);
				}
			}
		}

		if (hRegKey)
		{
			RegCloseKey(hRegKey);
		}
	}

#endif

	return fIsRemoteable;
}



void PCControl::mouseControlCameraRot(const Rainbow::InputEvent& inputEvent)
{
    static Rainbow::NoFreeFixedString isremote("isremote");
	int remoteFlag = GetIWorldConfigProxy()->getGameData(isremote); //非0 就是远程
	bool autoCheckRemoteResult = IsCurrentSessionRemoteable();
	if (remoteFlag || autoCheckRemoteResult)
	{
		mouseControlCameraRotRemote(inputEvent);
	}
	else
	{
		mouseControlCameraRotLocal(inputEvent);
	}
}

void PCControl::mouseControlCameraRotLocal(const Rainbow::InputEvent& inputEvent)
{
	int w, h = 0;
	GetClientInfoProxy()->getClientWindowSize(w, h);
	int dx = inputEvent.mousePosition.x - w / 2.0f;
	int dy = inputEvent.mousePosition.y - h / 2.0f;
	//WarningStringMsg("MousePosition:%f,%f", inputEvent.mousePosition.x, inputEvent.mousePosition.y);
	//if (abs(dx) >= 2 || abs(dy) >= 2)
	{
#ifdef BUILD_MINI_EDITOR_APP
		auto playerNode = GetCurrentPlayersNodeRoot();
		auto player = playerNode ? playerNode->GetLocalPlayer().ToCast<ScenePlayerObject>() : nullptr;
		if (player)
		{
			player->Rotate(float(dx) * m_sensityvity * 0.75f * 0.75f / w, float(dy) * m_sensityvity * 0.75f * 0.75f / h);
		}
#endif //BUILD_MINI_EDITOR_APP
		m_DeltaMouseX += dx;
		m_DeltaMouseY += dy;
		Rainbow::SetCursorPos(w / 2, h / 2);

		if (m_CamLerpType == 0)
		{
			const float magicValue = 0.5625f; //0.36f;//0.75*0.75 = 0.5625 
			float deltaXAngle = float(dx) * magicValue / w * m_sensityvity;
			float deltaYAngle = float(dy) * magicValue / h * m_sensityvity;
			g_pPlayerCtrl->m_pCamera->rotate(deltaXAngle, deltaYAngle);
			g_pPlayerCtrl->resetRecoil();
			if (g_pPlayerCtrl->getCustomGunComponent())
				g_pPlayerCtrl->getCustomGunComponent()->resetRecoil();
		}
		
	}

	
	/*m_DeltaMouseX += inputEvent.mousePosition.x - m_PreMousePos.x;
	m_DeltaMouseY += inputEvent.mousePosition.y - m_PreMousePos.y;
	m_PreMousePos.x = inputEvent.mousePosition.x;
	m_PreMousePos.y = inputEvent.mousePosition.y;*/

}

void PCControl::mouseControlCameraRotRemote(const Rainbow::InputEvent& inputEvent)
{
	const int INVALID = -1;
	static int sLastX = INVALID;
	static int sLastY = INVALID;
	float pMouseX = inputEvent.mousePosition.x;
	float pMouseY = inputEvent.mousePosition.y;
	int w, h = 0;
	GetClientInfoProxy()->getClientWindowSize(w, h);
	if ((inputEvent.mousePosition.x == w / 2) && (inputEvent.mousePosition.y == h / 2)) {
		sLastX = INVALID;
		sLastY = INVALID;
		return;
	}//本机有些地方会设置默认焦点为窗口中心点

	if (sLastX != INVALID)
	{
		bool isEdge = false;
		const int EDGE_DVALUE = 10;
		if (inputEvent.mousePosition.x == w - 1)
		{
			isEdge = true;
			if (sLastX == pMouseX)
			{
				float rate = float(EDGE_DVALUE) * m_sensityvity * 0.75f * 0.75f / w;
				g_pPlayerCtrl->m_pCamera->rotate(rate, 0);
				g_pPlayerCtrl->resetRecoil();
				if (g_pPlayerCtrl->getCustomGunComponent())
					g_pPlayerCtrl->getCustomGunComponent()->resetRecoil();
			}
		}

		if (pMouseX == 0)
		{
			isEdge = true;
			if (sLastX == pMouseX)
			{
				float rate = float(EDGE_DVALUE) * m_sensityvity * 0.75f * 0.75f / w;
				g_pPlayerCtrl->m_pCamera->rotate(-rate, 0);
				g_pPlayerCtrl->resetRecoil();
				if (g_pPlayerCtrl->getCustomGunComponent())
					g_pPlayerCtrl->getCustomGunComponent()->resetRecoil();
			}
		}

		if (pMouseY == h - 1)
		{
			isEdge = true;
			if (sLastY == pMouseY)
			{
				float rate = float(EDGE_DVALUE) * m_sensityvity * 0.75f * 0.75f / w;
				g_pPlayerCtrl->m_pCamera->rotate(0, rate);
				g_pPlayerCtrl->resetRecoil();
				if (g_pPlayerCtrl->getCustomGunComponent())
					g_pPlayerCtrl->getCustomGunComponent()->resetRecoil();
			}
		}

		if (pMouseY == 0)
		{
			isEdge = true;
			if (sLastY == pMouseY)
			{
				float rate = float(EDGE_DVALUE) * m_sensityvity * 0.75f * 0.75f / w;
				g_pPlayerCtrl->m_pCamera->rotate(0, -rate);
				g_pPlayerCtrl->resetRecoil();
				if (g_pPlayerCtrl->getCustomGunComponent())
					g_pPlayerCtrl->getCustomGunComponent()->resetRecoil();
			}
		}


		if (!isEdge)
		{
			int dx = pMouseX - sLastX;
			int dy = pMouseY - sLastY;
			if (abs(dx) >= 2 || abs(dy) >= 2)
			{
				float rotX = float(dx) * m_sensityvity * 0.75f * 0.75f / w;
				float rotY = float(dy) * m_sensityvity * 0.75f * 0.75f / h;
				g_pPlayerCtrl->m_pCamera->rotate(rotX, rotY);
				g_pPlayerCtrl->resetRecoil();
				if (g_pPlayerCtrl->getCustomGunComponent())
					g_pPlayerCtrl->getCustomGunComponent()->resetRecoil();
			}
		}
		else
		{
			//Rainbow::SetCursorPos(w / 2, h / 2);
			sLastX = INVALID;
			sLastY = INVALID;
		}

	}

	sLastX = pMouseX;
	sLastY = pMouseY;
}

void PCControl::UpdateControlCameraRotLocal()
{
	if (g_pPlayerCtrl != nullptr)
	{
		if (m_DeltaMouseX != 0 || m_DeltaMouseY != 0)
		{
			int w, h = 0;
			GetClientInfoProxy()->getClientWindowSize(w, h);

			int dx = m_DeltaMouseX;
			int dy = m_DeltaMouseY;
			const float magicValue = 0.5625f; //0.36f;//0.75*0.75 = 0.5625 
			float deltaXAngle = float(dx) * magicValue / w * m_sensityvity;
			float deltaYAngle = float(dy) * magicValue / h * m_sensityvity;
			m_SmoothStep = m_MouseSmooth;
			m_CameraTargetValue.x += deltaXAngle;
			m_CameraTargetValue.y += deltaYAngle;

			m_DeltaMouseX = 0;
			m_DeltaMouseY = 0;
		}

		if (m_SmoothStep > 0)
		{
			Vector2f moveStep=Lerp(Vector2f::zero,m_CameraTargetValue, 1.0f / m_SmoothStep);
			m_CameraTargetValue = m_CameraTargetValue - moveStep;
			g_pPlayerCtrl->m_pCamera->rotate(moveStep.x, moveStep.y);
			g_pPlayerCtrl->resetRecoil();
			if (g_pPlayerCtrl->getCustomGunComponent())
				g_pPlayerCtrl->getCustomGunComponent()->resetRecoil();
			m_SmoothStep--;
			//WarningStringMsg("move TargetValue:(%f,%f),smooth:%f,%f,m_SmoothStep:%d", m_CameraTargetValue.x, m_CameraTargetValue.y, moveStep.x, moveStep.y, m_SmoothStep);
		}

	}
	
}

inline int getOriKey(int pKey)
{
	static std::map<int, int> sSameTypeKeyCode = { {SDLK_RSHIFT,SDLK_SHIFT},{SDLK_LSHIFT,SDLK_SHIFT },
												   {SDLK_RCTRL,SDLK_CONTROL},{SDLK_LCTRL,SDLK_CONTROL },
												{SDLK_RALT,SDLK_ALT},{SDLK_LALT,SDLK_ALT },
												{SDLK_KP_ENTER,SDLK_RETURN}
	};
	auto iter = sSameTypeKeyCode.find(pKey);
	if (iter != sSameTypeKeyCode.end())
	{
		return iter->second;
	}
	return pKey;
}

bool PCControl::isSameKey(int pKey0, int pKey1)
{
	return getOriKey(pKey0) == getOriKey(pKey1);
}

void PCControl::setAllKeyState(bool b)
{
	isLockAllKey = b;
}


void PCControl::setAppointKeyState(bool b, int vkey)
{
	m_LockOnceKeyMap[vkey] = b;
}

void PCControl::AddInputControl(CInputControl* pControl, int order /*= 0*/)
{
	auto item = mInputControls.find(order);
	if (item == mInputControls.end())
	{
		mInputControls[order] = pControl;
	}
	else
	{
		if (mInputControls[order] != pControl)
		{
			OGRE_DELETE(mInputControls[order]);
			mInputControls[order] = pControl;
		}
	}
}

void PCControl::removeInputControl(CInputControl* pControl)
{
	for (auto& item : mInputControls)
	{
		if (item.second == pControl)
		{
			item.second = nullptr;
			OGRE_DELETE(pControl);
		}
	}
}

void PCControl::removeInputControl(int order)
{
	auto item = mInputControls.find(order);
	if (item != mInputControls.end())
	{
		OGRE_DELETE(item->second);
		mInputControls.erase(item);
	}
}

bool PCControl::IsAllowInput(const Rainbow::InputEvent& inevent)
{
	if (Rainbow::InputEvent::kKeyDown == inevent.type || Rainbow::InputEvent::kKeyUp == inevent.type)
	{
		if (Rainbow::GetConsole()->IsShow())
		{
			return false;
		}
	}
	return true;
}

bool PCControl::onKeyDownEvent(const Rainbow::InputEvent& inevent)
{
	if (!this->IsAllowInput(inevent))
	{
		return false;
	}

	//LOG_INFO("KeyDown %d frame %d", inevent.key.vkey, m_selfUpdateCount);
//LOG_INFO("input KeyDown %d", inevent.keycode);
	MINIW::ScriptVM::game()->callFunction("AccelKeyDownEvent", "i", inevent.keycode);
	if (isLockAllKey)
	{
		return false;
	}
	if (m_LockOnceKeyMap[inevent.keycode])
	{
		return false;
	}

	// 冒险强制引导监听键盘输入
	if (GetAdventureGuideMgrProxy()->isEnterInGuide())
	{
		GetAdventureGuideMgrProxy()->keyDownEvent(inevent);
	}

#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	if (GetIClientGameManagerInterface()->getICurGame() && !GetIClientGameManagerInterface()->getICurGame()->isInSetting())
	{

		if (inevent.modifiers & InputEvent::kControl)
		{
			for (int i = 1; i < 9; i++)
			{
				if (i + SDLK_0 == inevent.keycode)
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_CtrlNumber", "i", i);
				}
			}

		}
		else if(!(inevent.modifiers & InputEvent::kAlt))
		{
			for (int i = 0; i < 8; i++)
			{
				if (GameSettings::GetInstance().keyBindShortcut[i]->getCurrentKeyCode() == inevent.keycode)
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_Number", "i", i+1);
				}
			}

		}

		//添加快捷栏快捷键逻辑 by：Jeff 2022/11/5
		if (inevent.modifiers & InputEvent::kAlt)
		{
			// 冒险强制新手引导控制按键
			if (GetAdventureGuideMgrProxy()->canInput())
			{
				for (int i = 0; i < 10; i++)
				{
					if (i + SDLK_0 == inevent.keycode)
					{
						MINIW::ScriptVM::game()->callFunction("AccelKey_MenuNumber", "i", i + 1);
					}
				}
			}
		}
	}
#endif
	// 按n
	if (SDLK_n == inevent.keycode)
	{
		MINIW::ScriptVM::game()->callFunction("AccelKey_N", "");
	}
	// 按x
	else if (SDLK_x == inevent.keycode)
	{
		// 冒险强制新手引导控制按键
		if (GetAdventureGuideMgrProxy()->canInput())
			MINIW::ScriptVM::game()->callFunction("AccelKey_X", "");
	}
	// 按y
	else if (SDLK_y == inevent.keycode)
	{
		MINIW::ScriptVM::game()->callFunction("AccelKey_Y", "");
		if (!GetIClientGameManagerInterface()->getICurGame()->isInSetting())
		{
			MINIW::ScriptVM::game()->callFunction("AccelKey_ModelLib", "");
		}
	}
	// 按z
	else if (SDLK_z == inevent.keycode)
	{
		MINIW::ScriptVM::game()->callFunction("AccelKey_Z", "");
	}
	//按下U
	else if (SDLK_u == inevent.keycode)
	{

		bool isOuterChecker = GetClientInfoProxy()->IsCurrentUserOuterChecker();
		if (isOuterChecker)
		{
			if (m_SpeedUpTimes >= MAX_SPEEDUP_TIMES)
			{
				m_SpeedUpTimes = 1;
			}
			else
			{
				m_SpeedUpTimes++;
			}

			g_pPlayerCtrl->setSpeedUpTimes(m_SpeedUpTimes);
		}
	}
	//alt
	else if (SDLK_RALT == inevent.keycode || SDLK_LALT == inevent.keycode)
	{
		MINIW::ScriptVM::game()->callFunction("AccelKey_Alt", "");
	}
	//VK_ESCAPE
	else if (SDLK_ESCAPE == inevent.keycode)
	{
		int tmp = GetClientInfoProxy()->getGameData("hideui");
		MINIW::ScriptVM::game()->callFunction("AccelKey_Escape", "b", g_pPlayerCtrl->isDead());
		if (tmp)
		{
			if (GetIClientGameManagerInterface()->getICurGame()->isInGame())
			{
				GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->ResetOperateState();
			}
		}
	}
	// 生物库选择模式下 按R
	else if (SDLK_r == inevent.keycode &&
		g_pPlayerCtrl->getIWorld() &&
		g_WorldMgr->isGameMakerToolMode() &&
		g_WorldMgr->isActorToolMode())
	{
		MINIW::ScriptVM::game()->callFunction("AccelKey_R_LivingTool", "");
	}
	// 建筑预览旋转功能 - 按R键旋转预览方块
	else if (SDLK_r == inevent.keycode)
	{
		// 获取世界渲染器并旋转建筑预览
		if (g_pPlayerCtrl && g_pPlayerCtrl->getWorld())
		{
			World* world = g_pPlayerCtrl->getWorld();
			if (world)
			{
				WorldRenderer* worldRenderer = world->getRender();
				BlockPlacementPreview* preview = worldRenderer->GetBlockPlacementPreview();
				if (preview && preview->IsVisible())
				{
					preview->RotatePreview();
				}
			}
		}
		MINIW::ScriptVM::game()->callFunction("AccelKey_R", "");
	}
	// 白名单玩家穿墙 按Q
	else if (SDLK_q == inevent.keycode)
	{
		//MINIW::ScriptVM::game()->callFunction("AccelKey_Q", "");
	}
	// GM账号夜视功能 快捷键K
	else if (SDLK_k == inevent.keycode)
	{
		MINIW::ScriptVM::game()->callFunction("AccelKey_K", "");
	}

	//可以移动
	if (g_pPlayerCtrl->canControl())
	{
		if (inevent.keycode == GameSettings::GetInstance().keyBindForward->getCurrentKeyCode())
		{
			if (-1 == g_pPlayerCtrl->m_InputInfo->moveForward)
			{
				g_pPlayerCtrl->m_InputInfo->moveForward = 0;
			}
			else
			{
				g_pPlayerCtrl->m_InputInfo->moveForward = 1;
			}
		}
		else if (inevent.keycode == GameSettings::GetInstance().keyBindBack->getCurrentKeyCode())
		{
			if (1 == g_pPlayerCtrl->m_InputInfo->moveForward)
			{
				g_pPlayerCtrl->m_InputInfo->moveForward = 0;
			}
			else
			{
				g_pPlayerCtrl->m_InputInfo->moveForward = -1;
			}
		}
		else if (inevent.keycode == GameSettings::GetInstance().keyBindLeft->getCurrentKeyCode())
		{
			if (1 == g_pPlayerCtrl->m_InputInfo->moveStrafe)
			{
				g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
			}
			else
			{
				g_pPlayerCtrl->m_InputInfo->moveStrafe = -1;
			}
		}
		else if (inevent.keycode == GameSettings::GetInstance().keyBindRight->getCurrentKeyCode())
		{
			if (-1 == g_pPlayerCtrl->m_InputInfo->moveStrafe)
			{
				g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
			}
			else
			{
				g_pPlayerCtrl->m_InputInfo->moveStrafe = 1;
			}
		}
	}

	if (!GetIClientGameManagerInterface()->getICurGame()->isInSetting())
	{
		if (!GetIClientGameManagerInterface()->getICurGame()->isOperateUI() && (SDLK_RSHIFT == inevent.keycode || SDLK_LSHIFT == inevent.keycode))
		{
			g_pPlayerCtrl->dismountActor();
		}

		// 冒险强制新手引导控制按键(过滤工具做的地图)
		auto bStudioMap = GetWorldManagerPtr() ? GetWorldManagerPtr()->isNewSandboxNodeGame() : false;
		if (GetAdventureGuideMgrProxy()->canInput() && !bStudioMap)
		{
			//按G或者重新设置的快捷键
			if (inevent.keycode == GameSettings::GetInstance().keyBindDrop->getCurrentKeyCode())
			{
				g_pPlayerCtrl->m_InputInfo->dropItem = true;
				MINIW::ScriptVM::game()->callFunction("AccelKey_G", "");//minicode里只保留B键和G键，Shift键
			}
			//按下B
			else if (inevent.keycode == GameSettings::GetInstance().keyBindInventory->getCurrentKeyCode())
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Backpack", "");
			}
			//按下V
			else if (inevent.keycode == GameSettings::GetInstance().keyBindReorganize->getCurrentKeyCode())
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_SortBackpack", "");
			}
			//按下tab
			else if (inevent.keycode == GameSettings::GetInstance().keyBindScore->getCurrentKeyCode())
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Battle", "");
			}
			else if (inevent.keycode == GameSettings::GetInstance().keyBindPlayerList->getCurrentKeyCode())
			{
				if (GetClientInfoProxy()->getMultiPlayer() != 0)
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_PlayerList", "");
				}
			}
			else if (isSameKey(inevent.keycode, GameSettings::GetInstance().keyBindChat->getCurrentKeyCode()))
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Chat", "");
			}
			if (SDLK_SLASH == inevent.keycode)
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Chat", "");
			}
			//按下J
			else if (inevent.keycode == GameSettings::GetInstance().keyBindRuleSet->getCurrentKeyCode())
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_RuleSet", "");
			}
			//按下M
			else if (inevent.keycode == GameSettings::GetInstance().keyBindMap->getCurrentKeyCode() && !g_pPlayerCtrl->isDead())
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Map", "b", true);
			}
			//按下L
			else if (inevent.keycode == GameSettings::GetInstance().keyBindAchievement->getCurrentKeyCode())
			{
				//MINIW::ScriptVM::game()->callFunction("AccelKey_Achievement", "");
			}
			//按下O
			else if (inevent.keycode == GameSettings::GetInstance().keyBindStoreSkin->getCurrentKeyCode())
			{
				//MINIW::ScriptVM::game()->callFunction("AccelKey_StoreSkin", "");
			}
			//按下I
			else if (inevent.keycode == GameSettings::GetInstance().keyBindStoreInventory->getCurrentKeyCode())
			{
				//MINIW::ScriptVM::game()->callFunction("AccelKey_StoreInventory", "");
			}
			//按下P
			else if (inevent.keycode == GameSettings::GetInstance().keyBindStoreChargeMoney->getCurrentKeyCode())
			{
				//MINIW::ScriptVM::game()->callFunction("AccelKey_StoreChargeMoney", "");
			}
			//按下F
			else if (inevent.keycode == GameSettings::GetInstance().keyBindFriends->getCurrentKeyCode())
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Friends", "");
			}
			//按下H
			else if (inevent.keycode == GameSettings::GetInstance().keyBindMount->getCurrentKeyCode())
			{
				//MINIW::ScriptVM::game()->callFunction("AccelKey_Mount", "");
			}
			//按下C
			else if (inevent.keycode == GameSettings::GetInstance().keyBindBuffStatus->getCurrentKeyCode())
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_BuffStatus", "");
			}
			//按下T
			else if (inevent.keycode == GameSettings::GetInstance().keyBindDevTool->getCurrentKeyCode())
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_DeveloperModeSet", "");
			}
		}

		// 冒险强制新手引导控制按键
		if (GetAdventureGuideMgrProxy()->canPlayerSneak())
		{
			if (isSameKey(inevent.keycode, GameSettings::GetInstance().keyBindSneak->getCurrentKeyCode()))
			{
				g_pPlayerCtrl->m_InputInfo->sneak = true;
			}
		}
	}

	//VK_OEM_3
	if (SDLK_BACKQUOTE == inevent.keycode)
	{
		//编辑录像不会切换鼠标\准星 bycode:Logo
		if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
		{
			return true;
		}
		g_pPlayerCtrl->getPCControl()->setSightModel(!g_pPlayerCtrl->getPCControl()->isSightMode());
		if (GetIClientGameManagerInterface()->getICurGame()->isInGame())
		{
			MINIW::ScriptVM::game()->callFunction("AccelKey_OEM_3", "");
			GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->ResetOperateState();

			bool isGuideState = true;
			MNSandbox::GetGlobalEvent().Emit<bool&, const char*>("ClientAccountMgr_getNoviceGuideState", isGuideState, "guidekey");
			if (!isGuideState)
			{
				MNSandbox::GetGlobalEvent().Emit<const char*, bool>("ClientAccountMgr_setNoviceGuideState", "guidekey", true);
				MINIW::ScriptVM::game()->callFunction("GuideKey_Finish", "");
			}
		}
	}
	if (GetClientInfoProxy()->getMultiPlayer() > 0)
	{
		if (inevent.keycode == GameSettings::GetInstance().keyBindMicroPhone->getCurrentKeyCode())	//key'T'
		{
			MINIW::ScriptVM::game()->callFunction("AccelKey_MicSwitch", "");
			return true;
		}

		if (inevent.keycode == GameSettings::GetInstance().keyBindVolume->getCurrentKeyCode()) //key'Y'
		{
			MINIW::ScriptVM::game()->callFunction("AccelKey_SpeakerSwitch", "");
			return true;
		}
	}
	//VK_F1
	if (SDLK_F1 == inevent.keycode)
	{
		MINIW::ScriptVM::game()->callFunction("AccelKey_F1", "");

		bool isGuideState = true;
		MNSandbox::GetGlobalEvent().Emit<bool&, const char*>("ClientAccountMgr_getNoviceGuideState", isGuideState, "guidekey");
		if (!isGuideState)
		{
			MNSandbox::GetGlobalEvent().Emit<const char*, bool>("ClientAccountMgr_setNoviceGuideState", "guidekey", true);
			MINIW::ScriptVM::game()->callFunction("GuideKey_Finish", "");
		}
	}
	//VK_F2
	else if (SDLK_F2 == inevent.keycode)
	{
		if (!GetIClientGameManagerInterface()->getICurGame()->isInSetting())
		{
			MINIW::ScriptVM::game()->callFunction("AccelKey_KeyDescription", "");
		}
		MINIW::ScriptVM::game()->callFunction("AccelKey_F2", "");
	}
	//VK_F3
	else if (SDLK_F3 == inevent.keycode)
	{
		MINIW::ScriptVM::game()->callFunction("AccelKey_F3", "");
	}
	//VK_F5
	else if (SDLK_F5 == inevent.keycode)
	{
		//// 在开镜情况下，先关掉镜，再切换人称
		if (g_pPlayerCtrl->getZoom())
		{
			g_pPlayerCtrl->setZoom(false);
		}
		int curcameravalue = 0;
		if (g_WorldMgr->m_RuleMgr)
		{
			curcameravalue = g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_CAMERA);
		}

		//if (!g_pPlayerCtrl->isSittingInStarStationCabin() && !g_pPlayerCtrl->isShapeShift() && (!g_WorldMgr->isGameMakerRunMode() || ((g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_CAMERA) < 3 || 8 == g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_CAMERA)) && !g_WorldMgr->m_RuleMgr->isLockViewMode())))
		if (!g_pPlayerCtrl->isSittingInStarStationCabin() &&
			!g_pPlayerCtrl->isShapeShift() &&
			((!g_WorldMgr->isGameMakerRunMode() && !g_pPlayerCtrl->IsViewLock()) || ((curcameravalue < 3 || 8 == curcameravalue || curcameravalue == 10 || curcameravalue == 11) && g_WorldMgr->m_RuleMgr && !g_WorldMgr->m_RuleMgr->isLockViewMode())))
		{
			//int mode = (g_pPlayerCtrl->m_ViewMode + 1) % g_pPlayerCtrl->getViewModeNum();
			

			//如果是持新枪状态，只能切第一人称和第三人称
			int mode = 0;
			if (GetClientInfoProxy()->GetAppId() != 999)
			//if (g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN)
			{
				if(g_pPlayerCtrl->getViewMode() == CameraControlMode::CAMERA_FPS)
					mode = CameraControlMode::CAMERA_TPS_BACK_SHOULDER/*CAMERA_TPS_BACK*/;
				else
					mode = CameraControlMode::CAMERA_FPS;
			}
			else
			{
				mode = g_pPlayerCtrl->getChangeNextViewMode();
				if (g_pPlayerCtrl->isAttrShapeShift() && mode == CameraControlMode::CAMERA_FPS)
					mode = CameraControlMode::CAMERA_TPS_BACK_2;
			}

			g_pPlayerCtrl->setViewMode(mode);
			if (mode < 4)
			{
				GetClientInfoProxy()->setGameData("view", mode + 1);
			}
		}
		else
		{
			//ge GetGameEventQue().postInfoTips(4897);
			CommonUtil::GetInstance().PostInfoTips(4897);
		}
	}
	//VK_F4
	else if (SDLK_F4 == inevent.keycode)
	{
		//F4:运行切玩法 普通编辑模式不会进去判断
#ifndef _DEBUG
		bool bAltHold = Rainbow::GetInputManager().IsAltHold();
		if (!bAltHold)
		{
			MINIW::ScriptVM::game()->callFunction("SceneEditor_AccelKey_F4_release", "");
		}
#endif
	}

#ifdef IWORLD_DEV_BUILD
	//VK_F6
	//else if (SDLK_F6 == inevent.keycode)
	//{
	//	s_CurPlayerIndex++;
	//	if (s_CurPlayerIndex > 10) s_CurPlayerIndex = 1;
	//
	//	g_pPlayerCtrl->changePlayerModel(ComposePlayerIndex(s_CurPlayerIndex, g_pPlayerCtrl->getBody()->getGeniusLv(), g_pPlayerCtrl->getBody()->getSkinID()));
	//}
	//VK_F7
	else if (SDLK_F7 == inevent.keycode)
	{
#if DEBUG_RENDER
		//用来调试shader的，刷新所有shader,方便游戏内修改生效
		//VK_F7
		MaterialManager::getSingletonPtr()->debugReloadShader();
#endif
		DebugDataMgr::GetInstance().toggleDebugMenu();
	}
#endif
	//VK_F8
	else if (SDLK_F8 == inevent.keycode && GetClientInfoProxy()->GetAppId() == 999)
	{
		GetClientInfoProxy()->setGMMode(true);
		DebugDataMgr::GetInstance().toggleRenderInfo();
		auto pgame = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);
		if (pgame)
		{
			pgame->setOpenCmd(true);
		}
		return true;
	}
	//VK_F9
	else if (SDLK_F9 == inevent.keycode)
	{
		//死亡状态屏蔽
		if (g_pPlayerCtrl && g_pPlayerCtrl->isDead())
		{
			return false;
		}
		MINIW::ScriptVM::game()->callFunction("AccelKey_HideUIToggle", "");
		if (GetIClientGameManagerInterface()->getICurGame()->isInGame())
		{
			GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->ResetOperateState();
		}
	}
	//VK_F10
	else if (SDLK_F10 == inevent.keycode)
	{
		MINIW::ScriptVM::game()->callFunction("AccelKey_ScreenShot", "");
	}
	//VK_NUMPAD1
	else if (SDLK_KP1 == inevent.keycode)
	{
		MINIW::PhysXManager::GetInstance().SwitchChannelEngine();
	}
	//VK_NUMPAD2
	else if (SDLK_KP2 == inevent.keycode)
	{
		MINIW::PhysXManager::GetInstance().SwitchChannelWheel();
	}

	auto RidComp = g_pPlayerCtrl->getRiddenComponent();
	if (RidComp && RidComp->isVehicleController())
	{
		ActorVehicleAssemble* vehicle = g_pPlayerCtrl->getDrivingVehicle();
		if (vehicle)
		{
			if (vehicle->getEngineNum() > 0 && vehicle->hasFuel())
			{
				if (inevent.keycode == g_WorldMgr->m_SurviveGameConfig->vehicleconfig.forward_key)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setAccelKeyPressed(true);
				}
				if (inevent.keycode == g_WorldMgr->m_SurviveGameConfig->vehicleconfig.backward_key)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setBrakeKeyPressed(true);
				}
			}
			else
			{
				g_pPlayerCtrl->m_VehicleControlInputs->setAccelKeyPressed(false);
				g_pPlayerCtrl->m_VehicleControlInputs->setBrakeKeyPressed(false);
			}
			if (vehicle->getSteeringSwitch() == true)
			{
				if (inevent.keycode == g_WorldMgr->m_SurviveGameConfig->vehicleconfig.left_key)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setSteerLeftKeyPressed(true);
				}
				if (inevent.keycode == g_WorldMgr->m_SurviveGameConfig->vehicleconfig.right_key)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setSteerRightKeyPressed(true);
				}
			}

			if (inevent.keycode == g_WorldMgr->m_SurviveGameConfig->vehicleconfig.reset_key)
			{
				vehicle->reset();
			}
		}
	}

	if (CheckKeyEnable(inevent.keycode))
	{
		if (g_pPlayerCtrl->getOWID() == NEWBIEWORLDID)
		{
			if (inevent.keycode == SDLK_SPACE)
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Space", "");
				return false;
			}
			else if (inevent.keycode == SDLK_LEFT)	//←左
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Left", "");
				return false;
			}
			else if (inevent.keycode == SDLK_RIGHT)	//右
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Right", "");
				return false;
			}
		}

		if ((IsColorableItem(g_pPlayerCtrl->getCurToolID()) || g_pPlayerCtrl->getCurToolID() == ITEM_COLORED_GUN || IsDyeableBlock(g_pPlayerCtrl->getCurToolID()))
			&& inevent.keycode == SDLK_r)
		{
			int opt;
			float score, teamnum;

			if (g_WorldMgr)
			{
				g_WorldMgr->getRuleOptionID(GMRULE_SCORE_COLORCHANGE, opt, score);
				g_WorldMgr->getRuleOptionID(GMRULE_TEAMNUM, opt, teamnum);
			}

			if (!(g_WorldMgr && g_WorldMgr->isGameMakerRunMode() && (int)score != 0 && (int)teamnum != 0))
				MINIW::ScriptVM::game()->callFunction("ShowPaletteFrame", "");
			return false;
		}

		if (ITEM_COLOR_BRUSH == g_pPlayerCtrl->getCurToolID() && inevent.keycode == SDLK_r)
		{
			MINIW::ScriptVM::game()->callFunction("ShowPaletteFrame", "");
			return false;
		}

		m_KeyMap[inevent.keycode] = true;
		m_KeyDownMap[inevent.keycode] = true;
		m_KeyDownMarks[inevent.keycode] = m_selfUpdateCount;

		// 键盘按下事件
		this->setTriggerKeys(inevent.keycode, 'D');
		g_pPlayerCtrl->triggerInputEvent(inevent.keycode, "Down");
	}
	return false;
}


bool PCControl::onKeyUpEvent(const Rainbow::InputEvent& inevent)
{
	MINIW::ScriptVM::game()->callFunction("AccelKeyUpEvent", "i", inevent.keycode);
	//LOG_INFO("input KeyUp %d", inevent.keycode);
	if (isLockAllKey)
	{
		return false;
	}
	if (m_LockOnceKeyMap[inevent.keycode])
	{
		return false;
	}

	// 冒险强制引导监听键盘输入
	if (GetAdventureGuideMgrProxy()->isEnterInGuide())
	{
		GetAdventureGuideMgrProxy()->keyUpEvent(inevent);
	}

	if (CheckKeyEnable(inevent.keycode))
	{
		m_KeyMap[inevent.keycode] = false;
		m_KeyUpMap[inevent.keycode] = true;
		m_KeyUpMarks[inevent.keycode] = m_selfUpdateCount;


		// 按键弹起事件
		this->setTriggerKeys(inevent.keycode, 'U');
		g_pPlayerCtrl->triggerInputEvent(inevent.keycode, "Up");
	}
	if (nullptr != g_pPlayerCtrl)
	{
		int W = GameSettings::GetInstance().keyBindForward->getCurrentKeyCode();
		int A = GameSettings::GetInstance().keyBindLeft->getCurrentKeyCode();
		int S = GameSettings::GetInstance().keyBindBack->getCurrentKeyCode();
		int D = GameSettings::GetInstance().keyBindRight->getCurrentKeyCode();

		if (inevent.keycode == W ||
			inevent.keycode == S)
		{
			g_pPlayerCtrl->m_InputInfo->moveForward = 0;
		}
		else if (inevent.keycode == A ||
			inevent.keycode == D)
		{
			g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
		}
		bool bKeyDownW = m_KeyMap[W];// 0XFF00 & eUIKeyState(W);
		bool bKeyDownS = m_KeyMap[S];// 0XFF00 & eUIKeyState(S);
		bool bKeyDownA = m_KeyMap[A];// 0XFF00 & eUIKeyState(A);
		bool bKeyDownD = m_KeyMap[D];// 0XFF00 & eUIKeyState(D);


		if (bKeyDownW && !bKeyDownS)
		{
			g_pPlayerCtrl->m_InputInfo->moveForward = 1;
		}
		else if (!bKeyDownW && bKeyDownS)
		{
			g_pPlayerCtrl->m_InputInfo->moveForward = -1;
		}

		if (bKeyDownA && !bKeyDownD)
		{
			g_pPlayerCtrl->m_InputInfo->moveStrafe = -1;
		}
		else if (!bKeyDownA && bKeyDownD)
		{
			g_pPlayerCtrl->m_InputInfo->moveStrafe = 1;
		}
	}

	//这里判断和onKeyDownEvent一致
	if (!GetIClientGameManagerInterface()->getICurGame()->isInSetting())
	{
		// 冒险强制新手引导控制按键(过滤工具做的地图)
		auto bStudioMap = GetWorldManagerPtr() ? GetWorldManagerPtr()->isNewSandboxNodeGame() : false;
		if (GetAdventureGuideMgrProxy()->canInput() && !bStudioMap)
		{
			//按下M
			if (inevent.keycode == GameSettings::GetInstance().keyBindMap->getCurrentKeyCode() && !g_pPlayerCtrl->isDead())
			{
				MINIW::ScriptVM::game()->callFunction("AccelKey_Map", "b", false);
			}
		}
	}

	auto RidComp = g_pPlayerCtrl->getRiddenComponent();
	if (RidComp && RidComp->isVehicleController())
	{
		ActorVehicleAssemble* vehicle = g_pPlayerCtrl->getDrivingVehicle();
		if (vehicle)
		{
			if (vehicle->getEngineNum() > 0 && vehicle->hasFuel())
			{
				if (inevent.keycode == g_WorldMgr->m_SurviveGameConfig->vehicleconfig.forward_key)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setAccelKeyPressed(false);
				}
				if (inevent.keycode == g_WorldMgr->m_SurviveGameConfig->vehicleconfig.backward_key)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setBrakeKeyPressed(false);
				}
			}
			else
			{
				g_pPlayerCtrl->m_VehicleControlInputs->setAccelKeyPressed(false);
				g_pPlayerCtrl->m_VehicleControlInputs->setBrakeKeyPressed(false);
			}
			if (vehicle->getSteeringSwitch() == true)
			{
				if (inevent.keycode == g_WorldMgr->m_SurviveGameConfig->vehicleconfig.left_key)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setSteerLeftKeyPressed(false);
				}
				if (inevent.keycode == g_WorldMgr->m_SurviveGameConfig->vehicleconfig.right_key)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setSteerRightKeyPressed(false);
				}
			}

			if (inevent.keycode == g_WorldMgr->m_SurviveGameConfig->vehicleconfig.reset_key)
			{
				vehicle->reset();
			}
		}
	}
	//抬起G
	if (inevent.keycode == GameSettings::GetInstance().keyBindDrop->getCurrentKeyCode())
	{
		g_pPlayerCtrl->m_InputInfo->dropItem = false;
	}
	//
	else if (isSameKey(inevent.keycode , GameSettings::GetInstance().keyBindSneak->getCurrentKeyCode()))
	{
		g_pPlayerCtrl->m_InputInfo->sneak = false;
	}
	return true;
}

void PCControl::onLostFocus(const Rainbow::InputEvent& inevent)
{
	//移除世界外就不允许移动
	if (m_DeveloperBtnState)
	{
		m_DeveloperBtnState = false;
		isOpenDeveloperFrame = false;
	}

	int W = GameSettings::GetInstance().keyBindForward->getCurrentKeyCode();
	int A = GameSettings::GetInstance().keyBindLeft->getCurrentKeyCode();
	int S = GameSettings::GetInstance().keyBindBack->getCurrentKeyCode();
	int D = GameSettings::GetInstance().keyBindRight->getCurrentKeyCode();
	m_KeyMap[W] = m_KeyMap[A] = m_KeyMap[S] = m_KeyMap[D] = false;
	m_KeyMap[SDLK_RSHIFT] = m_KeyMap[SDLK_LSHIFT] = false;
	m_KeyUpMap[W] = m_KeyUpMap[A] = m_KeyUpMap[S] = m_KeyUpMap[D] = false;
	m_KeyUpMap[SDLK_RSHIFT] = m_KeyUpMap[SDLK_LSHIFT] = false;

	g_pPlayerCtrl->setStill();

	g_pPlayerCtrl->m_InputInfo->moveForward = 0;
	g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
}

bool PCControl::KeyBanInVehicleMode(const Rainbow::InputEvent& inevent)
{
	if (nullptr == g_pPlayerCtrl)
	{
		return false;
	}

	if (GameSettings::GetInstance().keyBindDrop->getCurrentKeyCode() == inevent.keycode)	//丢弃物品
	{
		return true;
	}
	else if (GameSettings::GetInstance().keyBindMount->getCurrentKeyCode() == inevent.keycode)	//坐骑
	{
		return true;
	}
	else if (SDLK_x == inevent.keycode)//表情
	{
		return true;
	}

	if (m_KeyMap[inevent.keycode] && not m_KeyUpMap[inevent.keycode])
		return false;

	ActorVehicleAssemble* vehicle = g_pPlayerCtrl->getDrivingVehicle();
	WCoord seatpos = vehicle->getRiddenBindSeatPos(g_pPlayerCtrl);
	ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(vehicle->getVehicleWorld()->getContainerMgr()->getContainer(seatpos));
	if (container == NULL)
	{
		return false;
	}
	for (auto iter = container->m_BindKeyData.begin(); iter != container->m_BindKeyData.end(); iter++)
	{
		if (container->ConvertIdtoKeyVal(iter->first) == inevent.keycode)
		{
			return true;
		}
	}

#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	if (GetIClientGameManagerInterface()->getICurGame() && !GetIClientGameManagerInterface()->getICurGame()->isInSetting())
	{
		for (int i = 0; i < 8; i++)
		{
			if (inevent.modifiers & InputEvent::kControl)
			{
				if (i + SDLK_1 == inevent.keycode)
				{
					return true;
				}
			}
			else
			{
				if (GameSettings::GetInstance().keyBindShortcut[i]->getCurrentKeyCode() == inevent.keycode)
				{
					return true;
				}
			}
		}
	}
#endif
	return false;
}

bool PCControl::isShowDigProgress()
{
	if (g_pPlayerCtrl == NULL)
		return true;

	World* pWorld = g_pPlayerCtrl->getIWorld();
	if (pWorld == NULL)
		return true;

	if (pWorld->IsBrokenBlockEnable(g_pPlayerCtrl->getDigBlockID()))
		return true;
	else
		return false;
}

void PCControl::SetDrawSight(bool draw)
{
	m_drawSight = draw;
}
bool PCControl::IsDrawSight()
{
	return m_drawSight;
}
