#ifndef __BLOCK_MULTI_WALL_H__
#define __BLOCK_MULTI_WALL_H__

#include "BlockMaterial.h"
#include "BlockArchitecturalBase.h"

class BlockMultiWall : public ModelBlockMaterial, BlockArchitecturalBase
{
    DECLARE_BLOCKMATERIAL(BlockMultiWall)
public:
    BlockMultiWall();
    virtual ~BlockMultiWall() = default;
    virtual void init(int resid) override;
    virtual int getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
    virtual bool onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount) override;
    virtual bool onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player) override;
    virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, float damage) override;
    virtual void onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho) override;
    virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f);
    virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual void getMultiPhisicMeshVerts(Section* psection, const WCoord& posInSection, dynamic_array<TriangleBlockPhyData>& physDatas);
    virtual void onBlockAdded(World *pworld, const WCoord &blockpos) override;
    virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
    virtual bool canPutOntoPlayer(World *pworld, const WCoord &blockpos, IClientPlayer *player) override;
    virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
    virtual int getPlaceBlockDataByPlayer(World *pworld, IClientPlayer *player) override;
    virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world);
    virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
    virtual int getBlockHP(World* pworld, const WCoord& blockpos) override;
    virtual WorldContainer* getCoreContainer(World* pworld, const WCoord& blockpos) override;
    virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_Architecture; }
private:
    virtual WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata = -1);
	bool isCoreBlock(int blockdata);
    bool isNearWall(int blockdata);
    virtual void initGeomName() override;

private:
    int thick = 30;
    dynamic_array<UInt16> m_physDataIdxs;
    // 四个方向的扩展方块位置定义
    const static int WallExtendPos[4][3][3];
};

#endif//__BLOCK_MULTI_WALL_H__ 