#ifndef __BLOCKPLACEMENTPREVIEW_H__
#define __BLOCKPLACEMENTPREVIEW_H__

#include "BlockDecalMesh.h"
#include "worldMesh/BlockGeom.h"
#include "worldData/world.h"

class EXPORT_SANDBOXENGINE BlockPlacementPreview : public BlockDecalMesh
{
	DECLARE_CLASS(BlockPlacementPreview);
public:
	DECLARE_CREATE_DESTORY(BlockPlacementPreview)
    // Constructor/Destructor
    BlockPlacementPreview();
    virtual ~BlockPlacementPreview();
    
    // Create/destroy methods
    
    // Initialization
    void Initialize(World* world);
    
    // Preview update
    void UpdateFromRaycast(int blockId, const IntersectResult& result);
    void setBlock(World* pworld, const WCoord& grid, int blockId, int blockData);
    // Preview control
    void ShowPreview(bool show);
    bool IsVisible() const { return m_IsVisible; }
    
    // Customization
    void SetValidColor(const Rainbow::ColorRGBAf& color) { m_ValidColor = color; UpdateColor(); }
    void SetInvalidColor(const Rainbow::ColorRGBAf& color) { m_InvalidColor = color; UpdateColor(); }
    
    // Rotation control - 新增旋转功能
    void RotatePreview(); // 旋转90度
    void SetRotation(int rotation); // 设置旋转角度(0,90,180,270)
    int GetRotation() const { return m_Rotation; } // 获取当前旋转角度
    
    // Get preview data - 获取预览数据
    WCoord GetPreviewPos() const { return m_PreviewPos; }
    int GetPreviewBlockData() const { return m_blockData; }
    
    // Internal update methods (public for testing)
    WCoord CalculatePlacementPosition(const WCoord& hitPos, int hitFace, int blockId);
    bool IsValidPlacement(const WCoord& pos, int blockId, long long playerUin, const IntersectResult& result);
    bool CanReplace(const WCoord& pos, int blockId);
    
private:
    // Helper methods
    bool IsReplaceable(int blockId);
    void UpdatePreviewBlock(const WCoord& pos, int blockId,int blockData, bool isValid);
    void UpdateColor();
    
    // Member variables
    World* m_World;
    bool m_IsVisible;
    bool m_IsValidPlacement;
    WCoord m_PreviewPos;
    int m_PreviewBlockId;
    int m_blockData;
    int m_HighlightedBlockId;
    int m_HighlightedBlockFace;
    int m_Rotation; // 当前旋转角度 (0, 90, 180, 270)
    Rainbow::ColorRGBAf m_ValidColor;
    Rainbow::ColorRGBAf m_InvalidColor;
    float m_AlphaValue;
};

#endif // __BLOCKPLACEMENTPREVIEW_H__ 