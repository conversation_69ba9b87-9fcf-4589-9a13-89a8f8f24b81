#include "ClientGameManager.h"
#include "OgrePrerequisites.h"
#include "OgreScriptLuaVM.h"
#include "MiniGameApi.h"
#include "MpGameSurvive.h"
#include "ClientGameRecord.h"
//#include "OgreInputManager.h"
#include "world.h"
#include "Platforms/PlatformInterface.h"
#include "LuaInterfaceProxy.h"
#include "WorldManager.h"

#include "ClientPlayer.h"
#include "MainMenuGame.h"
#include "SimpleLoadingGame.h"
#include "Network/LuaWebSocketMgr.h"
#include "SandboxCoreDriver.h"
#include "ClientGameNetHandler.h"
#include "ClientGameStandaloneServer.h"
#include "Jobs/MainThreadJob.h"
#include "Input/InputManager.h"
#include "ClientInfoProxy.h"
#include "GameModeDef.h"
#include "UIActorBodyMgr.h"
#include "Misc/GameApp.h"
#include "ClientInfoProxy.h"
#include "GameEvent.h"
#include "GameNetManager.h"
#include "SandboxCfg.h"
#include "SandboxGameInput.h"
#ifdef IWORLD_SERVER_BUILD
#include "IWorldConfigProxy.h"
#include "LinuxGameApp.h"
#endif
#include "GameStatic.h"
#include "GameAnalytics.h"
using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

//兼容老接口
ClientGameManager* ClientGameManager::getInstance()
{
	return GetClientGameManagerPtr();
}

ClientGameManager::ClientGameManager():
	m_bDisableChkRoomTick(false),
	m_CurGame(NULL), 
	m_LoadingGame(NULL),
	m_ReloadGameType(NO_RELOAD),
	m_bIsMapSceneLoadFinish(false),
	m_strMapSceneLoadTips("loading..."),
	m_iMapSceneLoadProgress(0),
	m_iMapSceneLoadMaxProgress(100),
	m_modurl(""),
	m_uiurl("")
{
	
}

ClientGameManager::~ClientGameManager()
{
	releaseGameData();
}

void ClientGameManager::createNetHandlerRegister()
{
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher(REGIST_NET_HANDLER_KEY);
}


void ClientGameManager::releaseGameData()
{
	Rainbow::GetInputManagerPtr()->UnregisterInputHandler(m_CurGame);

	#ifndef IWORLD_SERVER_BUILD
		m_CurGame = NULL;
		std::map<std::string, ClientGame *>::iterator iter = m_Games.begin();
		for(; iter!=m_Games.end(); iter++)
		{
			ENG_DELETE(iter->second);
		}	
		m_Games.clear();
	#else
		 ENG_DELETE(m_CurGame)
	     Rainbow::GetInputManagerPtr()->UnregisterInputHandler(m_LoadingGame);
	     ENG_DELETE(m_LoadingGame)
	#endif
}

void ClientGameManager::applayGameSetData(bool viewchange/* =false */)
{
	if(m_CurGame) m_CurGame->applyGameSetData(viewchange);
}

void ClientGameManager::applyScreenBright()
{
	if(m_CurGame) m_CurGame->applyScreenBright();
}

void ClientGameManager::applyGameSetData(bool viewchange/* =false */)
{
	applayGameSetData(viewchange);
}

void ClientGameManager::gotoGame(const char *name,GAME_RELOAD_TYPE reloadtype /* = NO_RELOAD */)
{
	// 20210901: 清理数据缓存 codeby:liusijia
	GetDefManagerProxy()->clearTempBlockDef();

	ScriptVM* scriptVM = ScriptVM::game();
	//LOG_INFO("gotoGame(): start [%s]", name);
	ClientGame *game = NULL;
	m_ReloadGameType = reloadtype;
	#ifdef IWORLD_SERVER_BUILD
		if(name==NULL || strcmp(name, "none") == 0)
		{
			if(m_LoadingGame)
			{
				Rainbow::GetInputManagerPtr()->UnregisterInputHandler(m_LoadingGame);
				ENG_DELETE(m_LoadingGame);
			}
			if(m_CurGame)
			{
				ENG_DELETE(m_CurGame);
			}
			return;
		}
		else if (name == NULL || strcmp(name, "exit") == 0) {
			// g_bGameQuit = true;
#ifndef IWORLD_SERVER_BUILD			
			Rainbow::GetGameApp().AppExit();
#else			
			g_bGameQuit = true;
#endif			
			return;
		}

		if(!m_LoadingGame)
		{
			LOG_INFO("gotoGame(): if(!m_LoadingGame)");
			//if (m_CurGame)
			//clearCurGame();
			game = ENG_NEW(StandaloneServer);
			m_LoadingGame = game;
			m_LoadingGame->load();
		}
	#else
		if(name==NULL || strcmp(name, "none") == 0)
		{
			if(getLoadingGame())
			{
				Rainbow::GetInputManagerPtr()->UnregisterInputHandler(m_LoadingGame);
				if (!removeGame(m_LoadingGame))
				{
					ENG_DELETE(m_LoadingGame);
				}
			}
			if (scriptVM && m_CurGame){
				scriptVM->setUserTypePointer("ClientCurGame", m_CurGame->getTypeName(), NULL);
			}
			if(m_CurGame) {
				clearCurGame();
			}
			return;
		}
		std::map<std::string, ClientGame *>::iterator iter = m_Games.find(std::string(name));
		//LOG_INFO("gotoGame(): &iter = %d", iter);
		if(iter == m_Games.end())
		{
			//LOG_INFO("gotoGame(): iter == m_Games.end()");
			//LOG_INFO("gotoGame(): name = %s", name);
			if (strcmp(name, "MainMenuStage") == 0)
			{
				game = ENG_NEW(MainMenuStage);
			}
			else if (strcmp(name, "SurviveGame") == 0)
			{
				game = ENG_NEW(SurviveGame);				
			}
			else if (strcmp(name, "MPSurviveGame") == 0) 
			{
				game = ENG_NEW(MpGameSurvive)(m_ReloadGameType == MULTI_RELOAD); 
			}
			else if (strcmp(name, "SimpleLoadingStage") == 0)
			{
				game = ENG_NEW(SimpleLoadingStage);
			}
			else if (strcmp(name, "GameSurviveRecord") == 0)
			{
				game = ENG_NEW(GameSurviveRecord);
			}
		
			else
			{
				assert(0);
				return;
			}
			//m_CurGame = game;
			m_Games[std::string(name)] = game;
		}
		else
		{
		    game = iter->second;
			if (m_CurGame == game)
			{
				return;
			}
		}
		setLoadingGame(game);
		getLoadingGame()->load();
	#endif
}

void ClientGameManager::addGame(const char *name, ClientGame *game)
{
	m_Games[std::string(name)] = game;
}


bool ClientGameManager::isInGame(){

#ifdef IWORLD_SERVER_BUILD
	//if (GetClientInfoProxy()->isRentServerMode())
	{
		return true;
	}
#endif

	std::map<std::string, ClientGame *>::iterator iter = m_Games.find("MainMenuStage");
	if (iter == m_Games.end()){
		return false;
	}
	return getLoadingGame() != iter->second;
}

static void roomTick(ClientGame* curGame)
{
	if (!Rainbow::CurrentThread::IsMainThread())
	{
//		ErrorStringMsg("roomTick not in main thread");
		return;
	}

	if(GetGameNetManagerPtr() && curGame && curGame->isInGame())
	{
//		ErrorStringMsg("roomTick in Game");
		GetGameNetManagerPtr()->tickConnect();
		GetLuaWebSocketMgr().tick();
		curGame->keepGame();
	}
}

void ClientGameManager::chkRoomTicks()
{
	if(m_bDisableChkRoomTick) return;

	// 必须在线程调用 Lua 函数
//	GetMainThreadJob().ScheduleMainThreadJob(roomTick, this->getCurGame());
	roomTick(this->getCurGame());
}

void ClientGameManager::disableChkRoomTicks(bool disabled)
{
	//LOG_INFO("%d",disabled ? 1 : 0);
	m_bDisableChkRoomTick = disabled;
}

IClientGameInterface* ClientGameManager::getICurGame(GameType type)
{
	IClientGameInterface* game = nullptr;
	switch (type)
	{
	case GameType::DefaultGame:
		game = m_CurGame;
		break;
	case GameType::MainMenuStage:
		game = dynamic_cast<MainMenuStage*>(m_CurGame);
		break;
	case GameType::SurviveGame:
		game = dynamic_cast<SurviveGame*>(m_CurGame);
		break;
	case GameType::MpGameSurvive:
		game = dynamic_cast<MpGameSurvive*>(m_CurGame);
		break;
	case GameType::GameSurviveRecord:
		game = dynamic_cast<GameSurviveRecord*>(m_CurGame);
		break;
	case GameType::SimpleLoadingStage:
		game = dynamic_cast<SimpleLoadingStage*>(m_CurGame);
		break;
#ifdef DEDICATED_SERVER
	case GameType::StandaloneServer:
		game = dynamic_cast<StandaloneServer*>(m_CurGame);
		break;
#endif
	default:
		break;
	}
	return (IClientGameInterface*)game;
}

IClientGameInterface* ClientGameManager::getILoadingGame()
{
	return (IClientGameInterface*)m_LoadingGame;
}

ClientGame *ClientGameManager::getCurGame()
{
	return m_CurGame;
}

void ClientGameManager::setCurGame(ClientGame* game)
{
	m_CurGame = game;
	if (m_CurGame)
	{
		Rainbow::GetInputManager().RegisterInputHandler(m_CurGame);
	}
	SANDBOX_LOG("set cur game. game name = ", game->getName());
	game->OnLoaded();
}

ClientGame *ClientGameManager::getLoadingGame()
{
	return m_LoadingGame;
}

void ClientGameManager::setLoadingGame(ClientGame* game)
{
	if (!game)
	{
		Rainbow::GetInputManager().UnregisterInputHandler(m_LoadingGame);
	}

	m_LoadingGame = game;
	if (m_LoadingGame)
	{
		Rainbow::GetInputManager().RegisterInputHandler(m_LoadingGame);
	}
}

IClientGameInterface* ClientGameManager::getIGame(const char* name)
{
	return (IClientGameInterface*)getGame(name);
}

ClientGame* ClientGameManager::getGame(const char *name)
{
	std::map<std::string, ClientGame *>::iterator iter = m_Games.find(std::string(name));
	if(iter != m_Games.end()) return iter->second;
	return nullptr;
}

MpGameSurvive* ClientGameManager::getMPGame()
{
	std::string gameName = "MPSurviveGame";
	if (g_WorldMgr && g_WorldMgr->isUGCEditMode())
	{
		gameName = "UGCMpGame";
	}
	return static_cast<MpGameSurvive *>(getGame(gameName.c_str()));
}

void ClientGameManager::clearCurGame()
{
	ENTRYMAPCOST_STEP("Begin ClientGameManager::clearCurGame");
	if (!m_CurGame) return;

	char funcname[256];
	m_CurGame->m_hasBegan = false;
	m_CurGame->endGame(m_ReloadGameType>0);
	ScriptVM* scriptVM = ScriptVM::game();

	if(m_ReloadGameType == NO_RELOAD)
	{
		if (strcmp(m_CurGame->getName(), "SurviveGame") == 0 ||
			strcmp(m_CurGame->getName(), "MPSurviveGame") == 0)
		{
			ScriptVM::game()->callFunction("reportGameExitNativeCall",NULL);
		}
		sprintf(funcname, "%s_Quit", m_CurGame->getName());
		if(scriptVM) scriptVM->callFunction(funcname, "");
	}
	if(Rainbow::GetInputManagerPtr()) Rainbow::GetInputManagerPtr()->UnregisterInputHandler(m_CurGame);
	m_CurGame->unload(m_ReloadGameType);
	GET_SUB_SYSTEM(UIActorBodyMgr)->releaseActorBody();
	removeGame(m_CurGame);
	m_CurGame = NULL;

	ENTRYMAPCOST_STEP("End ClientGameManager::clearCurGame");
	ENTRYMAPCOST_FINISH(); // 结束统计
}

void ClientGameManager::updateLoadingGame()
{
	/*if(m_CurGame && m_ReloadGameType>0)
	{
	m_CurGame->endGame();

	if(inputMgr) inputMgr->UnregisterInputHandler(m_CurGame);
	m_CSMgr->removeMsgHandler(m_CurGame);
	m_CurGame->unload();
	removeGame(m_CurGame);

	m_CurGame = NULL;

	setRenderCamera(NULL);
	setRenderContent(NULL);
	}*/
	
	
	ScriptVM* scriptVM = ScriptVM::game();
	Rainbow::InputManager* inputMgr = Rainbow::GetInputManagerPtr();
#ifndef IWORLD_SERVER_BUILD
	if(getLoadingGame() == NULL) return;
	int loadstatus = getLoadingGame()->updateLoad();

	if(loadstatus == 0) return;
	else if(loadstatus < 0)
	{ 
		LOG_INFO("updateLoadingGame0");
		GetClientInfoProxy()->setRenderCamera(NULL);
		GetClientInfoProxy()->setRenderContent(NULL);
		getLoadingGame()->unload();
		removeGame(getLoadingGame());
		setLoadingGame(nullptr);
		GetGameEventQue().postLoadProgress(1000, -1);
		if (m_CurGame)
			m_CurGame->reloadScene();
		LOG_INFO("updateLoadingGame1");
		return;
	}
	if(m_CurGame)
	{
		char gametypename[256];
		sprintf(gametypename, "%s", m_CurGame->getTypeName());
		clearCurGame();
		if(scriptVM != NULL) 
			scriptVM->setUserTypePointer("ClientCurGame", gametypename, NULL);
	}
	m_CurGame = getLoadingGame();
	if (m_CurGame->isInGame())
	{
		GetClientInfoProxy()->setSandboxEngineState(5);//todo huangfubin (GAME_IN_GAME);
	}
	else
	{
		GetClientInfoProxy()->setSandboxEngineState(3);//(GAME_WAITING);
	}
#else
	if (m_LoadingGame == NULL) 
		return;
	else
	{
		if(m_CurGame)
		{
			char gametypename[256];
			sprintf(gametypename, "%s", m_CurGame->getTypeName());
			clearCurGame();
			if(scriptVM != NULL) scriptVM->setUserTypePointer("ClientCurGame", gametypename, NULL);
		}
		//m_LoadingGame->load(this);
	}
	m_CurGame = m_LoadingGame;
	m_CurGame->updateLoad();
	m_CurGame->updateLoad();
#endif
	setLoadingGame(nullptr);

	if(inputMgr != NULL && m_CurGame != NULL){
		inputMgr->RegisterInputHandler(m_CurGame);
	    LOG_INFO("updateLoadingGame  inputMgr->RegisterInputHandler:%p",m_CurGame);
	}
	if(scriptVM != NULL && m_CurGame != NULL) {
		scriptVM->setUserTypePointer("ClientCurGame", m_CurGame->getTypeName(), m_CurGame);
		LOG_INFO("updateLoadingGame setUserTypePointer ClientCurGame:%s",m_CurGame->getTypeName());
	}
	if(m_CurGame)
	{
		m_CurGame->applyGameSetData();
		LOG_INFO("updateLoadingGame applayGameSetData");
		m_CurGame->beginGame();
		LOG_INFO("updateLoadingGame beginGame end");
		
		m_CurGame->m_hasBegan = true;
		if (strcmp(m_CurGame->getName(), "MainMenuStage") != 0)
		{
			//
			if (inputMgr && !inputMgr->GetEatKeyPressOnTextFieldFocus() && GetClientInfoProxy()->isPC() && ScriptVM::game())
			{
				ScriptVM::game()->callFunction("OnWindowLostFocus", "");
			}
		}


		char funcname[256];
#ifndef IWORLD_SERVER_BUILD
		if(m_ReloadGameType == NO_RELOAD || strcmp(m_CurGame->getTypeName(), "MainMenuStage") != 0)
		{
			sprintf(funcname, "%s_Enter", m_CurGame->getName());
			scriptVM->callFunction(funcname, "");
		}
#else
		if(m_ReloadGameType == NO_RELOAD || strcmp(m_CurGame->getTypeName(), "MainMenuStage") != 0)
		{
			sprintf(funcname, "%s_Enter", m_CurGame->getName());
			if(scriptVM) scriptVM->callFunction(funcname, "");
		}
#endif
		//LOG_INFO("updateloadingGame end");

		if(m_ReloadGameType != NO_RELOAD  && strcmp(m_CurGame->getName(), "MainMenuStage") == 0)
		{
			if (m_ReloadGameType == MULTI_RELOAD)
			{
				if (GetClientInfoProxy()->getMultiPlayer() == GAME_NET_MP_GAME_CLIENT)
				{
					//GetClientAccountMgr().m_CurWorldID = 0;
					//GetClientAccountMgr().m_nSpecialType = NORMAL_WORLD;
					MNSandbox::GetGlobalEvent().Emit<>("WorldArchiveMgr_resetCurWorld");
				}
				gotoGame("MPSurviveGame", m_ReloadGameType);
			}
			else if (m_ReloadGameType == SINGLE_RELOAD)
			{
				gotoGame("SurviveGame", m_ReloadGameType);
			}
		}
	}
}

bool ClientGameManager::removeGame(ClientGame *game)
{
	if(!game->isInGame()) return false;

	std::map<std::string, ClientGame *>::iterator iter = m_Games.begin();
	for(; iter!=m_Games.end(); iter++)
	{
		if(iter->second == game)
		{
			ENG_DELETE(game);
			m_Games.erase(iter);
			return true;
		}
	}
	return false;
}

void ClientGameManager::onStop()
{
	if (m_CurGame)
	{
		/*if (m_CurGame->isInGame()) { //gameStop中的房主踢出放在lua调用的话又多线程调用冲突导致lua挂掉，直接放这里做了。
			int gametype = GetClientInfoProxy()->getMultiPlayer();
			if (gametype == 1 || gametype == 3) {
				MNSandbox::GetGlobalEvent().Emit<int, int>("RoomManager_sendToClientKickInfo", 3,0);
			}
		}*/
		m_CurGame->pauseGame();
	}
}

void ClientGameManager::RegisterInputHandler()
{
	Rainbow::InputManager* inputMgr = Rainbow::GetInputManagerPtr();
	if(inputMgr != NULL && m_CurGame != NULL){
		inputMgr->RegisterInputHandler(m_CurGame);
		LOG_INFO("updateLoadingGame  inputMgr->RegisterInputHandler:%p",m_CurGame);
	}
	if (MNSandbox::GameInput::GetSingletonPtr())
		MNSandbox::GameInput::GetSingleton().BindInput();
}
void ClientGameManager::CheckWindowLostFocus()
{
	Rainbow::InputManager* inputMgr = Rainbow::GetInputManagerPtr();
	if (strcmp(m_CurGame->getName(), "MainMenuStage") != 0)
	{
		if (inputMgr && !inputMgr->IsInputActive() && GetClientInfoProxy()->isPC() && ScriptVM::game())
		{
			ScriptVM::game()->callFunction("OnWindowLostFocus", "");
		}
	}
}


bool ClientGameManager::isJudgeOrSpectator(int uin, int type /* = 0*/)
{
	if(getCurGame() && getCurGame()->isInGame() && GetWorldManagerPtr())
	{
		
		if (GetWorldManagerPtr()->getPlayerByUin(uin) == NULL)
		{
			return false;
		}
		ClientPlayer* player = GetWorldManagerPtr()->getPlayerByUin(uin)->GetPlayer();
		// 裁判模式下 判断裁判
		if (0 == type || 1 == type)
		{
			if (JUDGE_TEAM_ID == player->getTeam())
			{
				return true;
			}
		}
		else if (0 == type || 2 == type)
		{
			if (player->isInSpectatorMode())
			{
				return true;
			}
		}
	}
	return false;
}

void ClientGameManager::setCMURL(std::string cmurl, std::string mapmd5, long long mapID)
{
	m_cmurl = cmurl;
	m_mapmd5 = mapmd5;
	m_mapID = mapID;
}

bool ClientGameManager::getCMURL(std::string & cmurl, std::string & mapmd5, long long & mapID)
{
	if (m_cmurl == "" || m_cmurl == "null")
	{
		return false;
	}

	cmurl = m_cmurl;
	mapmd5 = m_mapmd5;
	mapID = m_mapID;


	return true;
}

MINIW::GameStatic<ClientGameManager> s_ClientGameManager(MINIW::kInitManual,101);
ClientGameManager& GetClientGameManager()
{
	return *s_ClientGameManager;
}
ClientGameManager* GetClientGameManagerPtr()
{
	return s_ClientGameManager.EnsureInitialized();
}

void ClientGameManager::mapSceneLoadFinish()
{
	m_bIsMapSceneLoadFinish = true;
	// 2022年5月5日11:00:06 现在新增状态管理类，需要更新
	SandBoxCfg::GetInstancePtr()->setLoadingMap(LoadingMapStatus::loaded);
}

bool ClientGameManager::isMapSceneLoadFinish()
{
	return m_bIsMapSceneLoadFinish;
}

#ifdef BUILD_MINI_EDITOR_APP
void ClientGameManager::setMapSceneLoadFinish(bool status)
{
	m_bIsMapSceneLoadFinish = status;
	if (status)
	{
		SandBoxCfg::GetInstancePtr()->setLoadingMap(LoadingMapStatus::loaded);
	}
}
#endif //BUILD_MINI_EDITOR_APP

void ClientGameManager::setMapSceneLoadTips(std::string tips)
{
	m_strMapSceneLoadTips = tips;
}

void ClientGameManager::setMapSceneLoadProgress(int progress, int max)
{
	m_iMapSceneLoadProgress = progress;
	m_iMapSceneLoadMaxProgress = max;
}

std::string ClientGameManager::getMapSceneLoadTips()
{
	return m_strMapSceneLoadTips;
}

int ClientGameManager::getMapSceneLoadProgress()
{
	return m_iMapSceneLoadProgress;
}

int ClientGameManager::getMapSceneLoadMaxProgress()
{
	return m_iMapSceneLoadMaxProgress;
}

void ClientGameManager::setModURL(std::string modurl, std::string uiurl)
{
	m_modurl = modurl;
	m_uiurl = uiurl;
}

std::string ClientGameManager::getModURL() const
{
	return m_modurl;
}

std::string ClientGameManager::getUIURL() const
{
	return m_uiurl;
}
