#ifndef __PLAYERCONTROL_H__
#define __PLAYERCONTROL_H__

#include "IPlayerControl.h"
#include "ClientPlayer.h"
#include "GameMode_CameraConfig.h"
#include "PlayerInputHandler.h"
#include "IWorldConfigProxy.h"
#include "SandboxEventObjectManager.h"
#include "SandboxGame.h"
#include "IControlInterface.h"

//using namespace Rainbow;
namespace fairygui
{
	class GComponent;
}

namespace Rainbow {
	struct InputEvent;
}

class GameCamera;
class BlockOperateMgr;
class TouchControl;
class PCControl;
class PlayerAnimation;
struct InputInfo;
class PlayerInputHelper;
class ActorRocket;
class VehicleControlInputs;
class OpenContainerComponent;
class ItemSkillComponent;
struct  SandBoxInputEvent;
class DealMusicClub;
class PlayerViewMask;
class BlockScene;
class IBackPack;
class WorldContainer;
//tolua_begin
struct ClientStatus
{
	WCoord curpos;
	Rainbow::Vector3f motion;
};
//tolua_end

//tolua_begin
enum
{
	UIOP_NULL = 0,
	UIOP_PUNCH,
	UIOP_USE
};
//tolua_end

//tolua_begin
enum
{
	Upload_BasketBall_Create = 1,
	Upload_BasketBallWear_Use,
	Upload_BasketBallFrame_Add,
	Upload_BasketBall_Dribble,
};
//tolua_end
//tolua_begin
enum MountType
{
	MOUNT_NOT = 0,
	MOUNT_RIDE = 1,
	MOUNT_SLEEP = 2,
	MOUNT_SIT = 3,
	MOUNT_DRIVE = 4,
	MOUNT_MANIPULATE_EMITTER = 5, //使用手持发射器
};
//tolua_end

EXPORT_SANDBOXGAME extern float s_WheelDist;
extern int s_CurPlayerIndex;

class EXPORT_SANDBOXGAME PlayerControl;
class PlayerControl : public ClientPlayer, public IControlInterface, public IPlayerControl //tolua_exports
{ //tolua_exports
	DECLARE_SCENEOBJECTCLASS(PlayerControl)
public:

	void CreateEvent2();
	void DestroyEvent2();
	//tolua_begin
	PlayerControl();
#pragma region IPlayerControl
	virtual int GetIUin() { return getUin(); }

	virtual World* getIWorld() 
	{
		return getWorld(); 
	}

	virtual IBackPack* getIPlayerControlBackPack() 
	{ 
		return getIBackPack(); 
	}

	void OnLoadChunk(CHUNK_INDEX index) override;

	virtual void iNotifyGameInfo2Self(int infotype, int id, int num = 0, const char* name = NULL, const char* buff = NULL) {
		notifyGameInfo2Self(infotype, id, num, name, buff);
	}

	virtual void clearBuffAndStatus();

	virtual void iSetGameScore(int s)
	{
		return setGameScore(s);
	}

	virtual const char* GetPlayerControlNickname() override
	{
		return getNickname();
	}
	virtual unsigned short GetPlayerControlCurMapID() { return getCurMapID(); }

	virtual bool GetPlayerControlAimPos(int& outx, int& outy, int& outz, int distance = 20, bool calibration = false) {
		return getAimPos(outx, outy, outz, distance, calibration);
	}

	virtual IActorLocoMotion* getPlayerControlLocoMotion() override;

	virtual WorldContainer* GetPlayerControlCurOpenedContainer() override;

	virtual int GetPlayerControlCurToolID() override
	{
		return getCurToolID();
	}
	virtual int GetPlayerControlCurPlaceDir() override
	{
		return getCurPlaceDir();
	}
	virtual WCoord GetPlayerControlCurOperatePos(int operatedistance = 5) override
	{
		return getCurOperatePos(operatedistance);
	}
	virtual WCoord& GetPlayerControlPosition() override
	{
		return getPosition();
	}
	virtual bool GetPlayerControlSneaking() override
	{
		return getSneaking();
	}
	virtual bool GetPlayerControlRun() override
	{
		return getRun();
	}

	virtual WCoord GetPlayerControlEyePosition() override
	{
		return getEyePosition();
	}
	virtual bool HasReversed() override;
	virtual bool HasGundef() override;
	virtual int getGunSpread() override;
	virtual int GetGunCrosshair() override;
	virtual void setZoom(bool open) override;
	virtual bool getZoom() override;
	virtual void resetRecoil() override;

	virtual WCoord getCameraPosition() override;
	virtual int getOption(unsigned char option) override;
	virtual float getInitPlayerYaw() override;
	virtual void ActorUpdate(float dtime) override 
	{
		ClientActor::update(dtime);
	}
	virtual void onTouchInputEvent(const Rainbow::InputEvent& inevent) override;
	virtual bool GetEnableInput() override
	{
		return m_EnableInput;
	}
	virtual void SetCurMouseX( float value ) override
	{
		m_CurMouseX = value;
	}
	virtual void SetCurMouseY(float value) override
	{
		m_CurMouseY = value;
	}
	virtual bool IsGunHoldState(int state) override;
	virtual void PlayGunByState(int state) override;
	virtual const CustomGunDef* GetCustomGunDef() override;
	//获取当前的FPS手臂摇晃参数
	virtual void GetArmShakeParam(float& amplitude, float& speed) override;
	virtual void DoCurShotGunModEntry(bool enable) override;
#pragma endregion

	virtual bool init(int uin, const char *nickname, int playerindex, const char *customjson) override;

	virtual void enterWorld(World *pworld);
	virtual void leaveWorld(bool keep_inchunk);
	
	virtual Rainbow::Vector3f getCameraLookDir() override;
	virtual void tick();
	virtual void update(float dtime);
	void updateSuper(float dtime);
	void EnableUpdateRun(bool b);
	void updateRun();
	void setRun(bool) override;
	void updatePlayer(float dtime);
	virtual void onDie();
	virtual void onBuffChange(int chgtype, int buffid, int bufflv, int buffticks, int buffInstanceId = 0) override; //chgtype=0:add, 1:remove, 2:clear all
	virtual void onSetCurShortcut(int i);
	virtual void onSetCurSprayPaint(bool isNext);
	void updateFishUp();
	//flags = 1: 成就，  2: 总的统计,  3: 两者都有
	virtual void addAchievement(int flags, ACHIEVEMENT_TYPE achievetype, int target_id=0, int num=1) override;

	//type:活动类型，id：生物id或道具id等，value：进度值
	virtual void addOWScore(float score) override;
	//interactType: 0-attack,1-check interact then attack 2-only interact
	virtual bool interactActor(ClientActor *target, int interactType = 0, bool interactplot=false) override;
	//参数 onlyCheckAtk: 只检测能否攻击
	virtual bool performInteractActor(ClientActor* target, int interactType, bool interactplot, bool onlyCheckAtk) override;
	virtual bool interactBlock(const WCoord &targetblock, DirectionType targetface, const Rainbow::Vector3f &colpoint) override;
	virtual bool interactUnderEditorMode();
	virtual void onDisApplyEquips(int itemid) override;	//卸载装备回调
	virtual void onApplyEquips(int itemid, int grid_index = 0);		//装载装备回调
	virtual bool attackRangedFree(int status) override;
//	virtual bool eatFood(int itemid, int status) override;
	virtual bool useItem(int itemid, int status, bool onshift = false, unsigned int useTick = 0) override;
	virtual bool usePackingFCMItem(int itemid, WCoord usepos) override;
	virtual bool useSpecialItem(int grid_index, int itemId, int num=1) override;
	virtual void applyEquips(EQUIP_SLOT_TYPE t = MAX_EQUIP_SLOTS) override;
	//virtual bool useItemSkill(int itemid, int status, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos) override;
	virtual float getRotaionLimitAngle() const override;
//	virtual void beginChargeKickBall() override;
//	virtual void doKickBall(int type, float charge, ClientActor *ball) override;
//	virtual bool doCatchBall(ClientActor *ball) override;
//	virtual bool doTackle() override;
//	virtual void endTackle() override;
	virtual void starConvert(int num) override;
//	virtual bool doBlockShot() override;
//	bool checkBasketBallGrab();
//	virtual bool doRunDribbleRunBasketBall(ClientActor* ball) override;
	bool isIdolExpressionAction(int seq);//是否待机表情动作~~~(pc按x弹出的表情动作)
	bool isInvisibleByRide();	
	virtual void resetEyeDir(bool checkopway = true);
	void resetEyeDirExUGC(bool checkopway = true);
	bool checkIsFreeViewFormTPSBACK2(int seqId, const std::string& moveState, const std::string& actionState);
	void lockEyeDirWithView();
	//virtual bool basketBallOPStart(int type, ClientActor* ball) override;
	//virtual void basketBallOPEnd(int type, ClientActor* ball) override;
//	virtual void beginChargeThrowBall() override;
//	virtual void doKickBasketBall(int type,BasketballFall result,float charge, ClientActor* ball,const WCoord& pos, float cameraYaw, float cameraPitch, int selectedActorUin = -1) override;

//	virtual bool openContainer(WorldContainer *container) override;
//	virtual bool openContainer(ActorContainerMob *container) override;
	virtual void changePlayerModel(int playerindex, int mutatemob=0, const char *customskins="", const char* custommodel = NULL, int itemid = 0, int blockid = 0,bool force = false) override;
	virtual void checkNewbieWorldProgress(int curprogress, const char *name);
	virtual void checkNewbieWorldProgress(int curLv, int curStep);
	virtual void checkNewbieWorldTask(int itemid);
	virtual bool hasUIControl()
	{
		return true;
	}
	virtual void tryMountActor(ClientActor *actor, short shapeshiftid=0);
	virtual void shapeShift();
	virtual void tryWakeup();
	virtual void tryStandup() override;
	virtual bool castShadow();
	virtual void switchCurrentItem();
	virtual ChunkViewer* GetChunkViewerForCtrl() override;

	//显示、隐藏开关
	void SetHideEquipAvatar(bool bShow);
	bool GetHideEquipAvatarState();
	//为了解耦操作，添加新的获取aabb的接口
	virtual Rainbow::AABB GetIAABB() { return GetAABB(); }
	virtual void resetHandModel() override;
	bool isRightToolDigging()//是否是正确的工具
	{
		return isRightTool;
	}
	virtual void setIsRightTool(bool val) override
	{
		isRightTool = val;
	}
	int RightToolId;
	int getRightToolId()//是否是正确的工具
	{
		return RightToolId;
	}
	virtual void setRightToolId(int val) override
	{
		RightToolId = val;
	}
	virtual void setExploiting(bool val) override//设置是否显示开垦中字样
	{
		isShowExploit = val;
	}
	bool isShowExploiting()
	{
		return isShowExploit;
	}
	virtual void completeTask(int taskid) override;
	//void onClick(float x, float y);
/*
	void onLongPress(float x, float y);
	void onLongPressEnd();
	void onTriggerLongPress(float x, float y);*/

	void onTriggerUseAction();
	void onUseActionEnd();
	//void updateDigState(float x, float y);


	virtual float getDigProgress() override;

	virtual float getReviveProgress() override; //获取救援进度
	virtual void setCircleProgress(float p) override;

	//void resetAllState();

	virtual int getDigBlockID() override;
	
	virtual int onInputEvent(const Rainbow::InputEvent &ev);
	//virtual int onInputEvent(const SandBoxInputEvent &ev);
	void ConvertToSandboxInputEvent(const Rainbow::InputEvent& ev, SandBoxInputEvent& sandboxEvt);

	virtual GameCamera *getCamera() override
	{
		return m_pCamera;
	}


	virtual TouchControl *getTouchControl()
	{
		return m_TouchCtrl;
	}

	virtual PCControl *getPCControl()
	{
		return m_PCCtrl;
	}

	PlayerInputHelper* getPlayerInputHelper()
	{
		return m_InputHelper;
	}

	VehicleControlInputs* getVehicleControlInputs()
	{
		return m_VehicleControlInputs;
	}

	void getClientStatus(ClientStatus &status);

	virtual Rainbow::Vector3f getLookDir() override;

	//bool findNearestBlock(WCoord &blockpos, int blockid);
	bool findNearestClientItem(WCoord &blockpos, int itemid);

	//for npc shop api
	void setNpcShopInfo(int shopid);
	int getNpcShopInfo(int shopid, PB_NpcShopData* npcshopdata);
	int buyNpcShopSku(int shopid, int skuid, int buycount, NpcShopInfo& skuinfo);
	void respNpcShopInfo(const PB_NpcShopData& npcshopdata);
	void updateNpcShopSkuInfo(int shopid, int skuid, int leftcount, int endtime, int buycount, bool isSuccess);

	virtual void doWakeUp();
	virtual bool doSpecialSkill() ;
	virtual bool tryShapeShift(short shapeshiftid);
	bool isInsideNoOxygenBlock();
	virtual bool isSightMode();
	virtual void setSightMode(bool b,bool isIgnoreMoblie = true);   //老的行为只是改变PC端的SightMode,需要兼容老的行为,否则移动端会各种准心模式问题
	void resetInput();
	virtual void trySleep();
	void lerpRotationTo(float targetYaw, float tickTime);
	bool findNearOneClientItemPos(int &x, int &y, int &z, int itemid);
	virtual void setViewMode(int mode, bool ignoreBasketBaller=false) override;
	int  getChangeNextViewMode();
	//带偏移量的视角模式转换接口，x、y、z为地图坐标偏移值
	void setViewMode(int mode, float x, float y, float z);
	void SetViewLock(int mode, bool bLock);
	bool IsViewLock();
	float getPlayerYaw();

	void setPlayerYaw(float nYaw);
	
	virtual int getViewMode() override
	{
		return m_ViewMode;
	}
	int getViewModeNum();

	virtual bool getBobbingByRocket();
	void setBobbingByRocket(bool bobbing);
	
	virtual bool isShakingCamera();
	virtual void setShakeCamera(bool shaking, int power = 1, float duration = 1);

	/*缓动曲线旋转镜头，目前用于配合枪械上弹、拉栓等动作
	* 产品一般用lua配置曲线
	floatCurveConfig={                                            --浮点型曲线
		[1]={time = 0, value = 0, tweenFun = "linear"},			    --关键帧，时间，浮点值，缓动类型
		[2]={time = 0.02, value = 3, tweenFun = "linear"},
		[3]={time = 0.1, value = -2, tweenFun = "linear"},
		[4]={time = 0.05, value = 5, tweenFun = "linear"},
		[5]={time = 0.02, value = -1, tweenFun = "linear"},
		[6]={time = 0.1, value = 6, tweenFun = "linear"},
		[7]={time = 0.02, value = -4, tweenFun = "linear"},
		[8]={time = 0.1, value = 0, tweenFun = "linear"},
	}
	业务中：
	if CurMainPlayer then
		for i=1, #floatCurveConfig do
			keyFrame = floatCurveConfig[i]
			CurMainPlayer:TweenRotCameraKeyFrame(keyFrame.time, keyFrame.value, keyFrame.tweenFun)
		end
		CurMainPlayer:TweenRotCameraStart()
    end
	*/
	void TweenRotCameraKeyFrame(float keyDeltaTime, float degreeValue, string tweenFun);
	void TweenRotCameraStart();
	void TweenRotCameraClear();

	virtual CameraConfig *getCurCameraConfig();

	virtual InputInfo* getInputInfo() override{
		return m_InputInfo;
	}

	PlayerAnimation* getPlayerAnimation(){
		return m_PlayerAnimation;
	}

	void resetCameraPos();
	virtual void setCameraRotate(int Yaw, int Pitch, float mul);
	virtual void resetCurCameraConfig(bool isSavedConfig); // true:为自定义视角设置，false为系统默认设置
	virtual void applyCurCameraConfig();

	virtual int getCameraConfigOption(unsigned char option);
	virtual void setCameraConfigOption(unsigned char option, unsigned char value);
	float getCameraConfigFov();
	void setCameraConfigFov(float fov);
	virtual WCoord getCameraConfigPosition(float yaw, float pitch);
	virtual void setCameraConfigPosition();		//  以当前摄像机的位置为准
	virtual void setCameraConfigPositionEx(int x, int y, int z);
	Rainbow::Vector3f getCameraConfigLookDir();
	void setCameraConfigLookDir();		// 以当前摄像机的朝向为准
	void setCameraConfigLookDirEx(int Yaw, int Pitch);		

	void setCameraFov(float fov);

	void clearAllInputState();
	void showUI(bool);

	virtual void dismountActor() override;
	MountType getMountType();
	ActorHorse *getRidingHorse();
	ActorRocket *getRidingRocket();
	ActorVehicleAssemble *getDrivingVehicle();
	/*
	BlockOperateMgr *getBlockOperate()
	{
	return m_pBlockOp;
	}*/

	void setAccumulatorState(float progress);

	void setBasketBallLockState(bool state, float wx=0, float wy=0);

	virtual void setCurShortcut(int i);
	void EnableSetCurShortcut(bool b);
	void RefreshCurShortcut();
	int GetCacheCurShotcut() { return m_cacheCurShotcut; }

	virtual void setCurSprayPaint(bool isNext);

	virtual bool revive(int reviveType=0, int x=0, int y=-1, int z=0);

	void playGuideSound2D(const char *path);
	void OnPlayerEyeEnterWater();
	void OnPlayerEyeOutOfWater();
	virtual void setMoveUp(int dir); //-1: 往下, 1: 往上
	void cancelMoveUp(int dir);

	void setUIHide(bool b, bool isShowCursor=false, bool isChangeDispayName=true);

	void switchActView();
	//是否在隐藏UI的时候不隐藏手臂
	void setShowHandState(bool b)
	{
		m_bIsShowHandWhenHideUI = b;
	}

	bool getShowHandState()
	{
		return m_bIsShowHandWhenHideUI;
	}

	void setReloadMagazine() { m_isReloadMagazine = true; }

	void setSightingTelescope();
	bool hasSightingTelescope();

	int getBlockX();
	int getBlockY();
	int getBlockZ();
	int getBlockLight();

	bool beginTraceBlock(int id, int type=1); //type 1方块 2掉落物
	void setTraceBlockState(bool b);
	bool isShowMoveDir();
	float getCollideInFaceX(); //得到当前碰撞点在面内的值[0, 1]
	float getCollideInFaceY(); //得到当前碰撞点在面内的值[0, 1]
	float getCollideInFaceZ(); //得到当前碰撞点在面内的值[0, 1]
	bool pickLiquid(int &x, int &y, int &z);
	void setInputActive(bool enble);
	void setActionInputActive(bool enble);
	void setMoveInputActive(bool enble);
	virtual int doPick(bool pickliquid, bool ignorecarried=false, bool addboxpickrange=false, bool isPlaceOnActor = false);  //pick的range是否加上包围盒的长度
	int doRangeSkillPick(bool pickliquid, int range,IntersectResult& result, bool ignorecarried = false, bool addboxpickrange = false);  //给技能释放检测那里用的pick
	int doPickByType(int picktype, bool ignorecarried = false);
	float getExtraPickRange();
	void resetGameCameraUGC();
	virtual void changeGameMode(bool scenefallback = true);

	// 20220809 联机房间下切换地图模式，仅创造模式的创造/冒险地图，主机可用 by huangrulin
	bool changeMpGameMode(bool scenefallback = true);
	void triggerHeadshotTip(bool isDead = false);
	void triggerNormalshotTip(bool isDead = false);
	void triggerVehicleshotTip();
	void setAllKeyBindState(bool b);
	void setOneKeyBindState(bool b, int keycode);

	float getOverLookAngleToScreen();
	void getPointToScreen(float &x, float &y, float &z, ClientActor *actor, int offset=0);
	void getPointToScreen(float &x, float &y, int posx, int posy, int posz);
	float getAngleToScreen(int posx, int posy, int posz);
	void setCameraRotate(int y, int posx, int posy, int posz);
	ClientActor *spawnItem(int itemid, int num, int posx, int posy, int posz);

	//virtual void closeContainer();
	bool setContainerText(int index, const char *text);
	virtual void doExchangeItems(int useitemid, int usenum, int gainitemid, int gainnum, int type);
	virtual void closePlotDialogue();
	virtual int lootItem(int fromIndex, int num);
	virtual void swapItem(int fromIndex, int toIndex);
	virtual void mergeItem(int fromIndex, int toIndex);
	virtual void discardItem(int index, int num);
	virtual void sortPack(int base_index);
	virtual void sortStorageBox();
	virtual void craftItem(int craftid, int num = 1);
	virtual void AddToCraftingQueue(int craftid, int num = 1);// 新增制作队列函数
	virtual void RemoveFromCraftingQueue(int index); // 新函数，用于从制作队列中删除任务
	virtual int enchant(int tgtGridIdx, int frmGridIdx, int enchants[MAX_ITEM_ENCHANTS]); //not_auto_export_to_lua
	virtual int enchantRandom(int tgtGridIdx);

//	virtual int storeItem(int frmGrid, int num);
	virtual int repair(int tgtGridIdx);
	virtual void setItem(int itemid, int toIndex, int num=1, const char *sid_str = "");
	virtual void setItemWithoutLimit(int itemid, int toIndex, int num, const char *userdata_str, const char *sid_str = "");
    //设置颜色方块到快捷栏或者背包上，因为旧的走setItemWithoutLimit会被反作弊拦截，为了不影响反作弊逻辑，特此新加一个接口
	virtual void setDyeableItem(int itemid, int toIndex, int num, const char* userdata_str);
	virtual void moveItem(int fromindex, int toindex, int num); //num<=0表示shiftMoveItem, toindex代表类型
	virtual void doReload(int bulletid, int num, bool isCustomGun = false);
	virtual void doReloadWithoutCheck(int num);
	virtual bool takeAccountItemsToHost(int itemid, int num){return true;}

	void setSpectatorMode(PLAYER_SPECTATOR_MODE spectmod);
	void setSpectatorType(PLAYER_SPECTATOR_TYPE specttype);
								
	void setToSpectatorPlayerUin(int uin);
	int getToSpectatorPlayerUin() { return m_nToSpectatorUin; }
	void CheckSpectatorPlayerShow();

	bool isUnlockItemById(int itemid);

	virtual bool playAct(int act, bool isSwitchViewMode = true);
	virtual bool playSkinAct(int act, const int inviteUin, const int acceptUin); //******** codeby:chenwei 装扮互动动画
	virtual void switchSkinActView(); //******** codeby:chenwei 装扮互动切换视角模式

	//for npc shop
	void reqGetNpcShopInfo(int shopid);
	void reqBuyNpcShopSku(int shopid, int skuid, int buycount);

	//家园引导
	void setHomeTaskDir(bool b);
	bool isShowHomeTaskDir();
	void setHomeTaskTarget(float x, float y, float z);

	void setIsInVehicleWorkshop(int iInOut) { m_iInVehicleWorkShop += iInOut; }
	int getIsInVehicleWorkshop() { return m_iInVehicleWorkShop; }

	//void PickMobBackpack(int gridIndex,int moveType = 1);

	//virtual void resetActorBody();	//变形后还原模型
	std::vector<WORLD_ID> doPickActorByItemSkill(int skillid, WCoord &centerPos, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir);
	std::vector<WCoord> doPickBlockByItemSkill(int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir);
	std::vector<WORLD_ID> doPickPhysicsActorByItemSkill(int skillid, WCoord &centerPos, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir);

	virtual void doGunFire(int id);
	virtual void fall(float fall_dist) override;
	virtual void setFlying(bool b) override;
	bool checkCanJump();
	virtual bool setJumping(bool b) override;
	virtual void setSneaking(bool b) override;
	virtual void setMoveForward(float speed) override;				// 向前移动多少格，<0:保持向后移动
	virtual void setMoveStrafing(float speed) override;			// 向右移动多少格，<0:保持向左移动
	//tolua_end
	virtual bool mountActor(ClientActor *actor, bool isforce=false, int seatIndex = -1, bool bcontrol = true) override;
	virtual void changeMoveFlag(unsigned flag_id, bool on);
	//tolua_begin
	virtual void tryCarryActor(ClientActor *actor, WCoord pos = WCoord(0, -1, 0));
	virtual bool carryActor(ClientActor *actor, WCoord pos = WCoord(0, -1, 0)) override;
	bool isCurToolUnlocked(int toolid=-1);
	void renderWithLastTriggerBlocks();
	bool canShowCameraModel();
	bool canShowCameraModelExUGC();
	void changeGameraZoomInOut(bool isridingrocket);
	void setHookObj(WORLD_ID obj, bool include_me = true);

	//p1=="common_front" 公共参数提前 p1=="param_to_str" 0.35.5版本之后的新增的埋点参数全部使用string类型
	void statisticToWorld(int uin, int id, const char *fristname = "", int worldtype = -1, const char *p1 = "", const char *p2 = "", const char *p3 = "", const char *p4 = "", const char *p5 = "", const char *p6 = "", const char *p7 = "");
	int getCurWorldType();
	virtual void updateGameCamera(float dtime) override;
	bool updateGameCameraExUGC(float dtime);
	void recoverActView();
	void setPlayerCamera(int playerindex, int mutatemob=0, const char *customskins="");
	virtual int getLang()
	{
		return GetIWorldConfigProxy()->getGameData("lang");
	}
	virtual int getApiid();

	virtual void setLang(int lang);

	virtual void onToggleGameMode() override;
	bool checkIfDataUpload(int iType);
	void setUploadStatus(int iType, int iTime = 0);
	void loadWorldUploadStatus(WORLD_ID wid, int specialType = NORMAL_WORLD);
	void saveWorldUploadStatus(WORLD_ID wid, int specialType = NORMAL_WORLD);
	//IPlayerControl
	virtual void setRefreshType(int type) override
	{

	}
	virtual void postInfoTips(int tip) override;
	void ShowGameSocTips(const std::string &text, int time, int level);
	//bool  isCanGiveItemToWild(); // 是否可以给野人道具
	virtual int GetExploitCheckBlockId(bool isDigBlockId) override; //获取开垦方块需要检测的方块ID
	bool isRidingOnCrab();
	void onActorHorseMounted();


	virtual bool needCheckVisible();//not_auto_export_to_lua	// 20210926：是否检查可视（模型裁剪用）  codeby： keguanqiang

	ClientActor* GetPickActor();

	//20210926:音乐方块同步播放动作 codeby：huangxin
	void MusicClubPlayAnim(int seq);

	virtual bool checkInteraction(std::string& outtip) override;
	virtual void handleInteraction() override;
	bool getInteractionTip(IClientActor* actor, std::string& outTip);
	std::string getActorInteractionTips(IClientActor* actor);

	//是否直接隐藏手部模型
	bool IsHideHandModel();
	void SetHideHandModel(bool isHide) { m_IsHideHandModel = isHide; }
	//是否在使用拍立得相机
	bool IsUsePolaroidCamera() { return m_IsUsePolaroidCamera; }
	void SetUsePolaroidCamera(bool isUse) { m_IsUsePolaroidCamera = isUse; }
	int getPressEEvent() { return m_nOperateKeyEType; }
	//tolua_end

	void ResettingPlayerModel(); //玩法转编辑重置玩家初始外观
	// 上报客户端actionlog
	virtual void SendActionLog2Host(bool cheat, const std::string& eventName, const std::string& detail) {} //not_auto_export_to_lua
	
	bool checkInteractive();
	void renderUI();
	//20211214 根据改变视角消息设置玩家视角 codeby:liushuxin
	void changeViewModeByMsg(); //not_auto_export_to_lua
	void saveMsgChangeViewMode(int mode, bool lock); //not_auto_export_to_lua
	virtual void onOperateEnded() override; //not_auto_export_to_lua
	virtual void playEffect(ACTORBODY_EFFECT fx) override; //not_auto_export_to_lua
	virtual bool isUGCMode(int modeType = 0) { return false; };
	virtual void setStill() override;
	//方块高亮
	void tickBlockLine();

	bool isTriggerTrap();
	virtual BlockScene* getScene();

	static void SetAllowInput(bool allow, size_t event=0, int keyvalue=0);
	virtual bool GetAllowInput(size_t event, int keyvalue) override;

	static void ClearForbiddenEvent();

	static void SetCurrentLogicFrame(unsigned int count ) { s_currentLogicFrame = count; };
	static unsigned int  GetCurrentLogicFrame() { return s_currentLogicFrame; };
#ifdef BUILD_MINI_EDITOR_APP
	virtual void SetEnableChunkViewer(bool enable);
#endif

	virtual void UpdateXrayEffectEnable() override;
	virtual bool IsXrayEffectEnable() override;

	bool isAimState();
	void registerFirstPersonIK();
	void unRegisterFirstPersonIK();
private:
	//刺球相关
	void	checkThornBall();
	void	sawtoothAttackedUp(float atkpoints);
	void	sawtoothAttackedRound(float atkpoints);
	void    facingMovingDirection(bool checkopway);//面向移动的方向


protected:
	bool isPlayerClockRotationWithCamera();
	virtual ~PlayerControl();

	virtual OpenContainerComponent* getOpenContainerCom()override;
	virtual ItemSkillComponent* getItemSkillComponent() override;
	virtual void tickOperate() override;
	virtual void setOPWay(int way) override;

	void setBowStage(int stage);
	void tickUIOp();
	void checkAttribChange();
	void sendViewModeToSpectator();
	void triggerActorAttribChunk();
	void updateMoveControlFromInputinfo();

	bool m_saveLastViewMode;
	short m_iTryShapeShiftID;
	short m_iShapeShiftTick;
	bool m_ViewLock;
public:

	float m_ReviveProgress = -1; //救援进度

	GameCamera* m_pCamera = NULL;
	//tolua_begin

	//BlockOperateMgr *m_pBlockOp;
	//FPS = 0, TPS_BACK = 1,TPS_FRONT = 2,
	int m_ViewMode;
	int m_ViewMode_ToSpectator;
	//For become chicken buff
	int m_NeedRevertToFPS;
	int m_ActBeforeViewMode; //20210929 codeby:chenwei 播放动作前viewmode

	unsigned int m_SpaceClickTick; //检测是否space连按两下
	unsigned int m_WKeyClickTick; //检测'W'是否连按两下
	unsigned int m_LastSyncRotTick;


	bool m_IsEquipedJetpack;
	int m_DisplayOxygen; //-1: 不显示， >=0 显示
	bool m_IsEyeInWater;
	bool m_IsEyeInWaterLastFrame;

	//旧版代码兼容？
	float m_OldLife;
	float m_OldOxygen;
	float m_OldFoodLevel;
	float m_OldFoodSatLevel;
	

	WCoord m_DiePos;
	int m_DieMapID;
	int m_DieRecordTicks;
	bool isRightTool;
	int m_CurBowStage;
	bool isShowExploit;
	float m_CurMouseX;
	float m_CurMouseY;
	int m_PickType;

	int m_SwitchTick;

	TouchControl *m_TouchCtrl;
	PCControl *m_PCCtrl;

	PlayerAnimation *m_PlayerAnimation;
	int m_HorseCharge; //坐骑蓄力值
	int m_HorseHP; //坐骑血量

	//Record last tool in hand
	int m_lastToolId;
	int m_LastJumpMark;
	bool m_EnableInput;
	bool m_EnableMoveInput;
	bool m_EnableActionInput;
	bool m_isDigging;
	bool m_isReloadMagazine;

	bool m_LerpRotation;
	float m_LerpRotationDuration;
	float m_TargetYaw;
	float m_OriginYaw;
	float m_LerpRotationStartMarker;

	InputInfo* m_InputInfo;
	PlayerInputHelper* m_InputHelper;
	Rainbow::ISound* m_PlayerSounder;
	Rainbow::ISound* m_GuideSounder;
	fairygui::GComponent* m_TipUI;

	CameraConfig m_CurrentCameraConfig;	// 当前视角配置
	//Rainbow::Model *m_MoveDirective;		// 移动指示

	int m_nCheckSpectatorTick; //上次检查被观战玩家的tick

	int m_CatchBallCDTick; //抓球冷却时间
	int m_TackleCDTick;  //铲球冷却时间
	int m_BlockShotCDTick; //盖帽冷却时间
	int m_cdRunTime;	   //运气冲刺冷却时间
	int m_nGrabCDTick;	//篮球抢断的冷却时间(帧数)

	bool m_BobbingByRocket;
	//20210724: 触发器新API  codeby:wangshuai
	bool m_IsShaking;

	VehicleControlInputs* m_VehicleControlInputs;

	bool m_bIsShowHandWhenHideUI;	//是否在隐藏UI的时候显示手臂
	bool m_bIsRidden;	//是否乘骑载具
	ActorAttribut *m_ActorAttribTrigger;	//角色属性
	int m_iTickTriggerCount;	//开发者tick触发计数 10个tick触发一次
	std::map<int, int> m_UploadStatusMap;
	bool m_bIsShowBasketBallLockHint;

	int m_iRecoverViewMode;  //需要复原的视角模式
	bool m_bWaitRecoverViewMode;

	float m_wingForwardSpeed; //起飞得向前速度倍率
	float m_wingFlyTime; //起飞后可以上升得时间,负数代表空中下落时间

	int m_oldViewMode; //切换模式前的视角模式
	Vector3f m_CameraOffset;
	PlayerViewMask* m_playerViewMask;
	//tolua_end

	bool	m_IsJump;
	bool m_receiveChangeViewModeMsg; //是否收到过PB_PLAYER_CHANGEVIEWMODE_HC消息
	int m_msgViewMode; //PB_PLAYER_CHANGEVIEWMODE_HC消息包含的视野模式
	bool m_msgLock; //PB_PLAYER_CHANGEVIEWMODE_HC消息包含的视野锁定
private:	
	static unsigned int s_currentLogicFrame;
	bool bRepeatTipsToggleEasyMode;
	
	int m_intervalActorTravelingTrader;
	bool m_lastSkillInterrupt = true; //技能打断标记
	bool m_enableUpdateRun{ true }; //启动更新奔跑状态
	bool m_enableSetShortcut{ true };
	int m_cacheCurShotcut{ -1 };
	void *m_tween = nullptr;
	int m_tweenID = 0;
	bool m_IsHideHandModel;//是否直接隐藏首部模型
	bool m_IsUsePolaroidCamera;//是否正在使用拍立得相机
	void updateBlockPlacementPreview();
	int m_nOperateKeyEType = -1;
}; //tolua_exports

extern EXPORT_SANDBOXGAME PlayerControl *g_pPlayerCtrl;
EXPORT_SANDBOXGAME extern float GetPickRange(PlayerControl* player);
EXPORT_SANDBOXGAME extern int PlayerDistFromRailLastBlock(ClientPlayer* player, WCoord* ret_handpos);
#endif