#include "LinuxGameApp.h"
#include "Core/GameEngine.h"

#include "AssetPipeline/AssetManager.h"
#include "Misc/FrameTimeManager.h"

#include "File/Packages/PackageAsset.h"
#include "BaseClass/SharePtr.h"

#include "Core/EngineClassRegister.h"
#include "Allocator/MemoryManager.h"
#include "Utilities/Logs/LogSystem.h"
#include "Misc/PlayerSettings.h"

#include "Common/GameStatic.h"
#include "IWorldConfig.h"
#include "Misc/GameSetting.h"

#include "MiniCloud/sim/SimulateMgr.h"
#include "MiniCloud/sim/LiveStreamMgr.h"
#include "ClientInfo.h"
#include "GameInfo.h"
#include "AssetPipeline/AssetProjectSetting.h"
#include "GetClientInfo.h"
#include "LuaInterface.h"
#include "Platforms/PlatformInterface.h"
#include "ZmqProxy.h"
#include "ActionLogger.h"
#include "ClientGameStandaloneServer.h"
#include "OgreTimer.h"
#include <Core/CallBacks/GlobalCallbacks.h>
#include "EffectManager.h"
#include "WorldManager.h"
#include "ClientActorManager.h"
#include "ClientActor.h"
#include "SandboxGameNode.h"
#include "fps/statistics/SandboxStatisticsNode.h"
#include "GeoJsonParser.h"
#include "IClientPlayer.h"
#include "ClientPlayer.h"
#include "DebugJsonCmd.h"
#include "ChunkGenerator.h"
#include "ClientMob.h"
#include "worldEvent/AirDrop/AirDropEvent.h"
#include "Core/worldEvent/WorldEventManager.h"
#include "ChestMgr.h"
#include "sign/HttpSign.h"

using namespace MINIW;
 

bool g_bGameQuit = false;

#if PLATFORM_LINUX
#include <unistd.h>
#include <fcntl.h>
#include <sys/file.h>
#include <sys/stat.h>

#include "OgreStringUtil.h"
#include "ClientInfo.h"
#include "client/linux/handler/exception_handler.h"
#include "jemalloc/jemalloc.h"

extern unsigned long long getProcessMemoryBytes();
extern unsigned long long getMemoryUsage();
extern double getCpuUsage();

static int check_pid(const char* pidfile) {
	int pid = 0;
	FILE* f = fopen(pidfile, "r");
	if (f == NULL)
		return 0;
	int n = fscanf(f, "%d", &pid);
	fclose(f);

	if (n != 1 || pid == 0 || pid == getpid())
	{
		return 0;
	}

	if (kill(pid, 0) && errno == ESRCH)
		return 0;

	return pid;
}

static int write_pid(const char* pidfile) {
	FILE* f;
	int pid = 0;
	int fd = open(pidfile, O_RDWR | O_CREAT, 0644);
	if (fd == -1)
	{
		printf("Can't create pidfile [%s].\n", pidfile);
		return 0;
	}
	f = fdopen(fd, "r+");
	if (f == NULL)
	{
		printf("Can't open pidfile [%s].\n", pidfile);
		return 0;
	}

	if (flock(fd, LOCK_EX | LOCK_NB) == -1)
	{
		int n = fscanf(f, "%d", &pid);
		fclose(f);
		if (n != 1)
		{
			printf("Can't lock and read pidfile.\n");
		}
		else
		{
			printf("Can't lock pidfile, lock is held by pid %d.\n", pid);
		}
		return 0;
	}

	pid = getpid();
	if (!fprintf(f, "%d\n", pid))
	{
		printf("Can't write pid.\n");
		close(fd);
		return 0;
	}
	fflush(f);
	//close(fd);

	return pid;
}

static int redirect_fds()
{
	int nfd = open("/dev/null", O_RDWR);
	if (nfd == -1)
	{
		printf("Unable to open /dev/null: \n");
		return -1;
	}
	if (dup2(nfd, 0) < 0)
	{
		printf("Unable to dup2 stdin(0): \n");
		return -1;
	}
	if (dup2(nfd, 1) < 0)
	{
		printf("Unable to dup2 stdout(1): \n");
		return -1;
	}
	if (dup2(nfd, 2) < 0)
	{
		printf("Unable to dup2 stderr(2): \n");
		return -1;
	}

	close(nfd);

	return 0;
}

int daemon_init(const char* pidfile)
{
	int pid = check_pid(pidfile);

	if (pid)
	{
		printf("miniworldserver is already running, pid = %d.\n", pid);
		return 1;
	}

	/*if (daemon(1,1))
	{
		printf("Can't daemonize.\n");
		return 1;
	}*/

	pid = write_pid(pidfile);
	if (pid == 0)
	{
		return 1;
	}

	/*if (redirect_fds())
	{
		return 1;
	}*/

	return 0;
}

void sign_handler(int sig)
{
	GetClientApp().handleSIGUSR2();
}

void check_param_handler(int sig)
{
	printf("==== check_param_handler get SIGUSR1\n");
	bool active = true;
	mallctl("opt.prof_active", NULL, NULL, &active, sizeof(bool));
	active = true;
	mallctl("prof.active", NULL, NULL, &active, sizeof(bool));
	mallctl("prof.dump", NULL, NULL, NULL, 0);

	// tinyxml2::XMLDocument  doc;
	// if (tinyxml2::XML_SUCCESS != doc.LoadFile("../serverroom.xml"))
	// {
	// 	return;
	// }
	// tinyxml2::XMLElement* root = doc.FirstChildElement("ServerRoom");
	// if (root)
	// {
	// 	tinyxml2::XMLElement* server = root->FirstChildElement("server");
	// 	while (server)
	// 	{
	// 		tinyxml2::XMLElement* room = server->FirstChildElement("room");
	// 		while (room)
	// 		{
	// 			const tinyxml2::XMLAttribute* AddrAttr = room->FirstAttribute();
	// 			std::string account;
	// 			HashMap<std::string, std::string> roomParam;

	// 			std::string account_left;
	// 			std::string account_right;
	// 			int iport = 0;

	// 			while (AddrAttr)
	// 			{
	// 				const char* name = AddrAttr->Name();
	// 				const char* value = AddrAttr->Value();

	// 				if (strcmp(name, "account") == 0)
	// 				{
	// 					std::vector<std::string> kv;
	// 					Rainbow::StringUtil::split(kv, value, "-");
	// 					if (kv.size() == 2)
	// 					{
	// 						account_left = kv[0];
	// 						account_right = kv[1];
	// 					}
	// 					else
	// 					{
	// 						account = value;
	// 						roomParam["account"] = value;
	// 					}
	// 				}
	// 				else if (strcmp(name, "type") == 0 && strcmp(value, "office") == 0)
	// 				{
	// 					roomParam["owindex"] = "0";
	// 				}
	// 				else if (strcmp(name, "id") == 0)
	// 				{
	// 					char port[16];
	// 					sprintf(port, "%d", 8700 + atoi(value));
	// 					iport = atoi(value);
	// 					roomParam["port"] = port;
	// 				}
	// 				else if (strcmp(name, "mapname") == 0)
	// 				{
	// 					AddrAttr = AddrAttr->Next();
	// 					continue;
	// 				}
	// 				else
	// 					roomParam[name] = value;
	// 				AddrAttr = AddrAttr->Next();
	// 			}
	// 			auto clientmgr = GetClientInfo();
	// 			if (account_left.size())
	// 			{
	// 				int left = atoi(account_left.c_str());
	// 				int right = atoi(account_right.c_str());
	// 				int mini = atoi(clientmgr->getAccount().c_str());
	// 				if (mini >= left && mini <= right)
	// 				{
	// 					roomParam["account"] = clientmgr->getAccount();
	// 					char port[16];
	// 					sprintf(port, "%d", 8700 + iport + mini - left);
	// 					roomParam["port"] = port;
	// 					account = clientmgr->getAccount();
	// 				}
	// 			}

	// 			if (clientmgr->getAccount() == account)
	// 			{
	// 				HashMap<std::string, std::string>::iterator iter = roomParam.begin();
	// 				while (iter != roomParam.end())
	// 				{
	// 					std::string name_ = clientmgr->getEnterParam(iter->first.c_str());
	// 					//if(clientmgr->getEnterHashValue(iter->first) != iter->second)
	// 					if (name_ != iter->second)
	// 					{
	// 						GetClientApp().handleSIGUSR2();
	// 						break;
	// 					}
	// 					iter++;
	// 				}
	// 				break;
	// 			}

	// 			room = room->NextSiblingElement();
	// 		}
	// 		server = server->NextSiblingElement();
	// 	}
	// }
}


void check_param_handler2(int sig)
{
	printf("==== check_param_handler2 get SIGUSR2\n");
	GetClientApp().setSIGUSR2();
}

namespace {
	bool breakpad_callback(const google_breakpad::MinidumpDescriptor& descriptor,
		void* context,
		bool succeeded) {
		// if succeeded is true, descriptor.path() contains a path
		// to the minidump file. Context is the context passed to
		// the exception handler's constructor.
		// std::cout << "on callback " << succeeded << std::endl;
		return succeeded;
	}
}

#endif

// 显示服务器fps 用于性能优化   by:liusijia
namespace TestOutPut {
	unsigned int m_Frames = 0;
	unsigned int m_FramesPS = 0;
	unsigned long long m_LastTick = 0;
	unsigned long long m_StartTick = 0;

	unsigned int m_Frames10Sec = 0;
	unsigned long long m_StartTick10Sec = 0;
	unsigned int m_FramesLow10Sec = 0;
	unsigned int m_FramesHigh10Sec = 0;

	void ShowServerFps(double tick) {
		unsigned long long currentTick = Rainbow::Timer::getSystemTick();
		if (m_LastTick == 0) { // First call
			m_StartTick10Sec = m_StartTick = m_LastTick = currentTick;
		}
		unsigned long long elapsed = currentTick - m_LastTick;
		if (elapsed > 500) { // More than 500 ms since last call
			Rainbow::GetICloudProxyPtr()->SimpleErrLog(0, 0, "tick_slow", std::string("server tick slow cost:") + to_string(elapsed));
		}
		m_LastTick = currentTick;

		++m_Frames;
		++m_Frames10Sec;

		unsigned long long totalElapsed = currentTick - m_StartTick;
		if (totalElapsed >= 1000) { // More than 1000 ms since start
			if (m_Frames < 10)
				SimpleSLOG("Server run slow, fps is:%d  time: %lld ms", m_Frames, totalElapsed);
			else
				LOG_INFO("ServerFPS:%d ms:%d", m_Frames, totalElapsed);

			if (m_Frames > m_FramesHigh10Sec)
				m_FramesHigh10Sec = m_Frames;
			if (m_Frames < m_FramesLow10Sec || m_FramesLow10Sec == 0)
				m_FramesLow10Sec = m_Frames;

			m_FramesPS = m_Frames;
			m_Frames = 0;
			m_StartTick = currentTick;

		}
		totalElapsed = currentTick - m_StartTick10Sec;
		if (totalElapsed >= 10000) { // More than 10000 ms since start
		    auto   s_Uin = GetClientInfoProxy()->getEnterParam("account");
		    auto   s_RoomId = GetClientInfoProxy()->getEnterParam("room_id");
		    auto   s_MapId  = GetClientInfoProxy()->getEnterParam("toloadmapid");
		    auto   s_HostIp  = GetClientInfoProxy()->getEnterParam("ip");
		    time_t now;
		    time(&now);
		    char tmstr[32] = {0};
		    strftime(tmstr, 32, "%Y-%m-%d %H:%M:%S", localtime(&now));
		    jsonxx::Object log;
		    log << "tm" << now;
		    log << "event" << "fps_10sec";
		    log << "aid" << s_MapId;
		    log << "uin" << s_Uin;
		    log << "roomid" << s_RoomId;
		    log << "hostip" << s_HostIp;

		    log << "low_fps" << m_FramesLow10Sec;
			log << "high_fps" << m_FramesHigh10Sec;
			log << "avg_fps" << (int)((m_Frames10Sec / (totalElapsed / 1000.0)) + 0.5);

			ClientActorMgr* actor_mgr = g_WorldMgr->getWorld(0)->getActorMgr()->ToCastMgr();
			if (actor_mgr) {
				log << "player_num" << actor_mgr->getNumPlayer();
			}

#if PLATFORM_LINUX
		    double    CpuUsage = getCpuUsage();
		    long long memUsage = getMemoryUsage();
		    auto      memBytes = getProcessMemoryBytes();
		    log << "cpu_usage" << CpuUsage;
		    log << "mem_usage" << memUsage;
		    log << "mem_bytes" << memBytes;
#endif

			int actors = 0;
			int players = 0;
			EffectManager::EffectStat effStat;
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->GetStatics(actors, players, effStat) == 0)
			{
			    log << "actors" << actors;
			    log << "effects" << effStat.particles;

				auto* p_world = GetWorldManagerPtr()->getWorld(0);
				std::vector<ClientActor*> actors_list;
				p_world->getActorMgr()->ToCastMgr()->getAllLiveActors(actors_list);
				int cnt = actors_list.size();

				std::map < int, int > actors_type_num;
				for(auto actor : actors_list){
					if (actor )
					{
						actors_type_num[actor->getObjType()]++;
					}
				}

				// 将统计数据添加到日志
				for(auto& pair : actors_type_num){
					std::string key = "actor_type_" + std::to_string(pair.first) + "_count";
					log << key << pair.second;
				}
		    }



			int triggerCount;
			int curTriggerSize;
			if (GetObserverEventManagerPtr() && GetObserverEventManagerPtr()->GetStatics(triggerCount, curTriggerSize) == 0)
			{
				GetObserverEventManagerPtr()->ResetStatics();
			    log << "trigger_count" << triggerCount;
			    log << "left_trigger_size" << curTriggerSize;
			}

		    // auto gameroot = MNSandbox::GetCurrentGameRoot();
			// if (gameroot)
			// {
			// 	int nodecount = gameroot->GetDescendantCount();
			//     log << "sandbox_node_count" << nodecount;
			// }
			int nodecount = MNSandbox::Statistics::NodeCount::GetInstance().GetTotal();
			log << "sandbox_node_count" << nodecount;

		    snprintf(tmstr, sizeof(tmstr), "%llu", Rainbow::GetICloudProxyPtr()->genLogID(now));
		    log << "log_id" << std::string(tmstr);
		    std::string logstr = log.json_nospace();
		    FPSLOG(INFO) << logstr;
		    m_Frames10Sec = 0;
			m_FramesLow10Sec = 0;
			m_FramesHigh10Sec = 0;
			m_StartTick10Sec = currentTick;
		}
	}
}

// 显示地图地理信息 用于性能优化
namespace MapOutPut {
	unsigned long long m_LastTick = 0;
	unsigned long long m_StartTick = 0;

	void ShowGeoInfo(double tick) {
		unsigned long long currentTick = Rainbow::Timer::getSystemTick();
		if (m_LastTick == 0) { // First call
			m_StartTick = m_LastTick = currentTick;
		}

		unsigned long long totalElapsed = currentTick - m_StartTick;
		if (totalElapsed >= 5000) { // More than 5000ms (5 seconds) execute once
			
			// Create a Feature collection
			std::vector<Feature> features;

			// 1. Add player positions (Point)
			{
				// 获取所有玩家信息
				std::vector<IClientPlayer*> players;
				if (g_WorldMgr) {
					g_WorldMgr->getAllPlayers(players);

					// 为每个玩家创建一个位置点
					for (size_t i = 0; i < players.size(); i++) {
						IClientPlayer* iplayer = players[i];
						if (!iplayer) continue;

						ClientPlayer* player = iplayer->GetPlayer();
						if (!player) continue;

						// 获取玩家位置
						WCoord playerWCoord = player->getPosition();
						double longitude = playerWCoord.x;
						double latitude = playerWCoord.z ;
						double altitude = playerWCoord.y; // 高度

						GeoPoint playerPos(longitude, latitude, altitude);
						auto geometry = std::make_shared<PointGeometry>(playerPos);

						// 创建玩家Feature
						Feature playerFeature;
						playerFeature.geometry = geometry;
						playerFeature.properties["type"] = "player";
						playerFeature.properties["name"] = player->getNickname();
						playerFeature.properties["uin"] = player->getUin();
						playerFeature.properties["objid"] = (long long)player->getObjId();
						playerFeature.properties["team"] = player->getTeam();

						playerFeature.id = "player_" + std::to_string(player->getUin());

						features.push_back(playerFeature);
					}
				}

			}

			// 2. Add NPC position (Point)			
			{				
				// 获取所有NPC（筛选非玩家角色）				
				
				if (g_WorldMgr) {
					auto* p_world = g_WorldMgr->getWorld(0);
					if (p_world) {
						std::vector<ClientActor*> all_actors;
						p_world->getActorMgr()->ToCastMgr()->getAllLiveActors(all_actors);
						
						// 筛选非玩家角色（NPC、怪物等）
						//for (auto actor : all_actors) {
						//	if (actor && actor->getObjType() != OBJ_TYPE_ROLE) {
						//		npcs_list.push_back(actor);
						//	}
						//}
						 
						// 为每个Actor创建Feature
						for (size_t i = 0; i < all_actors.size(); i++) {
							ClientActor* actor = all_actors[i];
							if (!actor) continue;
							
							// 获取NPC位置
							WCoord npcWCoord = actor->getPosition();
							double longitude = npcWCoord.x;
							double latitude = npcWCoord.z;
							double altitude = npcWCoord.y;
							
							GeoPoint npcPos(longitude, latitude, altitude);
							auto geometry = std::make_shared<PointGeometry>(npcPos);
							
							

							// 创建NPC Feature 
							Feature npcFeature;
							npcFeature.geometry = geometry;
							npcFeature.properties["type"] = "actor";
							npcFeature.properties["obj_type"] = actor->getObjType();
							npcFeature.properties["obj_id"] = (long long)actor->getObjId();
							
							if (actor->getObjType() == 0) {
								ClientMob* mod = dynamic_cast<ClientMob*>(actor);
								const MonsterDef* def = mod->getDef();
								npcFeature.properties["id"] = def->ID;
								npcFeature.properties["name"] =  def->Name.c_str();
								//LOG_INFO("npcFeature:%d Name:%s", def->ID, def->Name.c_str());
							}
							
							features.push_back(npcFeature);
						}
					}
				}
				
			}

			// 3. Add ruin building (Polygon)
			{
				// 获取所有单独建筑数据（包括遗迹建筑）
				if (g_WorldMgr) {
					auto* p_world = g_WorldMgr->getWorld(0);
					if (p_world) {
						// 获取所有单独建筑数据
						auto singleBuilds = p_world->getCityMgr()->GetAllSingleBuildData();
						for (auto& pair : singleBuilds) {
							auto config = GetCityConfigInterface()->getSingleBuildDataByName(pair.first);
							if (!config) continue;
							
							// 处理每个建筑
							auto& buildDatas = pair.second;
							for (auto& build : buildDatas.buildData) {
								// 创建建筑多边形（使用chunk坐标转换为世界坐标）
								std::vector<std::vector<GeoPoint>> rings;
								std::vector<GeoPoint> outerRing;
								
								// 根据建筑的位置和范围创建外环
								// 左下角
								outerRing.push_back(GeoPoint(build.x * CHUNK_BLOCK_X, build.z * CHUNK_BLOCK_Z));
								// 右下角
								outerRing.push_back(GeoPoint((build.x + build.rangeX) * CHUNK_BLOCK_X, build.z * CHUNK_BLOCK_Z));
								// 右上角
								outerRing.push_back(GeoPoint((build.x + build.rangeX) * CHUNK_BLOCK_X, (build.z + build.rangeZ) * CHUNK_BLOCK_Z));
								// 左上角
								outerRing.push_back(GeoPoint(build.x * CHUNK_BLOCK_X, (build.z + build.rangeZ) * CHUNK_BLOCK_Z));
								// 闭合环（重复第一个点）
								outerRing.push_back(GeoPoint(build.x * CHUNK_BLOCK_X, build.z * CHUNK_BLOCK_Z));
								
								rings.push_back(outerRing);
								
								// 创建内环（如果建筑足够大）
								if (build.rangeX > 2 && build.rangeZ > 2) {
									std::vector<GeoPoint> innerRing;
									// 内环左下角（向内缩进1个单位）
									innerRing.push_back(GeoPoint((build.x + 1) * CHUNK_BLOCK_X, (build.z + 1) * CHUNK_BLOCK_Z));
									// 内环右下角
									innerRing.push_back(GeoPoint((build.x + build.rangeX - 1) * CHUNK_BLOCK_X, (build.z + 1) * CHUNK_BLOCK_Z));
									// 内环右上角
									innerRing.push_back(GeoPoint((build.x + build.rangeX - 1) * CHUNK_BLOCK_X, (build.z + build.rangeZ - 1) * CHUNK_BLOCK_Z));
									// 内环左上角
									innerRing.push_back(GeoPoint((build.x + 1) * CHUNK_BLOCK_X, (build.z + build.rangeZ - 1) * CHUNK_BLOCK_Z));
									// 闭合内环
									innerRing.push_back(GeoPoint((build.x + 1) * CHUNK_BLOCK_X, (build.z + 1) * CHUNK_BLOCK_Z));
									
									rings.push_back(innerRing);
								}
								
								auto geometry = std::make_shared<PolygonGeometry>(rings);
								
								// 创建建筑Feature
								Feature buildingFeature;
								buildingFeature.geometry = geometry;
								buildingFeature.properties["type"] = "building";
								buildingFeature.properties["name"] = pair.first;  // 建筑名称
								buildingFeature.properties["position_x"] = build.x;
								buildingFeature.properties["position_z"] = build.z;
								buildingFeature.properties["range_x"] = build.rangeX;
								buildingFeature.properties["range_z"] = build.rangeZ;
								buildingFeature.id = "building_" + pair.first + "_" + std::to_string(build.x) + "_" + std::to_string(build.z);
								
								features.push_back(buildingFeature);
							}
						}
					}
				}
			}

			// 增加空投信息
			{
				//空投宝箱
				auto& chest_list = AirDropEvent::chestList;
				for (auto& chest : chest_list) {
					// 创建空投箱子位置点					
					GeoPoint chestPos(chest.pos.x, chest.pos.z, chest.pos.y);
					auto geometry = std::make_shared<PointGeometry>(chestPos);
					
					// 创建空投箱Feature
					Feature chestFeature;
					chestFeature.geometry = geometry;
					chestFeature.properties["type"] = "airdrop";
					chestFeature.properties["name"] = "airdrop_chest";
 
					// 使用唯一ID
					chestFeature.id = "airdrop_chest" + std::to_string(chest.event_id) + "_" + std::to_string(chest.pos.x) + "_" + std::to_string(chest.pos.z);
					
					features.push_back(chestFeature);
				}

				//空投路线
				PluginManager* pluginManager = GetPluginManagerPtr();
				WorldEventManager* worldEventManager = pluginManager->FindSubsystem<WorldEventManager>();
				if (worldEventManager)
				{
					 
					std::vector<WorldEvent*> events = worldEventManager->GetActiveEvents();
					for (auto event : events) {
						if (event->GetEventType().find("airdrop") != std::string::npos) {
							AirDropEvent* airDropEvent = dynamic_cast<AirDropEvent*>(event);
							if (airDropEvent) {

								const Rainbow::Vector3f& startPos = airDropEvent->GetStartLocation();
								const Rainbow::Vector3f& endPos = airDropEvent->GetEndLocation();
								const Rainbow::Vector3f& dropPos = airDropEvent->GetDropLocation();
								const Rainbow::Vector3f& currPos = airDropEvent->GetCurrLocation();

								GeoPoint startGeo(startPos.x,  startPos.z, startPos.y);
								GeoPoint endGeo(endPos.x, endPos.z, endPos.y);
								GeoPoint dropGeo(dropPos.x, dropPos.z, dropPos.y);
								GeoPoint currGeo(currPos.x, currPos.z, currPos.y);

								std::vector<GeoPoint> linePoints = {startGeo, endGeo};							
								Feature lineFeature;
								lineFeature.geometry = std::make_shared<LineStringGeometry>(linePoints);
								lineFeature.properties["type"] = "airdrop";
								lineFeature.properties["name"] = "airdrop_line";

								lineFeature.id = "airdrop_line" + std::to_string(airDropEvent->GetEventID())+ "_" + std::to_string(dropPos.x) + "_" + std::to_string(dropPos.z);

								features.push_back(lineFeature);


								// 飞机位置点
								Feature craftFeature;
								craftFeature.geometry = std::make_shared<PointGeometry>(currGeo);;
								craftFeature.properties["type"] = "airdrop";
								craftFeature.properties["name"] = "airdrop_craft";

								craftFeature.id = "airdrop_craft" + std::to_string(airDropEvent->GetEventID())+ "_" + std::to_string(dropPos.x) + "_" + std::to_string(dropPos.z);
								features.push_back(craftFeature);
								
								//空投位置点
								Feature dropFeature;
								dropFeature.geometry = std::make_shared<PointGeometry>(dropGeo);;
								dropFeature.properties["type"] = "airdrop";
								dropFeature.properties["name"] = "airdrop_drop";

								dropFeature.id = "airdrop_drop" + std::to_string(airDropEvent->GetEventID())+ "_" + std::to_string(dropPos.x) + "_" + std::to_string(dropPos.z);
								features.push_back(dropFeature);
							}
						}
					}
				}

			}

			// 获取宝箱地理信息
			{
				WorldManager* worldMgr = GetWorldManagerPtr();
				if (worldMgr && worldMgr->getWorld(0)) {
					ChestManager* chestMgr = worldMgr->getWorld(0)->GetChestMgr();
					if (chestMgr) {
						const std::vector<SocChestInfo>& chests = chestMgr->getChests();
						for (auto& chest : chests) {
							if (chest.isSpawned) {
								GeoPoint chestPos(chest.pos.x, chest.pos.z, chest.pos.y);
								auto geometry = std::make_shared<PointGeometry>(chestPos);

								Feature chestFeature;
								chestFeature.geometry = geometry;
								chestFeature.properties["type"] = "chest";
								chestFeature.properties["name"] = "chest";
								chestFeature.properties["obj_type"] = chest.chestId;
								chestFeature.id = "chest_" + std::to_string(chest.pos.x) + "_" + std::to_string(chest.pos.z);
								features.push_back(chestFeature);
							}
						}
					}
				}

			}

			WorldManager* worldMgr = GetWorldManagerPtr();
			World* pWorld = worldMgr->getWorld(0);
			long long owid = worldMgr->getWorldId();


			// 获取房间ID
			std::string roomId = GetClientInfoProxy()->getEnterParam("room_id");			
			std::string roomName = roomId;						
			std::string trans_msg = GetClientInfoProxy()->getEnterParam("trans_msg");			
			//trans_msg={"room_desc":"map-geo-test","room_labels":{"clear_type":1,"difficulty":1,"play_type":1,"server_category":1},"room_name":"map-geo-test"} 			
			if (trans_msg != "") {			
				try {
					HttpSign sign;
					std::string msg = sign.urlDecode(trans_msg);
					json transJson = json::parse(msg);
					if (transJson.contains("room_name")) {
						roomName = transJson["room_name"];
					}
				} catch (const std::exception& e) {
					// 处理异常情况，例如记录日志
					LOG_WARNING("JSON解析失败: %s", e.what());
					LOG_WARNING("JSON解析失败: %s", trans_msg.c_str());
				}
			}

			// 获取地图ID
			std::string mapId = GetClientInfoProxy()->getEnterParam("toloadmapid");
			std::string mapVer = g_zmqMgr->GetMapVer();
			

			std::string serverIP = GetClientInfoProxy()->getEnterParam("ip");
			int serverPort = atoi(GetClientInfoProxy()->getEnterParam("port"));

			// 获取地图略缩图URL
			char pixelurl[256];
			MINIW::ScriptVM::game()->callFunction("GetPixelMapUrl", ">s", pixelurl);

			// 获取地图边界
			// provider 有这四个函数获取边界，数值是chunk坐标，需要转换成block坐标
			auto provider = pWorld->getChunkProvider();
			int chunkXMin = provider->getStartChunkX();
			int chunkXMax = provider->getEndChunkX();
			
			int chunkZMin = provider->getStartChunkZ();
			int chunkZMax = provider->getEndChunkZ();

			// 转换为block坐标
			int mapMaxWidth = abs((chunkXMax - chunkXMin + 1) * CHUNK_BLOCK_X);
			int mapMaxHeight = abs((chunkZMax - chunkZMin + 1) * CHUNK_BLOCK_Z);
			
			// 将所有Feature转换为FeatureCollection JSON
			json featureCollection = GeoJsonParser::featureCollectionToJson(features);
			
			featureCollection["pixelurl"] = std::string(pixelurl);
			featureCollection["roomid"] = roomId;
			featureCollection["roomName"] = roomName;
			featureCollection["mapid"] = mapId;
			featureCollection["serverIP"] = serverIP;
			featureCollection["serverPort"] = serverPort;
			featureCollection["ts"] = time(0);
			featureCollection["mapMaxWidth"] = mapMaxWidth;
			featureCollection["mapMaxHeight"] = mapMaxHeight;
			featureCollection["mapMaxX"] = chunkXMax * CHUNK_BLOCK_X;
			featureCollection["mapMaxZ"] = chunkZMax * CHUNK_BLOCK_Z;
			featureCollection["mapMinX"] = chunkXMin * CHUNK_BLOCK_X;
			featureCollection["mapMinZ"] = chunkZMin * CHUNK_BLOCK_Z;

			// 输出JSON字符串（不使用美化格式，节省空间）
			std::string jsonStr = GeoJsonParser::stringify(featureCollection, false);
			
			//LOG_INFO("MapGeoInfo: %s", jsonStr.c_str());
			// 记录原始大小
			size_t originalSize = jsonStr.size();
			
			// 使用zlib压缩数据
			unsigned long compressedLen = 0;
			void* compressedData = MINIW::compressBinaryAllocated((void*)jsonStr.c_str(), jsonStr.size(), compressedLen);
			
			// 输出压缩后的日志
			LOG_INFO("MapGeoInfo: originalSize:%d, compressedSize:%d", originalSize, compressedLen);

			// 重置计时器
			m_StartTick = currentTick;


			if (g_zmqMgr){

				miniw::map_geo_data data;
				data.set_mapid(mapId);
				data.set_map_ver(mapVer);
				data.set_roomid(roomId);
				data.set_worldid(std::to_string(owid));
				data.set_server_ip(serverIP);
				data.set_server_port(serverPort);
				data.set_geo_data(compressedData, compressedLen);
				data.set_ts(time(0));

				std::string savedata;
				data.SerializeToString(&savedata);

				g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveMapGeoData, savedata.c_str(), savedata.length(),  owid, 10000);
			}
		}

		m_LastTick = currentTick;
	}
}


#if DEDICATED_SERVER
void checkAndSetLogPath()
{
	#if   PLATFORM_LINUX
	static unsigned tick_count = 0;
	static time_t last_set_timestamp = 0;

	++ tick_count;
	if (last_set_timestamp != 0 && tick_count % 1200 != 0)
		return;

	time_t now = MINIW::GetTimeStamp();
	#define DIFF_FILE_INTERVAL (3600 * 8)
	if (now / DIFF_FILE_INTERVAL != last_set_timestamp / DIFF_FILE_INTERVAL)  // 小时数不同, 未考虑半个时区的情况
	{
		tm * timeinfo = localtime(&now);

		char log_file_path[1024] = {0x00};
			char *currentDir = get_current_dir_name();
			// 房主uin_房间ID_月日_时
			snprintf(log_file_path, sizeof(log_file_path), "%s/logs/%s_%s_%02d%02d_%02d%02d.log"
				, get_current_dir_name()
				, GetClientInfoProxy()->getEnterParam("account")
				, GetClientInfoProxy()->getEnterParam("room_id")
				, timeinfo->tm_mon + 1
				, timeinfo->tm_mday
				, timeinfo->tm_hour
				, timeinfo->tm_min
			);
			free(currentDir);

		ResetConsoleLog(log_file_path);
		last_set_timestamp = now;
	}
	#endif
}
#endif // DEDICATED_SERVER

namespace Rainbow
{
	void LinuxGameApp::SystemInit(const char* cmdStr)
	{
		ClientApp::SystemInit(cmdStr);

		auto cliInfo = GetClientInfo();
		m_WriteLog = strcmp(GetClientInfo()->getEnterParam("rent_lua_log"), "1") == 0;
		GetLogSystem().SetLogType(kLogTypeDebug);

#if DEDICATED_SERVER
		if (m_WriteLog)
			checkAndSetLogPath();
#endif
		const char* assetPath = GetAssetProjectSetting().GetAssetPath();

		GetGameInfo().SetRoomHostType(ROOM_SERVER_RENT);
#if PLATFORM_LINUX
		printf("lpCmdLine = %s\n", cmdStr);
		printf("maptag=%s\n", cliInfo->getEnterParam("maptag"));
		std::string pid = std::string("./pid/") + cliInfo->getAccount();
		std::string room_id_ = cliInfo->getEnterParam("room_id");
		if (room_id_.length() > 0) {
			pid += "_";
			pid += room_id_;
			//clientmgr->setRoomHostType(ROOM_SERVER_RENT);         //玩家租赁服
		}
		pid += ".pid";

		std::string path = "mini";
		std::string data_file_dir_ = cliInfo->getEnterParam("data_file_dir");
		if (data_file_dir_.length() > 0) {
			path = data_file_dir_ + "mini";    //是否有data_file_dir
		}

		path += cliInfo->getAccount();

		if (room_id_.length() > 0) {
			path += "_";
			path += room_id_;
		}
		path += "/";
		mkdir(path.c_str(), 0755);
		m_WritePathDir = path;

		auto clientmgr = GetClientInfo();
		if (daemon_init(pid.c_str()) == 0)
		{
			//if (!clientmgr->onInitialize(NULL, path.c_str(), 0, 0, NULL))
			//{
			//	printf("clientmgr->create error.\n");
			//	return 0;
			//}
			m_pid = pid;
			signal(SIGPIPE, SIG_IGN);
			signal(SIGINT, sign_handler);
			signal(SIGTERM, sign_handler);
			signal(SIGUSR1, check_param_handler);
			signal(SIGUSR2, check_param_handler2);

		}
		else
		{
			printf("miniworldserver already start.\n");
		}
#endif

#ifdef WINDOWS_SERVER
		// windows上服务器跟客户端可能在一起，避免数据冲突，修改以下写入目录
		m_WritePathDir = m_WritePathDir + "winserver/";
#endif

		// linux 需要使用房间id做目录，否则同地图会有数据冲突
		SimpleSLOG("LinuxGameApp use writepath :%s", m_WritePathDir.c_str());
		// systeminit已经添加，这里要删除再重新添加
		GetFileManager().RemovePackage(BUILTIN_WRITE_PATH_PKGNAME);
		FileSystemEntry fileEntry(m_WritePathDir.c_str());
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, BUILTIN_WRITE_PATH_PKGNAME, fileEntry.Path(), 10001);
	}

	void LinuxGameApp::GameExit(bool restart)
	{
		ClientApp::GameExit(restart);
#if PLATFORM_LINUX
		MINIW::GameExit(restart, m_pid.c_str());
#endif
	}

	//每一帧的后面执行,在渲染之后
	void LinuxGameApp::AppFrameTickEnd()
	{
		if (m_DoFrame) {
#if DEBUG_MODE
			GetTransformManager().m_DEBUG_NoDirtyAnyMore = false;
#endif
			Rainbow::GlobalCallbacks::Get().endFrameTick.Invoke();
			//Object对象的GC
//资源GC
			AssetManager& resMgr = GetAssetManager();
			resMgr.GCResouces();
			resMgr.ChkBatchGCResouces();

#if ENABLE_MEMORY_MANAGER
			GetMemoryManager().FrameMaintenance();
#endif
		}

		//帧率的时间等待
		GetFrameTimeManager().Sync(FrameTimeManager::AfterPlayerLoop);
	}

//#define USING_DEBUG_BUILD_PKG
	void LinuxGameApp::OnPKGInit()
	{
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "root", "", 1, FILETYPE_OUTSIDE_ASSET);
		GetIWorldConfig().LoadWorldCfg();
#ifdef STUDIO_SERVER
		core::string assetScriptPath;
		int nGameEnv = GetIWorldConfig().getGameData("game_env");
		if (nGameEnv >= 10)
		{
			assetScriptPath = "../OverseaScript/Script/";
			Rainbow::GetAssetProjectSetting().SetAdditionalAssetPath("../OverseaAssets/Assets/");
		}
		else
		{
			assetScriptPath = "../DomesticScript/Script/";
			Rainbow::GetAssetProjectSetting().SetAdditionalAssetPath("../DomesticAssets/Assets/");
		}

		const char* assetPath = Rainbow::GetAssetProjectSetting().GetAdditionalAssetPath();
#else
		const char* assetPath = GetAssetProjectSetting().GetAssetPath();
		core::string assetScriptPath = AppendPathName(assetPath, "../Script/");
#endif
		core::string engineRes = GetAssetProjectSetting().GetEngineResourcePath();
		core::string engineRes1 = engineRes + "Assets/";
		core::string engineRes2 = engineRes + "Assets/Resources/";
		core::string resourcePath = assetPath;
		resourcePath = resourcePath + "Resources/";

	    core::string miniGameRes       = AppendPathName(GetAssetProjectSetting().GetEngineResourcePath(), "Assets/Resources/minigame");
	    core::string miniGamePrefabRes = AppendPathName(GetAssetProjectSetting().GetEngineResourcePath(), "Assets/Resources/minigame/prefab");
	    core::string prefabPath        = resourcePath + "prefab/";

	    GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets", assetPath, 5, FILETYPE_SERIALIZE_FILE);

	    GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources", resourcePath.c_str(), 2, FILETYPE_SERIALIZE_FILE);
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Assets/", engineRes1.c_str(), 3, FILETYPE_SERIALIZE_FILE);
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Assets/Resources", engineRes2.c_str(), 4, FILETYPE_SERIALIZE_FILE);
	    GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Assets/Resources/minigame", miniGameRes.c_str(), 4, Rainbow::FILETYPE_SERIALIZE_FILE);
        GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Assets/Resources/minigame/prefab", miniGamePrefabRes.c_str(), 4, Rainbow::FILETYPE_SERIALIZE_FILE);

		// GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "EngineResource/Assets/", engineRes1.c_str(), 3, FILETYPE_SERIALIZE_FILE);
		// GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "EngineResource/Assets/Resources", engineRes2.c_str(), 2, FILETYPE_SERIALIZE_FILE);
		// GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Script", scriptPath.c_str(), 6, FILETYPE_NONE);
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources/prefab", prefabPath.c_str(), 10, FILETYPE_SERIALIZE_FILE);
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "AssetScript", assetScriptPath.c_str(), 6, FILETYPE_OUTSIDE_ASSET);

		//子项目路径
		GameSetting& gameSetting = GetGameSetting();
		gameSetting.InitCurSubprojectInfo(GetIWorldConfig().getGameData("game_env"));
		core::string subprojectScript = assetScriptPath + gameSetting.m_CurSubprojectInfo.pkgPathPrefix;
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Script/Sub", subprojectScript.c_str(), 7, FILETYPE_SERIALIZE_FILE);
		core::string subprojectAssets = resourcePath + gameSetting.m_CurSubprojectInfo.pkgPathPrefix;
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources/Sub", subprojectAssets.c_str(), 8, FILETYPE_SERIALIZE_FILE);
		core::string engineRes3 = engineRes + "Script/";
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Script", engineRes3.c_str(), 6, FILETYPE_SERIALIZE_FILE);
#ifdef STUDIO_SERVER
		engineRes2 += "/ministudio";
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Assets/Resources/ministudio", engineRes2.c_str(), 4, FILETYPE_SERIALIZE_FILE);
		const char* firstpkgPath = gameSetting.m_FirstPkg.pkgFilePath.c_str();
		if (GetFileManager().IsFileExist(firstpkgPath))
		{
			FilePkgBase* first = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, gameSetting.m_FirstPkg.name.c_str(), firstpkgPath, gameSetting.m_FirstPkg.priority, FILETYPE_SERIALIZE_FILE);
			first->SetFilePrefix(gameSetting.m_FirstPkg.filePrefix);
		}

		if (GetFileManager().IsFileExist(gameSetting.m_EnginePkg.pkgFilePath.c_str()))
		{
			FilePkgBase* engine = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, gameSetting.m_EnginePkg.name.c_str(), gameSetting.m_EnginePkg.pkgFilePath.c_str(), gameSetting.m_EnginePkg.priority, FILETYPE_SERIALIZE_FILE);
			engine->SetFilePrefix(gameSetting.m_EnginePkg.filePrefix);
		}

		if (GetFileManager().IsFileExist(gameSetting.m_StudioPkg.pkgFilePath.c_str()))
		{
			FilePkgBase* studioPkg = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, gameSetting.m_StudioPkg.name.c_str(), gameSetting.m_StudioPkg.pkgFilePath.c_str(), gameSetting.m_StudioPkg.priority, FILETYPE_SERIALIZE_FILE);
			studioPkg->SetFilePrefix(gameSetting.m_StudioPkg.filePrefix);
		}

		dynamic_array<GamePackageInfo>& infoList = gameSetting.m_PkgLists;
		for (int i = 0; i < infoList.size(); i++) {
			GamePackageInfo& info = infoList[i];
			if (GetFileManager().IsFileExist(info.pkgFilePath.c_str()))
			{
				FilePkgBase* studioPkg = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, info.name.c_str(), info.pkgFilePath.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
				studioPkg->SetFilePrefix(info.filePrefix);
			}
		}

#endif
#ifdef IWORLD_UNIVERSE_BUILD
			core::string modulesSubAssets = modulesAssets + gameSetting.m_CurSubprojectInfo.pkgPathPrefix;
			GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources/modules_sub", modulesSubAssets.c_str(), 8, FILETYPE_SERIALIZE_FILE);
			core::string remotesSubAssets = remotesAssets + gameSetting.m_CurSubprojectInfo.pkgPathPrefix;
			GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources/remotes_sub", remotesSubAssets.c_str(), 8, FILETYPE_SERIALIZE_FILE);
#endif
    }

	void LinuxGameApp::AppInit()
	{
		StandaloneServer::s_LoadBeginTime = Rainbow::Timer::getTimeUS();
#ifdef WINDOWS_SERVER
		// 检测启动参数
		std::string mapid = GetClientInfoProxy()->getEnterParam("toloadmapid");
		std::string gameenv = GetClientInfoProxy()->getEnterParam("game_env");
		if (mapid.empty() || gameenv.empty())
		{
			WarningStringMsg("you need setup VS debug cmd param");
			MINIW::PopMessageBox("you need setup VS debug cmd param", "server error");
			exit(1);
			return;
	}
#endif
		//RegisterShaderLibraryClass();

		ClientApp::AppInit();

#ifdef OGRE_USE_CEF3_LIB
		MINIW::GetCef3ManagerPtr()->InitializeCef();
#endif

	}

	void LinuxGameApp::BeginPlay()
	{
		ClientApp::BeginPlay();
	}


	void LinuxGameApp::AppFrameTick()
	{
		static bool quit = false;
		if (quit)
			return;

		MINIW::ClientApp::AppFrameTick();

		if (g_bGameQuit)
		{
			GameExit(false);
			Rainbow::GetGameApp().AppExit();
			quit = true;
			//exit(0);
		}
	}

	void LinuxGameApp::OnTick(float dt)
	{
		unsigned dtick = dt * 1000;


		MINIW::ClientApp::OnTick(dt);

#if DEDICATED_SERVER
		if (m_WriteLog)
			checkAndSetLogPath();
#endif
		if (IsServerInitOk())
		{
			SimulateMgr::getInstance()->Tick(dtick);
			LiveStreamMgr::getInstance()->Tick(dtick);
			TestOutPut::ShowServerFps(dt);
			MapOutPut::ShowGeoInfo(dt);
		}
	}

	void LinuxGameApp::AppExit()
	{
		SimpleSLOG("LinuxGameApp start AppExit");
		ClientApp::AppExit();
		SimpleSLOG("LinuxGameApp AppExit finish");
	}

	void LinuxGameApp::setSIGUSR2() {
		m_HasSIGUSR2 = true;
	}
	void LinuxGameApp::handleSIGUSR2() {
		if (g_zmqMgr)
			g_zmqMgr->CloseRoom(3, "");
	}
	bool LinuxGameApp::hasSIGUSR2() {
		return m_HasSIGUSR2;
	}

	void LinuxGameApp::OnServerFrameTick()
	{
		if (hasSIGUSR2()) {
			m_HasSIGUSR2 = false;
#if PLATFORM_LINUX
			if (getuid() == 0)
			{
				LOG_INFO("get sig usr2, root ignore it");
				return;
			}
#endif

			GetLuaInterface().callLuaString("__handle_SIGUSR2__()");
		}
		if (g_zmqMgr && g_zmqMgr->CheckAutoExit())
		{
			GetLuaInterface().callLuaString("__handle_autoexit__()");
			ClientInfo_Service* pClientInfo = dynamic_cast<ClientInfo_Service*>(GetClientInfo());
			if (pClientInfo)
			{
				pClientInfo->setClosing();
			}
		}
	}
}
