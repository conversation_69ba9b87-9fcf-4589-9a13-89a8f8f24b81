#include "MonsterCsv.h" 
#include "OgreUtils.h"
#include "Common/OgreStringUtil.h"
#include "defmanager.h"
#include "ModManager.h"
#include "ModEditorManager.h"

using MINIW::CSVParser;
IMPLEMENT_LAZY_SINGLETON(MonsterCsv)
extern bool gFunc_IsDomesticVer();

MonsterCsv::MonsterCsv() 
{
	m_Monsters.clear();
}

MonsterCsv::~MonsterCsv() 
{ 
	onClear();
}

void PraseItemVal(const std::string& configstr, std::vector<ItemDropNewVal>& dropconf)
{
	std::vector<core::string> elements = Rainbow::StringUtil::split(configstr.c_str(), "|");
	for (auto& elem : elements)
	{
		std::vector<core::string> vals = Rainbow::StringUtil::split(elem, ",");
		if (vals.size() == 2)
		{
			ItemDropNewVal dropval;
			dropval.v1 = atoi(vals[0].c_str());
			dropval.v2 = atoi(vals[1].c_str());
			dropconf.push_back(dropval);
		}
	}
}

// Add the skinning drop items parsing function after PraseItemVal
std::vector<SkinningDropItem> parseSkinningDropItems(const std::string& input) {
	std::vector<SkinningDropItem> result;
	
	if (input.empty()) return result;
	
	std::string trimmed = input.substr(1, input.size() - 2);
	std::stringstream ss(trimmed);
	std::string item;

	while (std::getline(ss, item, '}')) { // 读取到 '}'
		if (item.empty()) continue;

		// 查找数据开始位置 '{'，并提取相关字符串
		size_t start = item.find('{');
		if (start != std::string::npos) {
			item = item.substr(start + 1); // 去掉 '{'

			std::stringstream dataStream(item);
			
			char delimiter;
			SkinningDropItem data;
			data.id = 0;
			data.num = 0;
			data.odds = 0;
			data.toolEfficiency = false;

			dataStream >> data.id >> delimiter
				>> data.num >> delimiter
				>> data.odds >> delimiter >> data.toolEfficiency;

			result.push_back(data);
		}
	}
	return result;
}

void MonsterCsv::onParse(CSVParser& parser) 
{ 
	char tmpname[64];

	parser.SetTitleLine(1);
	bool isDomestic = gFunc_IsDomesticVer();
	int numLines = parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id == 0) continue;

		int regionType = parser[i]["RegionType"].Int();
		if ((isDomestic && regionType == REGION_TYPE_UNIVERSE) || (!isDomestic && regionType == REGION_TYPE_DOMESTIC)) 
			continue;

		MonsterDef def;
		def.ID = id;
		def.ParentID = 0;
		def.ScriptDisable = 0;
		def.BabyID = parser[i]["BabyID"].Int();
		def.TextureID = parser[i]["TextureID"].Int();
		memset(def.SpawnWeight, 0, sizeof(def.SpawnWeight));
// 		def.Texture[0] =0;
// 		def.InteractionFunction[0] = 0;
		def.DevShopDisPlay = false;
		def.OpenStoreConfig = "";
		def.Name = ColumnLang(parser[i],"Name");
		def.Icon = parser[i]["Icon"].Str();
		def.EnglishName = parser[i]["ENName"].Str();
		def.Model = parser[i]["Model"].Str();
		def.ModelType = 0;
		def.Type = parser[i]["Type"].Int();
		def.EditType = parser[i]["EditType"].Int();
		def.IsTemplate = parser[i]["IsTemplate"].Int() > 0;
		def.Nature = parser[i]["Nature"].Int();
		def.TickPeriod = parser[i]["TickPeriod"].Int();
		def.TickScript = parser[i]["TickScript"].Str();
		def.Life = parser[i]["Life"].Int();
		def.LifeIncrease = parser[i]["LifeIncrease"].Float();
		def.MinRandomScale = parser[i]["MinRandomScale"].Float();
		if (def.MinRandomScale == 0)
		{
			def.MinRandomScale = 1;
		}
		// ��������0-9����ս��Զ�̡���ը���������𡢶������ҡ��硢����ħ����
		def.Armors[0] = parser[i]["ArmorPunch"].Short();
		def.Armors[1] = parser[i]["ArmorRange"].Short();
		def.Armors[2] = parser[i]["ArmorExplode"].Short();
		def.Armors[3] = parser[i]["ArmorPhysics"].Short();
		def.Armors[4] = parser[i]["ArmorFire"].Short();
		def.Armors[5] = parser[i]["ArmorPoison"].Short();
		def.Armors[6] = parser[i]["ArmorWither"].Short();
		def.Armors[7] = 0; // Ĭ�ϵ����0����δͶ����˺��ͷ���
		def.Armors[8] = parser[i]["ArmorIce"].Short();
		def.Armors[9] = parser[i]["ArmorMagic"].Short();

		def.ArmorIncrease = parser[i]["ArmorIncrease"].Float();
		def.AttackType = parser[i]["AttackType"].Short();
		// ��������0-9����ս��Զ�̡���ը���������𡢶������ҡ��硢����ħ����
		def.Attacks[0] = 0;
		def.Attacks[1] = 0;
		def.Attacks[2] = 0;
		// ��ս
		if (def.AttackType == 0)
		{
			def.Attacks[0] = parser[i]["Attack"].Short();
		}
		// Զ��
		else if (def.AttackType == 1)
		{
			def.Attacks[1] = parser[i]["Attack"].Short();
		}
		// ��ը
		else if (def.AttackType == 2)
		{
			def.Attacks[2] = parser[i]["Attack"].Short();
		}
		def.Attacks[3] = 0;
		def.Attacks[4] = parser[i]["AttackFire"].Short();
		def.Attacks[5] = parser[i]["AttackPoison"].Short();
		def.Attacks[6] = parser[i]["AttackWither"].Short();
		def.Attacks[7] = parser[i]["AttackFlash"].Short();
		def.Attacks[8] = parser[i]["AttackIce"].Short();
		def.Attacks[9] = parser[i]["AttackMagic"].Short();

		def.TouReduce = parser[i]["TouReduce"].Int();
		// ����Ĭ����5���߻�:cjt��
		if (def.TouReduce == 0)
		{
			def.TouReduce = 5;
		}
		def.AttackIncrease = parser[i]["AttackIncrease"].Float();
		def.AttackAnimTicks = parser[i]["AttackAnimTicks"].Short();
		def.Height = parser[i]["Height"].Int();
		def.Mass = parser[i]["Mass"].Int();
		def.Width = parser[i]["Width"].Int();
		def.Thickness = parser[i]["Thickness"].Int();
		def.HitHeight = parser[i]["HitHeight"].Int();
		def.HitWidth = parser[i]["HitWidth"].Int();
		def.HitThickness = parser[i]["HitThickness"].Int();

		GetDefManager().ParseTypeCollides(parser[i]["CollideBoxs"].Str(), def.CollideBoxs);

		def.AttackDistance = (float)parser[i]["AttackDistance"].Int();
		def.ViewDistance = parser[i]["ViewDistance"].Int();
		def.Speed = parser[i]["Speed"].Int();
		def.SpawnMaxLight = parser[i]["SpawnMaxLight"].Int();
		def.SpawnSunLight = parser[i]["SpawnSunLight"].Int();
		def.SpawnMinHeight = parser[i]["SpawnMinHeight"].Int();
		def.SpawnMaxHeight = parser[i]["SpawnMaxHeight"].Int();
		if(def.SpawnMinHeight==0 && def.SpawnMaxHeight==0) def.SpawnMaxHeight = 255;
		def.SpawnMinTime = parser[i]["SpawnMinTime"].Int();
		def.SpawnMaxTime = parser[i]["SpawnMaxTime"].Int();
		
		// ���¶Ȳ���ʱ��Ĭ�ϸ�0 �����¶�У��ʧ�� code-by:lizb
		if (parser[i]["SpawnMinTemperature"].String().empty())
			def.SpawnMinTemperature = TEMPERATURE_MIN_ICE;
		else
			def.SpawnMinTemperature = parser[i]["SpawnMinTemperature"].Int();

		if (parser[i]["SpawnMaxTemperature"].String().empty())
			def.SpawnMaxTemperature = TEMPERATURE_MAX_HEAT;
		else
			def.SpawnMaxTemperature = parser[i]["SpawnMaxTemperature"].Int();
		
		def.NumLimit = parser[i]["NumLimit"].Int();
		def.PackNum = parser[i]["PackNum"].Int();

		def.PickItemOdds = parser[i]["PickItemOdds"].Int();
		def.EquipGroup[0] = parser[i]["EquipGroup"].Int();
		def.EquipOdds = parser[i]["EquipOdds"].Int();

		// for(int n=0; n<MAX_MONSTER_DROPGROUP; n++)
		// {
		// 	sprintf(tmpname, "DropGroup%d", n+1);
		// 	def.DropGroup[n] = parser[i][tmpname].Int();
		// 	sprintf(tmpname, "DropGroupOdds%d", n+1);
		// 	def.DropGroupOdds[n] = parser[i][tmpname].Int();
		// }

		std::string dropitems = parser[i]["DropItemsNew"].Str();
		std::string dropitemsOdds = parser[i]["DropItemsOddsNew"].Str();
		if (!dropitems.empty() && !dropitemsOdds.empty())
		{
			PraseItemVal(dropitems, def.DropItemsNew);           // ����id - ���� ÿ��һ��
			PraseItemVal(dropitemsOdds, def.DropItemsOddsNew);   // ����-����
		}

		for(int n=0; n<MAX_MONSTER_DROPITEM; n++)
		{
			sprintf(tmpname, "DropItem%d", n+1);
			def.DropItem[n] = parser[i][tmpname].Int();
			sprintf(tmpname, "DropItemOdds%d", n+1);
			def.DropItemOdds[n] = parser[i][tmpname].Int();
			sprintf(tmpname, "DropItemNum%d", n+1);
			def.DropItemNum[n] = Rainbow::Max(parser[i][tmpname].Int(), 1);
		}
		def.BurnDropItem = parser[i]["BurnDropItem"].Int();
		def.BurnDropItemOdds = parser[i]["BurnDropItemOdds"].Int();
		def.FeedItem = parser[i]["FeedItem"].Int();
		def.FeedOdds = parser[i]["FeedOdds"].Int();
		def.DropExp = parser[i]["DropExp"].Int();
		def.DropExpOdds = parser[i]["DropExpOdds"].Int();
		def.LevelExp = parser[i]["LvExp"].Int();

		def.ActiveAtk = parser[i]["ActiveAtk"].Int();
		def.CanTame = parser[i]["CanTame"].Bool();
		def.CanBreed = parser[i]["CanBreed"].Bool();
		def.CanRide = parser[i]["CanRide"].Bool();
		def.CanTalk = parser[i]["CanTalk"].Bool();
		def.IsPlayerCustom = false;
		//Wait to add colume to monster.csv --- rivershen 2017.2.14
		//def.AvoidWater = parser[i]["AvoidWater"].Bool();
		def.AvoidWater = false;
		//Ŀǰֻ�б�������1
		def.PathHide = 0;
		def.SunHurt = false;
		def.HasAvatar = true;
		def.CanPassClosedWoodenDoors = false;

		def.ModelScale = parser[i]["ModelScale"].Float();

		def.KillScore = parser[i]["KillScore"].Float();
		def.BreedScore = parser[i]["BreedScore"].Float();
		def.TameScore = parser[i]["TameScore"].Float();
		def.SkinningTime = parser[i]["SkinningTime"].Float();
		def.CorpseTime = parser[i]["CorpseTime"].Float();
		def.CopyID = 0;
		def.TeamID = parser[i]["TeamID"].Int();
		def.gamemod = NULL;
		def.BuffId = parser[i]["BuffId"].Int();
		def.BornBuff = parser[i]["BornBuff"].Int();
		def.NameDisPlay = parser[i]["NameDisPlay"].Int() > 0;
		def.DescDisplay = parser[i]["DescDisplay"].Int() > 0;
		def.Desc = ColumnLang(parser[i],"Desc");
		def.BagNum = parser[i]["BagNum"].Int();
		def.Food = parser[i]["Food"].Int();
		def.FoodReduce = parser[i]["FoodReduce"].Int();
		def.TriggerType = parser[i]["TriggerType"].Int();

		def.Dialogue = parser[i]["Dialogue"].Str();
		def.CanToEgg = parser[i]["BecomeEgg"].Bool();
		def.AIConfig = parser[i]["AIConfig"].Str();

		def.HurtSound = parser[i]["HurtSound"].Str();
		def.DeathSound = parser[i]["DeathSound"].Str();
		def.SaySound = parser[i]["SaySound"].Str();
		def.StepSound = parser[i]["StepSound"].Str();
		def.AttackSound = parser[i]["AttackSound"].Str();
		def.AttackStopSound = parser[i]["AttackStopSound"].Str();
		def.AttackSound2 = parser[i]["AttackSound2"].Str();
		def.AttackStopSound2 = parser[i]["AttackStopSound2"].Str();
		def.Effect = parser[i]["Effect"].Str();
		def.ScriptDisable = parser[i]["ScriptDisable"].Int();

		def.TurningSpeed = parser[i]["TurningSpeed"].Float();
		if (def.TurningSpeed == 0) def.TurningSpeed = 30.0f;
		def.Toughness = parser[i]["Toughness"].Int();
		def.ToughnessRecover = parser[i]["ToughnessRecover"].Int();
		def.RepelRes = parser[i]["RepelRes"].Int();

		def.PreScale = parser[i]["PreScale"].Float();
		if (def.PreScale == 0)
			def.PreScale = 1.0;

		def.AlwaysUpdate = 0;
		if(parser[i].hasColumn("AlwaysUpdate"))
		def.AlwaysUpdate = parser[i]["AlwaysUpdate"].Int();

		def.BreedType = parser[i]["Breed"].Int();
		std::vector<core::string> str = Rainbow::StringUtil::split(parser[i]["Fodder"].Str(), ",");
		for (unsigned int j = 0; j < 2; j++)
		{
			if (j < str.size())
			{
				def.Fodder[j] = atoi(str[j].c_str());
			}
			else
			{
				def.Fodder[j] = 0;
			}
		}
		def.TemperatureDefense = parser[i]["TemperatureDefense"].Float();

		std::string skinningDropStr = parser[i]["SkinningDropItems"].Str();
		if (!skinningDropStr.empty()) {
			def.SkinningDropItems = parseSkinningDropItems(skinningDropStr);
		}

		m_Monsters.AddRecord(def.ID, def);
	}

	auto iter = m_Monsters.m_Records.begin();
	for(; iter!=m_Monsters.m_Records.end(); iter++)
	{
		if(iter->second.BabyID > 0)
		{
			MonsterDef *babydef = m_Monsters.GetRecord(iter->second.BabyID);
			assert(babydef);
			babydef->ParentID = iter->first;
		}

		m_MonsterTable.push_back(&iter->second);
	}
	
	g_DefMgr.resetCrcCode(CRCCODE_MONSTER);
} 
void MonsterCsv::onClear() 
{ 
} 
const char* MonsterCsv::getName() 
{ 
    return "monster"; 
} 
const char* MonsterCsv::getClassName() 
{ 
    return "MonsterCsv"; 
} 
int MonsterCsv::getNum()
{
	load();

	return (int)m_MonsterTable.size();
}

MonsterDef *MonsterCsv::getOriginal(int id)
{	
	load();

	MonsterDef* def = m_Monsters.GetRecord(id);

	return def;
}

DefDataTable<MonsterDef> &MonsterCsv::getMonsters()
{	
	load();

	return m_Monsters;
}

MonsterDef *MonsterCsv::get(int id, bool takeplace/* =false */, bool bUseOne/* = false*/)
{	
	load();

	MonsterDef* def = g_ModMgr.tryGetMonsterDef(id, bUseOne);
	if (def != nullptr)
	{
		return def;
	}

	def = m_Monsters.GetRecord(id);
	if(def == NULL && takeplace)
	{
		return m_Monsters.GetRecord(MONSTER_DEFAULT);
	}

	return def;

}

const MonsterDef *MonsterCsv::getByIndex(int index)
{	
	load();

	assert(index >= 0 && index < getNum());
	return m_MonsterTable[index];
}

MonsterDef* MonsterCsv::add(int id, MonsterDef* templateMonsterDef, std::string modelmark, std::string modelname, int type/* =ACTOR_MODEL */)
{
	if (!templateMonsterDef)
		return nullptr;

	MonsterDef tmpMonsterDef;
	tmpMonsterDef.ID = id;

	tmpMonsterDef.ParentID = 0;
	tmpMonsterDef.BabyID = templateMonsterDef->BabyID;
	tmpMonsterDef.TextureID = templateMonsterDef->TextureID;

	memset(tmpMonsterDef.SpawnWeight, 0, sizeof(tmpMonsterDef.SpawnWeight));
	// 	tmpMonsterDef.Texture[0] = 0;
	// 	tmpMonsterDef.InteractionFunction[0] = 0;
	tmpMonsterDef.DevShopDisPlay = false;
	tmpMonsterDef.OpenStoreConfig = templateMonsterDef->OpenStoreConfig;

	if (modelname != "")
		tmpMonsterDef.Name = modelname.c_str();
	else
		tmpMonsterDef.Name = templateMonsterDef->Name;

	tmpMonsterDef.Icon = templateMonsterDef->Icon;
	tmpMonsterDef.EnglishName = templateMonsterDef->EnglishName;
	tmpMonsterDef.Model = modelmark.c_str();

	if (MODEL_TYPE_MAX == type)
	{
		tmpMonsterDef.ModelType = templateMonsterDef->ModelType;
	}
	else
	{
		if (type == ACTOR_MODEL)
			tmpMonsterDef.ModelType = MONSTER_CUSTOM_MODEL;
		else if (type == IMPORT_ACTOR_MODEL)
			tmpMonsterDef.ModelType = MONSTER_IMPORT_MODEL;
		else
			tmpMonsterDef.ModelType = MONSTER_FULLY_CUSTOM_MODEL;
	}
	tmpMonsterDef.Type = templateMonsterDef->Type;
	tmpMonsterDef.EditType = templateMonsterDef->EditType;
	tmpMonsterDef.IsTemplate = templateMonsterDef->IsTemplate;
	tmpMonsterDef.Nature = templateMonsterDef->Nature;
	tmpMonsterDef.TickPeriod = templateMonsterDef->TickPeriod;
	tmpMonsterDef.TickScript = templateMonsterDef->TickScript;
	tmpMonsterDef.Life = templateMonsterDef->Life;
	tmpMonsterDef.LifeIncrease = templateMonsterDef->LifeIncrease;
	tmpMonsterDef.MinRandomScale = templateMonsterDef->MinRandomScale;
	tmpMonsterDef.MinRandomScale = templateMonsterDef->MinRandomScale;
	memcpy(tmpMonsterDef.Armors, templateMonsterDef->Armors, sizeof(templateMonsterDef->Armors));
	tmpMonsterDef.ArmorIncrease = templateMonsterDef->ArmorIncrease;
	memcpy(tmpMonsterDef.Attacks, templateMonsterDef->Attacks, sizeof(templateMonsterDef->Attacks));
	tmpMonsterDef.AttackType = templateMonsterDef->AttackType;
	tmpMonsterDef.AttackIncrease = templateMonsterDef->AttackIncrease;
	tmpMonsterDef.AttackAnimTicks = templateMonsterDef->AttackAnimTicks;
	tmpMonsterDef.Height = templateMonsterDef->Height;
	tmpMonsterDef.Mass = templateMonsterDef->Mass;
	tmpMonsterDef.Width = templateMonsterDef->Width;
	tmpMonsterDef.Thickness = templateMonsterDef->Thickness;
	tmpMonsterDef.HitHeight = templateMonsterDef->HitHeight;
	tmpMonsterDef.HitWidth = templateMonsterDef->HitWidth;
	tmpMonsterDef.HitThickness = templateMonsterDef->HitThickness;
	tmpMonsterDef.AttackDistance = templateMonsterDef->AttackDistance;
	tmpMonsterDef.ViewDistance = templateMonsterDef->ViewDistance;
	tmpMonsterDef.Speed = templateMonsterDef->Speed;
	tmpMonsterDef.SpawnMaxLight = templateMonsterDef->SpawnMaxLight;
	tmpMonsterDef.SpawnSunLight = templateMonsterDef->SpawnSunLight;
	tmpMonsterDef.SpawnMinHeight = templateMonsterDef->SpawnMinHeight;
	tmpMonsterDef.SpawnMaxHeight = templateMonsterDef->SpawnMaxHeight;
	tmpMonsterDef.SpawnMinHeight = templateMonsterDef->SpawnMinHeight;
	tmpMonsterDef.SpawnMinTime = templateMonsterDef->SpawnMinTime;
	tmpMonsterDef.SpawnMaxTime = templateMonsterDef->SpawnMaxTime;
	tmpMonsterDef.SpawnMinTemperature = templateMonsterDef->SpawnMinTemperature;
	tmpMonsterDef.SpawnMaxTemperature = templateMonsterDef->SpawnMaxTemperature;
	tmpMonsterDef.NumLimit = templateMonsterDef->NumLimit;

	tmpMonsterDef.PackNum = templateMonsterDef->PackNum;
	tmpMonsterDef.PickItemOdds = templateMonsterDef->PickItemOdds;
	memcpy(tmpMonsterDef.EquipGroup, templateMonsterDef->EquipGroup, sizeof(templateMonsterDef->EquipGroup));
	tmpMonsterDef.EquipOdds = templateMonsterDef->EquipOdds;
	for (int n = 0; n < MAX_MONSTER_DROPGROUP; n++)
	{
		tmpMonsterDef.DropGroup[n] = templateMonsterDef->DropGroup[n];
		tmpMonsterDef.DropGroupOdds[n] = templateMonsterDef->DropGroupOdds[n];
	}
	for (int n = 0; n < MAX_MONSTER_DROPITEM; n++)
	{
		tmpMonsterDef.DropItem[n] = templateMonsterDef->DropItem[n];
		tmpMonsterDef.DropItemOdds[n] = templateMonsterDef->DropItemOdds[n];
	}
	tmpMonsterDef.BurnDropItem = templateMonsterDef->BurnDropItem;
	tmpMonsterDef.BurnDropItemOdds = templateMonsterDef->BurnDropItemOdds;
	tmpMonsterDef.FeedItem = templateMonsterDef->FeedItem;
	tmpMonsterDef.FeedOdds = templateMonsterDef->FeedOdds;
	tmpMonsterDef.DropExp = templateMonsterDef->DropExp;
	tmpMonsterDef.DropExpOdds = templateMonsterDef->DropExpOdds;
	tmpMonsterDef.ActiveAtk = templateMonsterDef->ActiveAtk;
	tmpMonsterDef.CanTame = templateMonsterDef->CanTame;
	tmpMonsterDef.CanBreed = templateMonsterDef->CanBreed;
	tmpMonsterDef.CanRide = templateMonsterDef->CanRide;
	tmpMonsterDef.CanTalk = templateMonsterDef->CanTalk;
	tmpMonsterDef.IsPlayerCustom = templateMonsterDef->IsPlayerCustom;
	tmpMonsterDef.AvoidWater = templateMonsterDef->AvoidWater;
	tmpMonsterDef.PathHide = templateMonsterDef->PathHide;
	tmpMonsterDef.SunHurt = templateMonsterDef->SunHurt;
	tmpMonsterDef.HasAvatar = templateMonsterDef->HasAvatar;
	tmpMonsterDef.CanPassClosedWoodenDoors = templateMonsterDef->CanPassClosedWoodenDoors;
	tmpMonsterDef.ModelScale = templateMonsterDef->ModelScale;
	tmpMonsterDef.KillScore = templateMonsterDef->KillScore;
	tmpMonsterDef.BreedScore = templateMonsterDef->BreedScore;
	tmpMonsterDef.TameScore = templateMonsterDef->TameScore;
	tmpMonsterDef.SkinningTime = templateMonsterDef->SkinningTime;
	tmpMonsterDef.CorpseTime = templateMonsterDef->CorpseTime;
	tmpMonsterDef.CopyID = templateMonsterDef->CopyID;
	tmpMonsterDef.TeamID = templateMonsterDef->TeamID;
	tmpMonsterDef.gamemod = templateMonsterDef->gamemod;
	tmpMonsterDef.BuffId = templateMonsterDef->BuffId;
	tmpMonsterDef.BornBuff = templateMonsterDef->BornBuff;
	tmpMonsterDef.NameDisPlay = templateMonsterDef->NameDisPlay;
	tmpMonsterDef.DescDisplay = templateMonsterDef->DescDisplay;
	tmpMonsterDef.Desc = templateMonsterDef->Desc;
	tmpMonsterDef.BagNum = templateMonsterDef->BagNum;
	tmpMonsterDef.Food = templateMonsterDef->Food;
	tmpMonsterDef.FoodReduce = templateMonsterDef->FoodReduce;
	tmpMonsterDef.Dialogue = templateMonsterDef->Dialogue;

	tmpMonsterDef.TurningSpeed = templateMonsterDef->TurningSpeed;
	tmpMonsterDef.Toughness = templateMonsterDef->Toughness;
	tmpMonsterDef.ToughnessRecover = templateMonsterDef->ToughnessRecover;
	tmpMonsterDef.TouReduce = templateMonsterDef->TouReduce;
	tmpMonsterDef.RepelRes = templateMonsterDef->RepelRes;

	tmpMonsterDef.HurtSound = templateMonsterDef->HurtSound;
	tmpMonsterDef.DeathSound = templateMonsterDef->DeathSound;
	tmpMonsterDef.SaySound = templateMonsterDef->SaySound;
	tmpMonsterDef.StepSound = templateMonsterDef->StepSound;
	tmpMonsterDef.Effect = templateMonsterDef->Effect;
	tmpMonsterDef.AlwaysUpdate = templateMonsterDef->AlwaysUpdate;
	m_Monsters.AddRecord(tmpMonsterDef.ID, tmpMonsterDef);

	auto iter = m_Monsters.m_Records.begin();
	for (; iter != m_Monsters.m_Records.end(); iter++)
	{
		if (iter->second.ID == id)
		{
			if (iter->second.BabyID > 0)
			{
				MonsterDef* babydef = m_Monsters.GetRecord(iter->second.BabyID);
				assert(babydef);
				if (babydef) {
					babydef->ParentID = iter->first;
				}
			}

			m_MonsterTable.push_back(&iter->second);

			break;
		}
	}

	return m_Monsters.GetRecord(id);
}

MonsterDef *MonsterCsv::add(int id, std::string modelmark, std::string modelname, int type)
{	
	load();

	MonsterDef* templateMonsterDef = get(3100);
	if (templateMonsterDef == NULL) return NULL;

	return add(id, templateMonsterDef, modelmark, modelname, type);
}

MonsterDef* MonsterCsv::addByCopy(int id, std::string modelmark, std::string modelname, int type, int copyId)
{
	load();

	MonsterDef* templateDef = get(copyId);
	if (!templateDef) { return nullptr; }
	MonsterDef* pDef = add(id, templateDef, modelmark, modelname, type);
	pDef->CopyID = copyId;
	return pDef;
}

MonsterDef *MonsterCsv::getIgnoreEditPlugin(int id, bool takeplace)
{
	MonsterDef * def = get(id, takeplace);
	if (def && def->CopyID <= 0 &&  g_ModEditorMgr.isModEditing())
	{
		def = getOriginal(id);
	}
	return def;
}
