#pragma once

#include "CitySave_generated.h"
#include "EcosysBuildHelp.h"
#include "world_types.h"
#include "WorldProxy.h"
#include "Common/SingletonDefinition.h"

struct CityData;


struct CityConnectionPoints {
    WCoord point1;
    WCoord point2;
    float distance;
};

// 道路类型枚举
enum RoadType {
    ROAD_TYPE_NORMAL = 0,  // 普通道路
    ROAD_TYPE_TRACK = 1    // 轨道
};

class EXPORT_SANDBOXENGINE EcosysUnitRoadBuild :public EcosysUnitBigBuild {
public:
    EcosysUnitRoadBuild();
    struct RoadNode {
        WCoord start;  // 起点
        WCoord end;    // 终点
        bool roadBuilt;
        RoadType roadType; // 道路类型：普通道路或轨道
        std::vector<std::pair<WCoord, WCoord>> pathSegments; // 存储实际的路径段，包括拐点
        
        RoadNode() : roadBuilt(false), roadType(ROAD_TYPE_NORMAL) {}
        RoadNode(const WCoord& s, const WCoord& e, bool built = false, RoadType type = ROAD_TYPE_NORMAL) 
            : start(s), end(e), roadBuilt(built), roadType(type) {}
    };

    struct PotentialConnection {
        size_t index;
        float distance;

        PotentialConnection(size_t i, float d)
            : index(i), distance(d) {
        }
    };

    bool addToWorld(World* pworld);
    void onWorldLoaded(World* pworld);
    void leaveWorld();

    void save(flatbuffers::FlatBufferBuilder& builder, std::vector<flatbuffers::Offset<FBSave::CityRoadNodeData>>& roaddataFB);
    void load(World* pworld, const FBSave::CityFBData* src);
    
    // 曼哈顿路径生成（L形路径）
    void generateManhattanRoad(World* pworld, const WCoord& start, const WCoord& end);
    
    // Z形路径生成（三段式路径）
    void generateZShapedRoad(World* pworld, const WCoord& start, const WCoord& end, RoadType roadType = ROAD_TYPE_NORMAL);
    
    // 生成直线路段
    void generateStraightRoadSegment(World* pworld, const WCoord& start, const WCoord& end);
    
    // 计算点到直线的距离
    float pointToLineDistance(int x, int z, const WCoord& lineStart, const WCoord& lineEnd);
    
    // 计算点在线段上的投影参数t
    float calculateProjection(int x, int z, const WCoord& lineStart, const WCoord& lineEnd);

    // 在指定位置放置道路方块，增加参数控制是否生成标线
    void placeRoadBlock(World* pworld, int x, float targetY, int z, float distFromCenter, bool generateLine = true);
    void processRoadEdge(World* pworld, int x, float targetY, int z);

    // 生成道路拐角
    void generateRoadCorner(World* pworld, const WCoord& cornerPoint);

    // 计算道路边界框的辅助函数
    void calculateRoadBoundingBox(const WCoord& start, const WCoord& end, 
                                 int& minX, int& maxX, int& minZ, int& maxZ);

    int calculateRoadHeight(World* pworld, const WCoord& pos, bool clearObstacle = true);

    // 检查并填补道路边缘缺口
    bool checkAxisHasRoad(World* pworld, int x, float targetY, int z, bool isXAxis);
    
    // 辅助函数：在指定位置及其上下各一格检查方块
    int checkBlockWithHeight(World* pworld, int x, float baseY, int z);
    
    // 判断方块ID是否为道路方块
    bool isRoadBlock(int blockId);

    // 生成轨道
    void generateTrack(World* pworld, const WCoord& start, const WCoord& end);
    
    // 生成L型轨道（曼哈顿轨道）
    void generateManhattanTrack(World* pworld, const WCoord& start, const WCoord& end);
    
    // 放置轨道方块
    void placeTrackBlock(World* pworld, int x, float targetY, int z, float distFromCenter, float trackPosition);
    

    // 检查是否为轨道方块
    bool isTrackBlock(int blockId);
    
    // 计算轨道高度
    int calculateTrackHeight(World* pworld, const WCoord& pos);
    
    // 添加道路节点
    void addRoadNodes(const WCoord& start, const WCoord& end);

    // 在道路两侧生成建筑点的函数声明
    std::vector<WCoord> generateBuildingPointsAlongRoads(
        World* world,
        float minDistance = 30.0f,      // 建筑间最小距离
        float buildingSideOffset = 8.0f, // 建筑距离道路的侧向偏移
        int totalCount = 10             // 需要生成的建筑点总数
    );

private:
    bool m_CalculatRoad = false;
    bool m_GenRoad = false;
    std::vector<RoadNode> m_RoadNodes;
    Rainbow::HashTable<ChunkIndex, int, ChunkIndexHashCoder> m_ProcessedChunindex;

    float evaluateObstacles(World* pworld, const WCoord& pos);
    float evaluateTerrainSuitability(World* pworld, const WCoord& pos);
    CityConnectionPoints findBestRoadConnectionPoint(World* pworld, const CityData& citydata1, const CityData& citydata2);
    bool processNodePos(World* pworld, const WCoord& point1, const WCoord& point2, bool load = false);

    bool checkTerrainSuitability(World* pworld, const ChunkIndex& index) const;
    bool isValidRoadBiome(int biomeId) const;
    void clearObstacles(World* world, int x, int targetHeight, int z);
    void generateRoadSegment(World* pworld, const WCoord& start, const WCoord& end);
    bool canConnectNodes(World* pworld, const WCoord& start, const WCoord& end);
    float pointToLineSegmentDistance(const WCoord& point,const WCoord& lineStart,const WCoord& lineEnd) const;

    bool doLinesIntersect(const WCoord& p1, const WCoord& p2, const WCoord& p3, const WCoord& p4);

    WCoord calculateBezierPoint(const WCoord& start, const WCoord& control, const WCoord& end, float t);
    WCoord calculateControlPoint(const WCoord& start, const WCoord& end) const;
    void generateSmoothRoadSegment(World* pworld, const WCoord& start, const WCoord& end);
    float normalizeAngle(float angle) const;
    float calculateCurvature(const WCoord& start, const WCoord& control, const WCoord& end) const;
};

EXPORT_SANDBOXENGINE EcosysUnitRoadBuild& GetEcosysUnitRoadBuild();
EXPORT_SANDBOXENGINE EcosysUnitRoadBuild* GetEcosysUnitRoadBuildPtr();