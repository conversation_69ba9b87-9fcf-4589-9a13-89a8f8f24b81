/*
** Lua binding: GameAnalyticsTolua
*/

#ifndef __cplusplus
#include "stdlib.h"
#endif
#include "string.h"

#include "Minitolua.h"

#include "ui_common.h"

/* Exported function */
TOLUA_API int  tolua_GameAnalyticsTolua_open (lua_State* tolua_S);

#include "GameAnalytics.h"

/* function to register type */
static void tolua_reg_types (lua_State* tolua_S)
{
 tolua_usertype(tolua_S,"GameAnalytics");
}

/* method: TrackEvent of class  GameAnalytics */
#ifndef TOLUA_DISABLE_tolua_GameAnalyticsTolua_GameAnalytics_TrackEvent00
static int tolua_GameAnalyticsTolua_GameAnalytics_TrackEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"GameAnalytics",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::string event_name = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  {
   GameAnalytics::TrackEvent(event_name);
   tolua_pushcppstring(tolua_S,(const char*)event_name);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'TrackEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: TrackEvent of class  GameAnalytics */
#ifndef TOLUA_DISABLE_tolua_GameAnalyticsTolua_GameAnalytics_TrackEvent01
static int tolua_GameAnalyticsTolua_GameAnalytics_TrackEvent01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"GameAnalytics",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  const std::string event_name = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string json_data = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
  {
   GameAnalytics::TrackEvent(event_name,json_data);
   tolua_pushcppstring(tolua_S,(const char*)event_name);
   tolua_pushcppstring(tolua_S,(const char*)json_data);
  }
 }
 return 2;
tolua_lerror:
 return tolua_GameAnalyticsTolua_GameAnalytics_TrackEvent00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* Open function */
TOLUA_API int tolua_GameAnalyticsTolua_open (lua_State* tolua_S)
{
 tolua_open(tolua_S);
 tolua_reg_types(tolua_S);
 tolua_module(tolua_S,NULL,0);
 tolua_beginmodule(tolua_S,NULL);
  tolua_cclass(tolua_S,"GameAnalytics","GameAnalytics","",NULL);
  tolua_beginmodule(tolua_S,"GameAnalytics");
   tolua_function(tolua_S,"TrackEvent",tolua_GameAnalyticsTolua_GameAnalytics_TrackEvent00);
   tolua_function(tolua_S,"TrackEvent",tolua_GameAnalyticsTolua_GameAnalytics_TrackEvent01);
  tolua_endmodule(tolua_S);
 tolua_endmodule(tolua_S);
 return 1;
}


#if defined(LUA_VERSION_NUM) && LUA_VERSION_NUM >= 501
 TOLUA_API int luaopen_GameAnalyticsTolua (lua_State* tolua_S) {
 return tolua_GameAnalyticsTolua_open(tolua_S);
};
#endif

