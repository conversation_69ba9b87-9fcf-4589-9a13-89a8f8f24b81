﻿#include "WorldEventManager.h"
#include <fstream>
#include "json/jsonxx.h"
#include "AirDrop/AirDropEvent.h"
#include "Utilities/Logs/LogAssert.h"
#include "WorldEventCsv.h"

WorldEventManager::WorldEventManager(PluginManager* p)
{
    m_pPluginManager = p;
}

WorldEventManager::~WorldEventManager()
{
}

bool WorldEventManager::Awake()
{

    return true;
}

bool WorldEventManager::Init()
{
    this->LoadConfig();
    return true;
}

bool WorldEventManager::Execute(float dtime)
{
    this->Update(dtime);
    return true;
}

bool WorldEventManager::FixedTick()
{
    return true;
}

bool WorldEventManager::Shut()
{
    return true;
}


void WorldEventManager::ReloadConfig()
{
    WorldEventCsv* worldEventCsv = WorldEventCsv::getInstance();
    if (!worldEventCsv) {
        LOG_WARNING("WorldEventManager::ReloadConfig: WorldEventCsv singleton is null");
        return;
    }

    if (!worldEventCsv->reload()) {
        LOG_WARNING("WorldEventManager::LoadConfig: Failed to load WorldEventCsv");
        return;
    }

    m_eventdef_list.clear();

    for (int i = 0; i < worldEventCsv->getNum(); i++) {
        WorldEventDef* def = worldEventCsv->getByIndex(i);
        if (def) {
            m_eventdef_list.push_back(*def);
        }
    }
}

bool WorldEventManager::IsEventActive(int eventid) const {
    return m_activeEvents.find(eventid) != m_activeEvents.end();
}

void WorldEventManager::LoadConfig() {
    WorldEventCsv* worldEventCsv = WorldEventCsv::getInstance();
    if (!worldEventCsv) {
        LOG_WARNING("WorldEventManager::LoadConfig: WorldEventCsv singleton is null");
        return;
    }

    if (!worldEventCsv->load()) {
        LOG_WARNING("WorldEventManager::LoadConfig: Failed to load WorldEventCsv");
        return;
    }

    LOG_INFO("WorldEventManager::LoadConfig: Successfully loaded WorldEventCsv");

   for (int i = 0; i < worldEventCsv->getNum(); i++) {
        WorldEventDef* def = worldEventCsv->getByIndex(i);
        if (def) {
            m_eventdef_list.push_back(*def);
        }
    }
}

void WorldEventManager::Update(float deltaTime) {
    std::lock_guard<std::mutex> lock(m_eventMutex);

    // Update server time
    m_serverTime += deltaTime;

    // 每1分钟重新加载一次配置
    if(m_serverTime - m_loadcfgTime > 60 ){
        ReloadConfig();
        m_loadcfgTime = m_serverTime;
    }

    // Check for new events to trigger
    CheckEventTriggers();

    // Update active events
    for (auto it = m_activeEvents.begin(); it != m_activeEvents.end();) {
        auto& event = it->second;
        if (event->IsActive()) {
            event->OnUpdate(deltaTime);
            ++it;
        } else {
            event->OnEnd();
            it = m_activeEvents.erase(it);
        }
    }

    AirDropEvent::ClearChestList();
}

void WorldEventManager::CheckEventTriggers() {

    for (size_t i = 0; i < m_eventdef_list.size(); ++i) {

        auto& eventConfig = m_eventdef_list[i];

        int eventid = eventConfig.ID;
        std::string eventType = eventConfig.EventType;
        float firstTrigger = eventConfig.FirstTime; // 首次触发时间秒
        float interval = eventConfig.Interval;

        if (!eventConfig.Enable) {
            continue;
        }

        if (eventConfig.EventType != "sys_airdrop") {
            continue;
        }

        // 检查interval是否有效
        if (interval <= 0.0f || firstTrigger <= 0.0f) {
            continue;
        }

        // 检查是否已经过了首次触发时间
        if (m_serverTime < firstTrigger) {
            continue;
        }

        // 计算从首次触发后经过了多少完整间隔
        float timeFromFirst = m_serverTime - firstTrigger;
        int intervalsPassed = static_cast<int>(timeFromFirst / interval);

        // 计算距离上次触发的时间
        float timeSinceLastTrigger = timeFromFirst - (intervalsPassed * interval);

        // 如果距离上次触发的时间很小，且当前没有该类型的活动事件，则触发新事件
        if (timeSinceLastTrigger < 0.1f && !IsEventActive(eventid)) {
            static int count = 0;
            LOG_INFO("StartEvent: %d %s, count: %d", eventid, eventType.c_str(), count++);
            StartEvent(&eventConfig);
        }
    }
}

void WorldEventManager::StartEvent(WorldEventDef* eventdef) {

    if (auto event = CreateWorldEvent(eventdef)) {
        m_activeEvents[eventdef->ID] = event;
        event->OnStart();
    }
}

void WorldEventManager::StopEvent(int eventid)  {
    auto event = m_activeEvents.find(eventid);
    if (event != m_activeEvents.end()) {
        LOG_INFO("StopEvent: %d", eventid);
        event->second->OnEnd();
        m_activeEvents.erase(eventid);
    }
}

std::shared_ptr<WorldEvent> WorldEventManager::CreateWorldEvent(WorldEventDef* eventdef) {

    std::string eventType = eventdef->EventType;

    std::shared_ptr<WorldEvent> event_instance = nullptr;
    if (eventType == "sys_airdrop" || eventType == "player_airdrop") {
        event_instance = std::make_shared<AirDropEvent>(*eventdef);
    }

    if (!event_instance) {
        LOG_WARNING("CreateWorldEvent: %s, event is nullptr", eventType.c_str());
        return nullptr;
    }

    return event_instance;
}

WorldEventDef* WorldEventManager::getEventDef(int eventid)
{
    for (size_t i = 0; i < m_eventdef_list.size(); ++i) {

        auto& eventdef = m_eventdef_list[i];

        if ( eventdef.ID == eventid) {
            return &eventdef;
        }
    }
    return nullptr;
}

// 获取活动事件列表
std::vector<WorldEvent*> WorldEventManager::GetActiveEvents() {
    std::vector<WorldEvent*> activeEvents;    
    for (const auto& event : m_activeEvents) {        
        activeEvents.push_back(event.second.get());    
    }   
    return activeEvents;
}

bool WorldEventManager::TriggerAirDropEvent(int eventid, int pos_x, int pos_y, int pos_z)
{
    // 检查事件ID是否有效
    WorldEventDef* eventdef = getEventDef(eventid);
    if (eventdef == nullptr) {
        LOG_WARNING("TriggerEvent: 无效的事件ID %d", eventid);
        return false;
    }

    // 检查事件是否已经处于活动状态
    if (IsEventActive(eventid)) {
        LOG_WARNING("TriggerEvent: 事件ID %d 已经处于活动状态", eventid);
        return false;
    }

    // 启动事件
    LOG_INFO("TriggerEvent: 外部触发事件 %d, 类型: %s", eventid, eventdef->EventType.c_str());

    if(pos_y > 0){
        eventdef->MaxHeight = pos_y;
    }

    eventdef->drop_pos1.x = pos_x-2;
    eventdef->drop_pos1.z = pos_z-2;

    eventdef->drop_pos2.x = pos_x +2;
    eventdef->drop_pos2.z = pos_z +2;
    eventdef->dropType = 1;
    StartEvent(eventdef);

    return true;
}
