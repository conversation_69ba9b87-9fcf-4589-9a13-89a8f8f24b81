#pragma once
#include "Misc/GameApp.h"
#include "ClientPrerequisites.h"
#include "IWorldExports.h"
#include "Utils/ClientAppProxy.h"
#include "SandboxMacros.h"
#include "thinkingdata/GameAnalytics.h"

namespace Rainbow
{
	class ConsoleOutputDevice;
}

namespace fairygui {
    class GLoader;
}

namespace MINIW 
{
	class ClientLogin;

	class EXPORT_IWORLD ClientApp;
	class ClientApp : public Rainbow::GameApp, public ClientAppProxy//tolua_exports
	{//tolua_exports
	public:
		ClientApp();
		~ClientApp() override;


		virtual void AppInit() override;

		virtual void BeginPlay() override;

		virtual void AppExit() override;

#if STUDIO_LINK_EDITOR

		virtual void SystemInit(const char* cmdStr) override
		{
			Application::SystemInit(cmdStr);
		}

		virtual void SetPlaying(bool playing)
		{
			m_Playing = playing;
		}
#endif// STUDIO_LINK_EDITOR

		bool InitGameAnalytics();

		std::string getAceInfo();
		void PostHotfix(bool isImmediately = false);
		void StartAppUpdate(bool isSkipColdUpdate = false);
		void StartHotfixUpdate();
		void DisptchHotfix();
		virtual void StartDnsConvert();
		void AfterDnsConvert();
		void ExcuteBasePkgHotfix();
		void ExcuteUniverseBasePkgHotfix();
		//void OnDetectionPermission(bool result);
		//tolua_begin
		bool InitPhysXMgr();
		bool InitTriggerScriptMgr();
		void SetUserTypePointer();
		bool LoadScriptTOC(const char* tocfile);

		void InitGameData();

		void StartUpdate();

		virtual void GameExit(bool restart = false);

		float GetFrameTime();

		void HandleEvents();

		unsigned int  GetFrameCount();
		// for lua
		void SomeInitAfterCsvLoad();

		//tolua_end
		ClientLogin* GetClientLogin();
		virtual void CreateClientLogin();

		virtual bool OnCloseConfirm() override;

		//tolua_begin
		void SetLimitFPS(bool isLimitFPS);
		void SetLimitFPSFromLua(bool isLimitFPS/*deprecated ????????????*/, int frameRate);
		//tolua_end

#ifdef DEDICATED_SERVER
		virtual void setSIGUSR2() {}
		virtual void handleSIGUSR2() {}
		virtual bool hasSIGUSR2() { return false; }
		virtual void SetServerInitOk() {}
		virtual bool IsServerInitOk() { return false; }
		virtual void OnServerFrameTick() {}
#endif

		virtual void OnPause() override;

		virtual void OnResume() override;

		virtual void OnBackPressed() override;

		virtual bool checkIsGMWhiteListMember();
		void DevUISetIconByResIdExProxy(fairygui::GLoader* pLoader, std::string strLongId);
		void InitFont();
	private:
		INLINE void SetFPS(int frameRate);
		bool checkDebuggerRunning();
		bool checkVMPValid();
		bool checkUnderVM();
		bool preOpenMicroMini();
		bool checkDoubleOpenings();
		bool enableNewAppUpdate();
		bool checkInsidePkgCompress();

	protected:
		virtual void OnTick(float dt) override;

		virtual void OnParseCmdStr(const char* cmdStr) override;
		void OnCmd(const char* cmd, Rainbow::ConsoleOutputDevice* output);
	protected:
		ClientLogin* m_ClientLogin = nullptr;
	private:
		bool m_QuitGame = false;
		bool m_AppPaused = false;

		float m_GameTickAccum;
		float m_LastGameTickAccum;
		float m_NetTickAccum;

	};//tolua_exports

	EXPORT_IWORLD \
		ClientApp& GetClientApp(); //tolua_exports
}