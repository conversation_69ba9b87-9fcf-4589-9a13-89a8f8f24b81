@echo off
set mToluaType=%~1

set TOLUA_EXE=%cd%\..\..\External\Game\lua\tolua\bin\tolua++.exe
set PATH_PYTHON_EXE=%cd%\..\..\..\Tools\buildtools\Python\Win64\python.exe
set TOLUA_PYTHON_EXE=%cd%\..\..\..\Tools\tolua_sandbox\CppToLua.py

setlocal EnableDelayedExpansion
set "aOldFiles=!aOldFiles! ClientToLua.cpp"
set "aOldFiles=!aOldFiles! platform\win32\ClientToLua.cpp"
set "aOldFiles=!aOldFiles! platform\android\ClientToLua.cpp"
set "aOldFiles=!aOldFiles! platform\ohos\ClientToLua.cpp"
set "aOldFiles=!aOldFiles! platform\ios\ClientToLua.cpp"
set "aOldFiles=!aOldFiles! platform\linux\ClientToLua.cpp"

if "%mToluaType%" neq "winserver" (
    echo "======== gameStage ======="  
    %TOLUA_EXE% -o gameStage\GameStageTolua.cpp -n GameStageTolua gameStage\GameStageTolua.pkg
    for %%i in (%aOldFiles%) do (
        if exist %%i del %%i
    )

	echo "======== GameAnalytics ======="  
	%TOLUA_EXE% -o thinkingdata\GameAnalyticsTolua.cpp -n GameAnalyticsTolua thinkingdata\GameAnalytics.pkg
)

setlocal DisableDelayedExpansion

if "%mToluaType%" == "win32" (
	echo "========ClientToLua WIN======="  
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_win32.ini
	rem %TOLUA_EXE% -o platform\win32\ClientToLua.cpp -n ClientToLua platform\win32\ClientToLua.pkg
) ^
else if "%mToluaType%" == "win32_ministudio" (
	echo "========ClientToLua WIN MINISTUDIO======="  
	if exist platform\win32\ClientToLua_ministudio.cpp del /q platform\win32\ClientToLua_ministudio.cpp 
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_win32_ministudio.ini
	rem %TOLUA_EXE% -o platform\win32\ClientToLua_ministudio.cpp -n ClientToLua platform\win32\ClientToLua_ministudio.pkg
) ^
else if "%mToluaType%" == "android" (
	echo "========ClientToLua ANDROID======="  
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_android.ini
	rem %TOLUA_EXE% -o  platform\android\ClientToLua.cpp -n ClientToLua platform\android\ClientToLua.pkg
) ^
else if "%mToluaType%" == "ohos" (
	echo "========ClientToLua OHOS======="  
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_ohos.ini
	rem %TOLUA_EXE% -o  platform\ohos\ClientToLua.cpp -n ClientToLua platform\ohos\ClientToLua.pkg
) ^
else if "%mToluaType%" == "winserver" (
	echo "========ClientToLua winserver======="  
	%TOLUA_EXE% -o gameStage\GameStageTolua_server.cpp -n GameStageTolua gameStage\GameStageTolua_server.pkg
	if exist platform\win32\ClientToLua_server.cpp del /q platform\win32\ClientToLua_server.cpp 
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_winserver.ini
) ^
else ( 
	echo "========ClientToLua WIN======="
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_win32.ini
	rem %TOLUA_EXE% -o platform\win32\ClientToLua.cpp -n ClientToLua platform\win32\ClientToLua.pkg
	echo "========ClientToLua ANDROID======="
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_android.ini
	rem %TOLUA_EXE% -o  platform\android\ClientToLua.cpp -n ClientToLua platform\android\ClientToLua.pkg
	echo "========ClientToLua OHOS======="
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_ohos.ini
	rem %TOLUA_EXE% -o  platform\ohos\ClientToLua.cpp -n ClientToLua platform\ohos\ClientToLua.pkg 
	echo "========ClientToLua IOS======="
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_ios.ini
	rem %TOLUA_EXE% -o  platform\ios\ClientToLua.cpp -n ClientToLua platform\ios\ClientToLua.pkg 
	echo "========ClientToLua Linux======="
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_linux.ini
	rem %TOLUA_EXE% -o  platform\linux\ClientToLua.cpp -n ClientToLua platform\linux\ClientToLua.pkg
    echo "========ClientToLua WIN MINISTUDIO======="
	if exist platform\win32\ClientToLua_ministudio.cpp del /q platform\win32\ClientToLua_ministudio.cpp 
	%PATH_PYTHON_EXE% %TOLUA_PYTHON_EXE% -i platform\tolua_conf_win32_ministudio.ini
	rem %TOLUA_EXE% -o platform\win32\ClientToLua_ministudio.cpp -n ClientToLua platform\win32\ClientToLua_ministudio.pkg
	echo "========ClientToLua DONE======="  
)

rem pause