#pragma once
#include "Math/Vector3f.h"
#include <string>
#include "worldEvent/WorldEventCsv.h"
#include "SandboxGame.h"

class ClientActor;
namespace Rainbow
{
    class ISound;
}

class EXPORT_SANDBOXGAME AirDropCraft {
public:
    AirDropCraft(const WorldEventDef& def, const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos);
    ~AirDropCraft();

     
    // 核心更新
    void Update(float deltaTime);
    
    // 状态查询
    bool ReachedDropPoint() const { return m_isReachedDropPoint; }
    bool IsFinished() const { return m_isFinished; }
    const Rainbow::Vector3f& GetCurrentPosition() const { return m_currentPos; }
    const Rainbow::Vector3f& GetDropPoint() const { return m_dropPoint; }
    
    // 设置/获取
    void SetDropPoint(const Rainbow::Vector3f& point);
    void SetVisualEffect(const std::string& effectId) { m_visualEffectId = effectId; }
    void SetSoundEffect(const std::string& soundId) { m_soundEffectId = soundId; }

private:
    //飞机实体
    ClientActor* actor;
    long long objId;
    bool isActorExist();

    // 位置相关
    Rainbow::Vector3f m_startPos;
    Rainbow::Vector3f m_endPos;
    Rainbow::Vector3f m_currentPos;
    Rainbow::Vector3f m_dropPoint;
    Rainbow::Vector3f m_directionToDropPoint;
    Rainbow::Vector3f m_directionFromDropPoint;
    float m_distanceToDropPoint;
    float m_distanceFromDropPoint;
    // 运动参数
    float m_speed;
    float m_dropPointDistance;
    float m_totalDistance;
    float m_currentDistance{0.0f};
    
    // 状态标记
    bool m_isReachedDropPoint{false};
    bool m_isFinished{false};
    
    // 特效
    std::string m_visualEffectId;
    std::string m_soundEffectId;
    
    // 辅助方法
    void UpdatePosition(float deltaTime);
    void CheckDropPoint();
    void PlayEffects();
    void StopEffects();

    //广播飞机位置
    void sendBroadCastPos(std::string eventType);

    //开始事件
    float m_lastTime;
    float m_startTime;

    

    //空投事件配置
    WorldEventDef m_event_def;
};
