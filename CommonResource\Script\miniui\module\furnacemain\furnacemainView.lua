--声明
local furnacemainView = Class("furnacemainView",ClassList["UIBaseView"])

--创建
function furnacemainView:Create(param)
	return ClassList["furnacemainView"].new(param)
end

--初始化
function furnacemainView:Init(param)
	self.super:Init(param)


end

local TitleName = {
	[0] = "熔炉",
	[1] = "氧气提取器",
	[2] = "烹饪台",
	[3] = "大熔炉",
	[4] = "炼油机",
}


--初始化界面
function furnacemainView:InitView()
	MiniLog("furnacemainView:InitView")
	self.root:makeFullScreen()
end

function furnacemainView:UpdateType(type, fuelcount, matcount, retcount)
	MiniLog("furnacemainView:UpdateType", type)
	self.paneltype = type
	self.fuelcount = fuelcount
	self.matcount = matcount
	self.retcount = retcount
	self.panel_typectrl:setSelectedIndex(type)
	self.widgets.title:setText(TitleName[type])

	self:UpdateAllItems()
end

function furnacemainView:UpdateAllItems()
	MiniLog("furnacemainView:UpdateAllItems")
	if not ClientBackpack or not CurMainPlayer then
		return
	end

	self:UpdateFuelItem()
	self:UpdateMatItem()
	self:UpdateRetItem()
end

function furnacemainView:UpdateFireState(state)
	self.panel_fuelctrl:setSelectedIndex(state)
	if state == 0 or state == 1 then
		self.widgets.txt_fire:setText(GetS(700106))
	else
		self.widgets.txt_fire:setText(GetS(700107))
	end
end

function furnacemainView:UpdateItem(itemui, gridindex)
	local itemid = ClientBackpack:getGridItem(gridindex)
	if itemid ~= 0 then
		itemui.ctrl:setSelectedIndex(2)
		UIUtils:SetItemIcon(itemui.icon, itemid)
		local count = ClientBackpack:getGridNum(gridindex)
		MiniLog("furnacemainView:UpdateItem " .. gridindex .. " " .. count)
		itemui.num:setText(count)

	else
		itemui.ctrl:setSelectedIndex(0)
	end
end

-- 0~9 : mtl原料,  10~19: fuel燃料,   20~29: result产物
function furnacemainView:UpdateFuelItem()
	for i = 1, self.fuelcount do
		local itemui = self.widgets.fuelitems[i]
		local gridindex = FURNACE_START_INDEX + i + 9
		self:UpdateItem(itemui, gridindex)
	end
end


function furnacemainView:UpdateMatItem()
	for i = 1, self.matcount do
		local itemui = self.widgets.matitems[i]
		local gridindex = FURNACE_START_INDEX + i - 1
		self:UpdateItem(itemui, gridindex)
	end
end

function furnacemainView:UpdateRetItem()
	for i = 1, self.retcount do
		local itemui = self.widgets.retitems[i]
		local gridindex = FURNACE_START_INDEX + i + 19
		self:UpdateItem(itemui, gridindex)
	end
end
