#include "BlockPlacementPreview.h"
#include "blocks/BlockMaterialMgr.h"
//#include "blocks/BlockTexMgr.h"
#include "PlayManagerInterface.h"
#include "DefManagerProxy.h"
#include "Optick/optick.h"
#include <ClientInfoProxy.h>
//#include "PlayerControl.h"
#include "BaseClass/RuntimeClass.h"
#include "section.h"
#include "chunk.h"
#include <unordered_set> 
using namespace Rainbow;
using namespace MINIW;
IMPLEMENT_CLASS_GLOBAL(BlockPlacementPreview, kRuntimeClassFlagAbstract)

BlockPlacementPreview::BlockPlacementPreview()
    : BlockDecalMesh("white", GETTEX_WITHDEFAULT)
    , m_World(nullptr)
    , m_IsVisible(false)
    , m_IsValidPlacement(false)
    , m_PreviewPos(0, 0, 0)
    , m_PreviewBlockId(0)
    , m_blockData(-1)
    , m_HighlightedBlockId(0)
    , m_HighlightedBlockFace(-1)
    , m_Rotation(0)
    , m_AlphaValue(0.6f)
{
    // Set default colors

	m_ValidColor = Rainbow::ColorRGBAf(0.0f, 0.31f, 0.8f, m_AlphaValue);  // Blue with transparency
	m_InvalidColor = Rainbow::ColorRGBAf(1.0f, 0.2f, 0.2f, m_AlphaValue); // Red with transparency
}

BlockPlacementPreview::~BlockPlacementPreview()
{
    // Base class destructor handles cleanup
}

void BlockPlacementPreview::Initialize(World* world)
{
    m_World = world;
    ShowPreview(false);
}

void BlockPlacementPreview::ShowPreview(bool show)
{
    if (m_IsVisible != show)
    {
        m_IsVisible = show;
        Show(show);
    }
}

void BlockPlacementPreview::UpdateColor()
{
    SetOverrideColor(m_IsValidPlacement ? m_ValidColor : m_InvalidColor);
}

bool BlockPlacementPreview::IsReplaceable(int blockId)
{
    if (blockId == 0) // Air is always replaceable
        return true;
        
    // Get block material and check if it's replaceable
    BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockId);
    return material && material->isReplaceable();
}

bool BlockPlacementPreview::CanReplace(const WCoord& pos, int blockId)
{
    // Get current block at position
    int currentBlockId = m_World->getBlockID(pos.x, pos.y, pos.z);
    
    // Air is always replaceable
    if (currentBlockId == 0)
        return true;
        
    // Check if current block is replaceable
    return IsReplaceable(currentBlockId);
}

bool BlockPlacementPreview::IsValidPlacement(const WCoord& pos, int blockId, long long playerUin, const IntersectResult& result)
{
    if (!m_World)
        return false;
        
    // 获取方块定义，检查是否为建筑蓝图
    BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockId);
    if (blockDef) {
        const std::string& blockType = blockDef->Type.c_str();
        
        // 判断是否为多方块建筑蓝图
        if (blockType == "socdoor" || blockType == "socdoubledoor" || 
            blockType == "multibranch" || blockType == "multistair" || 
            blockType == "multitriangle") {
            
            // 获取蓝图尺寸
            int sizeX = 1, sizeZ = 1;
            if (blockType == "socdoubledoor" || blockType == "multistair" || blockType == "multitriangle") {
                sizeX = 2; sizeZ = 2; // 2x2
            } else if (blockType == "socdoor" || blockType == "multibranch") {
                sizeX = 1; sizeZ = 2; // 1x2
            }
            
            // 计算旋转角度
            int rotationSteps = (m_Rotation / 90) % 4;
            
            // 检查所有占用的位置
            for (int x = 0; x < sizeX; x++) {
                for (int z = 0; z < sizeZ; z++) {
                    WCoord offset;
                    
                    // 根据旋转角度调整偏移量
                    switch (rotationSteps) {
                        case 0: // 0度
                            offset = WCoord(x, 0, z);
                            break;
                        case 1: // 90度
                            offset = WCoord(z, 0, sizeX - 1 - x);
                            break;
                        case 2: // 180度
                            offset = WCoord(sizeX - 1 - x, 0, sizeZ - 1 - z);
                            break;
                        case 3: // 270度
                            offset = WCoord(sizeZ - 1 - z, 0, x);
                            break;
                    }
                    
                    WCoord checkPos = pos + offset;
                    
                    // 检查该位置是否可以放置
                    if (!m_World->canPlaceBlockAt(checkPos.x, checkPos.y, checkPos.z, blockId))
                        return false;
                        
                    // 检查权限
                    if (!m_World->CanBuildAtPosition(checkPos, playerUin))
                        return false;
                        
                    // 检查是否有建筑蓝图冲突
                    int existingBlockId = m_World->getBlockID(checkPos);
                    if (existingBlockId != 0) {
                        BlockDef* existingDef = GetDefManagerProxy()->getBlockDef(existingBlockId);
                        if (existingDef) {
                            const std::string& existingType = existingDef->Type.c_str();
                            if (existingType == "socdoor" || existingType == "socdoubledoor" || 
                                existingType == "multibranch" || existingType == "multistair" || 
                                existingType == "multitriangle") {
                                // 发现建筑蓝图冲突 - 在这里应该会阻止放置
                                return false; // 发现建筑蓝图冲突
                            }
                        }
                    }
                    
                    // 检查是否可替换
                    if (!CanReplace(checkPos, blockId))
                        return false;
                }
            }
            
            // 检查第一个位置的特殊放置条件
            auto hitface = ReverseDirection(result.face);
            IClientPlayer* player = nullptr;
            if (m_World && m_World->getActorMgr())
            {
                player = m_World->getActorMgr()->iFindPlayerByUin(playerUin);
            }
            if (!m_World->canPlaceActorOnSide(blockId, pos, false, hitface, NULL, result.facepoint, false, player))
                return false;
                
            return true;
        }
    }
    
    // 原有的单方块检测逻辑
    // 检查是否可以物理放置
    if (!m_World->canPlaceBlockAt(pos.x, pos.y, pos.z, blockId))
        return false;
        
    // 检查权限
    if (!m_World->CanBuildAtPosition(pos, playerUin))
        return false;
        
    // 检查是否可替换
    if (!CanReplace(pos, blockId))
        return false;
        
    auto hitface = ReverseDirection(result.face);
	IClientPlayer* player = nullptr;
	if (m_World && m_World->getActorMgr())
	{
		player = m_World->getActorMgr()->iFindPlayerByUin(playerUin);
	}
    if (!m_World->canPlaceActorOnSide(blockId, pos, false, hitface, NULL, result.facepoint, false, player))
        return false;
        
    return true;
}

WCoord BlockPlacementPreview::CalculatePlacementPosition(const WCoord& hitPos, int hitFace, int blockId)
{
    // This mirrors the logic in ClientPlayer_Interact.cpp for block placement
    
    WCoord placePos = hitPos;
    
    // Get the block material at the hit position
    BlockMaterial* material = m_World->getBlockMaterial(hitPos);
    int currentBlockId = m_World->getBlockID(hitPos.x, hitPos.y, hitPos.z);
    
    // Check for replacement cases
    if (material && material->isReplaceable() && currentBlockId != blockId)
    {
        // Place directly in the replaceable block's position
        return hitPos;
    }
    
    // Check for same-block placement (like stairs)
    bool placeInto = false;
    bool canPlaceAgain = material && material->canPlacedAgain(m_World, currentBlockId, 
                                                              Rainbow::Vector3f(0,0,0), // Placeholder for colpoint
                                                              hitPos, true, 
                                                              (DirectionType)hitFace);
    if (canPlaceAgain)
    {
        return hitPos; // Place in the same position
    }
    
    // Otherwise, place adjacent to the hit face
    switch (hitFace)
    {
        case DIR_NEG_X: placePos.x--; break;
        case DIR_POS_X: placePos.x++; break;
        case DIR_NEG_Y: placePos.y--; break;
        case DIR_POS_Y: placePos.y++; break;
        case DIR_NEG_Z: placePos.z--; break;
        case DIR_POS_Z: placePos.z++; break;
    }
    
    // Check if the adjacent position is replaceable
    if (!CanReplace(placePos, blockId))
    {
        // Invalid placement position - using extreme values to mark as invalid
        placePos = WCoord(INT_MAX, INT_MAX, INT_MAX);
    }
    
    return placePos;
}

void BlockPlacementPreview::UpdatePreviewBlock(const WCoord& pos, int blockId, int blockData,bool isValid)
{
    // 仅在有变化时更新
    if (m_PreviewPos == pos && m_PreviewBlockId == blockId && m_IsValidPlacement == isValid && m_blockData == blockData)
        return;
    
    m_PreviewPos = pos;
    m_IsValidPlacement = isValid;
    m_PreviewBlockId = blockId;
    m_blockData = blockData;
    // 设置渲染位置 - 在更新 m_PreviewBlockId 之前调用 setBlock
    setBlock(m_World, pos, blockId , blockData);
    
    // 根据有效性更新颜色
    UpdateColor();
}
// 静态辅助函数，用于检查方块类型是否支持预览
static bool IsSupportedBlockType(const std::string& blockType)
{
	static const char* supportedTypes[] = {
		"multiceiling",   // 天花板-地基
		"multiwall",      // 墙
		"socautodoor",    // 2518 扶梯门
		"socdoor",        // 2524 1X2 木门
		"socdoubledoor",  // 2516 2X2 双木门
		"multibranch",    // 1X2 立杆
		"multistair",     // 2X2楼梯
		"multitriangle"   // 2X2斜板
	};

	const int typeCount = sizeof(supportedTypes) / sizeof(supportedTypes[0]);
	for (int i = 0; i < typeCount; ++i)
	{
		if (blockType == supportedTypes[i])
			return true;
	}

	return false;
}

void BlockPlacementPreview::UpdateFromRaycast(int blockId, const IntersectResult& result)
{
    OPTICK_EVENT();
    // 检查是否是一个有效的方块
    BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockId);
    if (!blockDef || !m_World)
    {
        ShowPreview(false);
        return;
    }
	// 检查是否为支持预览的方块类型
	if (!IsSupportedBlockType(blockDef->Type.c_str()))
	{
		ShowPreview(false);
		return;
	}

	// 使用pickAll检测方块
	WorldPickResult pickResult = result.intersect_block ? WorldPickResult::BLOCK : WorldPickResult::NOTHING;
    
    if (pickResult == WorldPickResult::BLOCK && result.block != WCoord::infinity && result.face >= 0)
    {
        // 存储高亮方块信息
        m_HighlightedBlockId = m_World->getBlockID(result.block.x, result.block.y, result.block.z);
        m_HighlightedBlockFace = result.face;
        
        // 计算放置位置
        WCoord placementPos = CalculatePlacementPosition(result.block, result.face, blockId);
        
        // 如果找到了有效位置 (not at extreme values)
        if (placementPos.x != INT_MAX && placementPos.y != INT_MAX && placementPos.z != INT_MAX)
        {
            // 获取玩家UIN用于权限检查
            long long playerUin = GetClientInfoProxy()->getUin();
            
            // 检查放置是否有效
            bool isValid = IsValidPlacement(placementPos, blockId, playerUin, result);

			// 使用getPlaceBlockDataWithPlayer获取正确的方块数据
			int blockData = 0;
			IClientPlayer* player = nullptr;
            if (m_World && m_World->getActorMgr())
            {
                player = m_World->getActorMgr()->iFindPlayerByUin(playerUin);
            }
            auto hitface = ReverseDirection(result.face);
            BlockMaterial* material = g_BlockMtlMgr.getMaterial(blockId);
            if (player) {
                blockData = material->getPlaceBlockDataWithPlayer(
                    m_World,
                    player,
                    placementPos,
                    hitface,//(DirectionType)result.face,
                    result.facepoint.x,
                    result.facepoint.y,
                    result.facepoint.z,
                    0
                );
                bool b_socautodoor = blockDef->Type == "socautodoor";////2518 扶梯门
                if (b_socautodoor)
                {
                    blockData += 12;//按照门的逻辑来
                }
                
                // 应用预览旋转角度到方块数据
                if (m_Rotation > 0)
                {
                    // 根据方块类型和旋转角度调整方块数据
                    // 大多数方向性方块的旋转逻辑：每90度对应direction的变化
                    int rotationSteps = m_Rotation / 90; // 0,1,2,3对应0,90,180,270度
                    
                    // 对于支持旋转的方块类型，调整其朝向数据
                    if (blockDef->Type == "multiceiling" || blockDef->Type == "multiwall" || 
                        blockDef->Type == "socautodoor" || blockDef->Type == "socdoor" || 
                        blockDef->Type == "socdoubledoor" || blockDef->Type == "multibranch" || 
                        blockDef->Type == "multistair" || blockDef->Type == "multitriangle")
                    {
                        // 提取原始朝向
                        int originalDir = blockData % 4; // 假设朝向信息在低2位
                        int otherData = blockData & (~3); // 其他数据位
                        
                        // 应用旋转
                        int newDir = (originalDir + rotationSteps) % 4;
                        blockData = otherData | newDir;
                    }
                }
             /*   else if (b_socdoor)
                {
                    blockData = player->getCurPlaceDir();
                }*/
                  

            }

		
            // 更新预览
            UpdatePreviewBlock(placementPos, blockId, blockData,isValid);
            ShowPreview(true);
        }
        else
        {
            // 没有有效的放置位置
            ShowPreview(false);
        }
    }
    else
    {
        // 没有看着有效的方块
        ShowPreview(false);
    }
}

void BlockPlacementPreview::setBlock(World* pworld, const WCoord& grid, int blockId, int blockData)
{
    if (!pworld)
        return;
        
    bool immediateUpdate = false;
    
    Chunk* chunk;
    //chunk->getg

    //if (grid == m_CurBlock && m_PreviewBlockId == blockId)
    //{
    //    // 只有在方块数据改变时才强制更新
    //    Section* psection = pworld->getSection(grid);
    //    if (psection != nullptr) 
    //    {
    //        WCoord offset = grid - psection->m_Origin;
    //        int curData = psection->getBlockData(offset);
    //        immediateUpdate = curData != m_CacheBlockData;
    //    }
    //}
    
    //if (grid != m_CurBlock || m_PreviewBlockId != blockId || immediateUpdate)
	const ItemDef* itemdef = GetDefManagerProxy()->getItemDef(blockId);
	if (itemdef)
	{
		bool scripthandled = false;
		int setBlockAllRet = 0;//脚本中使用setBlockAll是否成功返回值。返回值0为修改了block数据（setBlockAll,setBlockData, placeBlock等调用成功），1为没有修改，2,3为其他(0和1返回值是在使用的)

		DirectionType tmpface;// = targetface;
		//MINIW::ScriptVM::game()->callFunction(itemdef->UseScript.c_str(), "u[ClientPlayer]u[World]iiii>bi", this, getWorld(), tmpPos.x, tmpPos.y, tmpPos.z, tmpface, &scripthandled, &setBlockAllRet);
	}
    {
        // 清除之前的网格数据
        m_DecalSectionMesh->CleanUp();
        
        Section* psection = pworld->getSection(grid);
        if (psection == nullptr) 
        {
            Show(false);
            return;
        }
        
        // 为部分网格设置原点
        WCoord offset = grid - psection->m_Origin;
        m_DecalSectionMesh->OnOriginChanged(psection->m_Origin);
        
        // 获取预览方块的定义，而不是当前位置的方块
        BlockMaterial* blockMtl = g_BlockMtlMgr.getMaterial(blockId);
        if (blockMtl) {
            // 为网格设置当前方块ID以创建正确的视觉预览
            m_DecalSectionMesh->SetCurBlockId(blockMtl->getBlockResID());
            
            Section tempScetion(psection->GetChunk(),0);
            tempScetion.initialize();
            tempScetion.m_Origin = psection->m_Origin;
			tempScetion.setBlock(offset.x, offset.y,offset.z, blockId, blockData);
            // 为预览构建网格数据
            BuildSectionMeshData data;
            data.m_World = pworld;
            data.m_SharedSectionData = &tempScetion;//psection;
            data.m_SharedChunkData = nullptr;// &psection->GetChunk()->GetSharedChunkData();
            data.m_SectionMesh = nullptr;
            // 为预览创建方块网格
            blockMtl->createBlockMeshPreview(data, offset, m_DecalSectionMesh);
        }
        
        // 将网格数据上传到GPU
        m_DecalSectionMesh->UploadMeshData();
        
        // 更新状态
        m_CurBlock = grid;
        //m_CacheBlockData = psection->getBlockData(offset);
        
        // 更新渲染对象
        if (m_RenderObject != nullptr)
        {
            m_RenderObject->SetDirtyFlags(SceneObject::kDirtyFlagsWorldTransform | SceneObject::kDirtyFlagsBounds);
        }
    }
    
    // 显示网格
    Show(true);
    
    // 根据放置有效性更新材质颜色
    UpdateColor();
} 

void BlockPlacementPreview::RotatePreview()
{
    // 每次旋转90度
    m_Rotation = (m_Rotation + 90) % 360;
    
    // 如果当前有预览显示，重新更新预览以应用旋转
    if (m_IsVisible && m_PreviewBlockId > 0)
    {
        // 强制重新计算预览，应用新的旋转角度
        UpdatePreviewBlock(m_PreviewPos, m_PreviewBlockId, m_blockData, m_IsValidPlacement);
    }
}

void BlockPlacementPreview::SetRotation(int rotation)
{
    // 限制角度在0-270度范围内
    m_Rotation = ((rotation % 360) / 90) * 90;
    if (m_Rotation < 0) m_Rotation += 360;
    
    // 如果当前有预览显示，重新更新预览以应用旋转
    if (m_IsVisible && m_PreviewBlockId > 0)
    {
        UpdatePreviewBlock(m_PreviewPos, m_PreviewBlockId, m_blockData, m_IsValidPlacement);
    }
} 
