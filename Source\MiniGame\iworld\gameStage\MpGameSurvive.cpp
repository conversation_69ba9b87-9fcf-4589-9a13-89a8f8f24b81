
#include "MpGameSurvive.h"
#include "MpPlayerControl.h"
#include "GameNetManager.h"

#include "WorldManager.h"
#include "ClientActorManager.h"
#include "ClientMob.h"
#include "MinimapRenderer.h"
#include "container_backpack.h"
#include "GameEvent.h"
#include "ActorBody.h"
#include "GameMode.h"
#include "DebugDataMgr.h"
#include "FullyCustomModelMgr.h"
#include "PackingFullyCustomModelMgr.h"
#include "CustomModelMgr.h"
#include "ImportCustomModelMgr.h"
#include "ObserverEventManager.h"
//#include "TaskSubSystem.h"
#include "ResourceCenter.h"
//#include "TriggerScriptMgr.h"
#include "Input/OgreInputManager.h"
#include "File/FileManager.h"
#include "Network/HttpManager.h"
#include "OgreTimer.h"
#include "OgreScriptLuaVM.h"
#include "Sound/OgreSoundSystem.h"
#include "OgreStringUtil.h"
#include "Platforms/PlatformInterface.h"
//#include "ModManager.h"
#include "Platforms/PlatformInterface.h"
#include "LegacyCompress.h"
#include "File/DirVisitor.h"
#include "json/jsonxx.h"
#include <stdlib.h>
#include <stdio.h>
#include "OgreUtils.h"
#include "RoomClient.h"
#include "ActorLocoMotion.h"
#include "PlayerAttrib.h"
#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"

#include "ICloudProxy.h"
#include "ZmqProxy.h"

#include "ClientGameNetHandler.h"
#include "MpGameSuviveNetHandler.h"
#include "MpGameSuviveLoadHandler.h"
#include "StarStationTransferMgr.h"
//#include "OgreEntityMotionData.h"

#include "GameSuviveUiHandler.h"
#include "GameMode.h"

#include "GameMode.h"
#include "TeamSetterComponent.h"
//#include "ChangeModelMgr.h"
#include "DataServerClient.h"
#include "GameInfoProxy.h"
#include "DefManagerProxy.h"
#include "ClientInfoProxy.h"
#include "RiddenComponent.h"
#include "BindActorComponent.h"
#include "GlobalFunctions.h"
#include "PermitsSubSystem.h"
#include "WorldArchiveMgr.h"

#ifdef IWORLD_SERVER_BUILD
#include "game_event.pb.h"
#include "AINpc.h"
#endif
//TODO:CONFIRME
#ifdef MODULE_SMALL_PACKAGE
#include "ModuleResManager.h"
#endif

#include "SandboxReplicatorRoot.h"
//#include "ServerReplicator.h"
#include "ThornBallComponent.h"
#include "SandboxType2Lua.h"
#include "ClientErrCode.h"

#if defined(_MSC_VER)
#define strtoll _strtoi64
#endif


#include "ChunkGenerator.h"
#include "PlayerCheat.h"
#include "ClientErrCode.h"
#include "IWorldConfigProxy.h"
#include "Entity/OgreEntityMotionData.h"
#include "Optick/optick.h"
#include "ClientGameReport.h"
#include "SandboxIdDef.h"
#include "ClientGameStatistics.h"
#include "GameAnalytics.h"

#ifdef SDB_MULTI_SCENE_OPEN //多场景支持 
#include "SandboxSceneMgrService.h"
#endif //  SDB_MULTI_SCENE_OPEN //多场景支持 
#include "backpack.h"
#include "ClientAccount.h"
using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;
static PlayerBriefInfo myBriefInfo;
EffectParticle *s_Effect = NULL;
extern const char *GetWorldThumbPath(char *buf, long long owid, int specialType);
extern const char *GetWorldThumbPathForWrite(char *buf, long long owid, int specialType);
extern const char *GetWorldThumbPathForWriteWebp(char *buf, long long owid, int specialType);

#define MPGAME_SURVIVE_DEBUG_LOG 1
#if MPGAME_SURVIVE_DEBUG_LOG
#define MPGAME_SURVIVE_LOG(...) \
    do { \
        WarningStringMsg("[MpGameSurvive] " __VA_ARGS__); \
    } while(0)
#else
#define MPGAME_SURVIVE_LOG(...)
#endif

bool _isHost()
{
	return (GetGameNetManagerPtr() != nullptr &&
		(GetGameNetManagerPtr()->getMPGameType() & GAME_NET_MP_GAME_HOST));
}

bool _isClient()
{
	return (GetGameNetManagerPtr() != nullptr &&
		(GetGameNetManagerPtr()->getMPGameType() & GAME_NET_MP_GAME_CLIENT));
}

bool _isNotInit()
{
	return (GetGameNetManagerPtr() == nullptr ||
		GetGameNetManagerPtr()->getMPGameType() == GAME_NET_MP_GAME_NOT_INIT);
}


MpGameSurvive::MpGameSurvive(bool isreload)
{
	m_CurOpenContainer = SANDBOX_NEW(ClientWorldContainer);
	m_IsReload = isreload;

	m_organizerFirst = true;
	m_organizerUin =	int(strtoll(GetClientInfoProxy()->getEnterParam("account"), NULL, 10));
	m_dowProCallback = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GE_HTTP_DOWNLOADFILE_PROGRESS", nullptr, [&](SandboxContext context) -> SandboxResult {

		int task_id = (int)context.GetData_Number("task_id");
		int progress = (int)context.GetData_Number("progress");

		downloadFileProgress(task_id, progress);

		return SandboxResult(nullptr, true);
	});
	m_FirstPlayerEnter = false;
	m_LastCheckTime = 0;
	m_lastReportOnlineTime = 0;
	ReachabilityManager::GetInstance().AddObserver(this);
	MINIW::ScriptVM::game()->callFunction("GetReportOnlineTime", ">i", &m_ReportOnlineTime);
	if (m_ReportOnlineTime <= 0)
		m_ReportOnlineTime = 600;
		//TODO:CONFIRME
#ifndef DEDICATED_SERVER
	// 上报玩家进入联机
	MINIW::ScriptVM::game()->callFunction("ReportHostMultiGame", "i", GetClientAccountMgr().getUin());
#endif

#ifdef MODULE_SMALL_PACKAGE
	GetModuleResManager().OnMpGameChange(true);
#endif
}


MpGameSurvive::~MpGameSurvive()
{
	if (m_PlayerCtrl)
	{
		m_PlayerCtrl->closeContainer();
	}
	SANDBOX_DELETE(m_CurOpenContainer);
	SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe("GE_HTTP_DOWNLOADFILE_PROGRESS", m_dowProCallback);
	for(size_t i=0; i<m_BriefPlayerInfo.size(); i++)
	{
		SANDBOX_DELETE(m_BriefPlayerInfo[i]);
	}
	m_BriefPlayerInfo.clear();
	ReachabilityManager::GetInstance().RemoveObserver(this);
	//TODO:CONFIRME
#ifdef MODULE_SMALL_PACKAGE
	GetModuleResManager().OnMpGameChange(false);
#endif
}

const char *MpGameSurvive::getName()
{
	return SurviveGame::getName();
}

const char *MpGameSurvive::getTypeName()
{
	return "MpGameSurvive";
}

int MpGameSurvive::getDebugInfo(char *buffer, int buflen)
{
	int bufpos = SurviveGame::getDebugInfo(buffer, buflen);

	if(m_WorldMgr->isGameMakerRunMode())
	{
		GameMode *rulemgr = static_cast<GameMode*>(m_WorldMgr->m_RuleMgr);
		bufpos += snprintf(buffer+bufpos, buflen-bufpos, "\nStage: %d, GameTime:%d,", rulemgr->getGameStage(), rulemgr->getGameStageTime()/20);

		bufpos += snprintf(buffer+bufpos, buflen-bufpos, "  Score(");
		for(size_t i=0; i<m_BriefPlayerInfo.size(); i++)
		{
			PlayerBriefInfo *info = m_BriefPlayerInfo[i];
			bufpos += snprintf(buffer+bufpos, buflen-bufpos, " %s:%d", info->nickname, info->cgamevar[0]);
		}

		bufpos += snprintf(buffer+bufpos, buflen-bufpos, " %s:%d)", m_PlayerCtrl->getNickname(), m_PlayerCtrl->getGameScore());
	}

	WCoord pos = m_PlayerCtrl->getLocoMotion()->getPosition();
	Rainbow::Vector3f dir = m_PlayerCtrl->getLocoMotion()->getLookDir();
	bufpos += snprintf(buffer + bufpos, buflen - bufpos, " (%.4f, %.4f) pos(%d,%d,%d), dir(%.4f,%.4f,%.4f)", 
		m_PlayerCtrl->getLocoMotion()->m_RotateYaw, 
		m_PlayerCtrl->getLocoMotion()->m_RotationPitch, 
		pos.x, pos.y, pos.z, 
		dir.x, dir.y, dir.z
	);
	
	if(m_WorldMgr->isGameMakerRunMode())
	{
		for(size_t i=0; i<m_BriefPlayerInfo.size(); i++)
		{
			if ( i % 3 == 0 )
			{
				bufpos += snprintf(buffer+bufpos, buflen-bufpos, "\n");
			}
			PlayerBriefInfo *info = m_BriefPlayerInfo[i];
			bufpos += snprintf(buffer+bufpos, buflen-bufpos, "%s = {hp = %.2f/%.0f(%.0f+%.0f), strength = %.2f/%.0f(%.0f+%.0f)}, ", 
				info->nickname, 
				info->hp, info->maxhp, info->maxhp, info->overflowhp,
				info->strength, info->maxstrength + info->overflowstrength, info->maxstrength, info->overflowstrength
			);
		}
	}


	return bufpos;
}

MNSandbox::AutoRef<PlayerControl> MpGameSurvive::createPlayerControl()
{
	return MpPlayerControl::NewInstance().ToCast<PlayerControl>();
}

PlayerBriefInfo *MpGameSurvive::findPlayerInfoByUin(int uin)
{
	for(size_t i=0; i<m_BriefPlayerInfo.size(); i++)
	{
		if(m_BriefPlayerInfo[i]->uin == uin) return m_BriefPlayerInfo[i];
	}

/*	if(m_PlayerCtrl && uin == m_PlayerCtrl->getUin())
	{
		return getPlayerBriefInfo(-1);
	}
 */
	return NULL;
}

PlayerBriefInfo *MpGameSurvive::addPlayerBriefInfo(int uin)
{
	assert(findPlayerInfoByUin(uin) == NULL);

	PlayerBriefInfo *info = SANDBOX_NEW(PlayerBriefInfo, uin);
	m_BriefPlayerInfo.push_back(info);
	m_FirstPlayerEnter = true;
	return info;
}

void MpGameSurvive::clearPlayerBriefInfo(std::vector<PlayerBriefInfo *>&existinfo)
{
#ifdef DEDICATED_SERVER
	int now = MINIW::GetTimeStamp();
#endif
	std::vector<PlayerBriefInfo *>::iterator iter = m_BriefPlayerInfo.begin();
	while(iter != m_BriefPlayerInfo.end())
	{
		PlayerBriefInfo *p = *iter;
#ifdef IWORLD_SERVER_BUILD
		// 云服异步加载玩家数据, 会有一段时间存在playerBriefInfo但没有ClientPlayer对象, 此时可能触发playerBreifInfo被清理
		// 此处设定: 设置 enterTime 一段时间后才能清理此 playerBriefInfo
		if (now - p->enterTime < 15)
		{
			iter++;
			continue;
		}
#endif
		if(std::find(existinfo.begin(), existinfo.end(), p) == existinfo.end())
		{
			SANDBOX_DELETE(p);
			iter = m_BriefPlayerInfo.erase(iter);
		}
		else iter++;
	}
}
int MpGameSurvive::clearPlayerBriefInfoByUin(const std::set<int>& uins)
{
	int clear_num = 0;
	std::vector<PlayerBriefInfo *>::iterator iter = m_BriefPlayerInfo.begin();
	while(iter != m_BriefPlayerInfo.end())
	{
		if (uins.count((*iter)->uin) > 0){
			SANDBOX_DELETE(*iter);
			iter = m_BriefPlayerInfo.erase(iter);
			++clear_num;
		} else {
			iter++;
		}
	}
	return clear_num;
}
int MpGameSurvive::clearPlayerBriefInfoByUin(const std::vector<int>& uins)
{
	int clear_num = 0;
	for (auto iter=uins.begin(); iter!=uins.end(); ++iter){
		for (auto briefInfoIter=m_BriefPlayerInfo.begin(); briefInfoIter!=m_BriefPlayerInfo.end(); ++briefInfoIter){
			if ((*briefInfoIter)->uin == *iter){
				SANDBOX_DELETE(*briefInfoIter);
				m_BriefPlayerInfo.erase(briefInfoIter);
				++clear_num;
				break;
			}
		}
	}
	return clear_num;
}
int MpGameSurvive::clearPlayerBriefInfoByUin(int uin)
{
	for (auto iter=m_BriefPlayerInfo.begin(); iter!=m_BriefPlayerInfo.end(); ++iter){
		if ((*iter)->uin == uin){
			SANDBOX_DELETE(*iter);
			m_BriefPlayerInfo.erase(iter);
			return 1;
		}
	}
	return 0;
}
bool MpGameSurvive::isHost(int uin)
{
	//云服主机判断
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->IsRentServerHost())
	{
		return true;
	}
	//云服客机判断
	if (ROOM_SERVER_RENT == GetGameInfoProxy()->GetRoomHostType())
	{
		return false;
	}
	//普通房间判断
	if(GetGameNetManagerPtr())
		return uin == GetGameNetManagerPtr()->getHostUin();
	
	return false;
}

int MpGameSurvive::getHostUin()
{
	if (GetGameNetManagerPtr())
		return GetGameNetManagerPtr()->getHostUin();

	return 0;
}

bool MpGameSurvive::canAcceptClientJoin()
{
	int kicktype = 0;
	std::string reason = "";
	return this->canAcceptClientJoin(kicktype, reason);
}


bool MpGameSurvive::canAcceptClientJoin(int& kicktype, std::string& reason)
{
	if (m_WorldMgr && m_WorldMgr->isGameMakerRunMode())
	{
		GameMode *rulemgr = static_cast<GameMode*>(m_WorldMgr->m_RuleMgr);
		CGAME_STAGE_TYPE stageVal = rulemgr->getGameStage();
		if(rulemgr->getRuleOptionVal(GMRULE_ALLOW_MIDWAYJOIN) == 0 && stageVal!=CGAME_STAGE_PREPARE && stageVal!=CGAME_STAGE_SELECTTEAM && stageVal!= CGAME_STAGE_SHOWINTROS)
		{
			//20211220 云服情况下，第一个玩家总是允许加入
#ifdef IWORLD_SERVER_BUILD
			if (m_FirstPlayerEnter == false) {
				return true;
			}
#endif
			return false;
		}
	}
	return true;
}

ClientPlayer *MpGameSurvive::onPlayerEnter(int uin)
{
	//LOG_INFO("MpGameSurvive::onPlayerEnter(): uin = %d", uin);
	assert(m_WorldMgr);
	if(m_WorldMgr == NULL) return NULL;

	if (ResourceCenter::GetInstancePtr())
		ResourceCenter::GetInstancePtr()->syncClassInfoToClient(uin);

	if (CustomModelMgr::GetInstancePtr())
	{
		//CustomModelMgr::GetInstancePtr()->syncClassInfoToClient(uin);
		CustomModelMgr::GetInstancePtr()->checkSyncCustomModelData(0, 0, false, uin);
	}

	if (FullyCustomModelMgr::GetInstancePtr() && FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr())
		FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr()->syncPackingFcm(uin, 0);

	if (ImportCustomModelMgr::GetInstancePtr())
	{
		ImportCustomModelMgr::GetInstancePtr()->syncImportModel(uin, 0);
	}
		
	PlayerBriefInfo* info = findPlayerInfoByUin(uin);
	if (info == NULL) return NULL;

	PB_RoleEnterWorldHC roleEnterWorldHC;
	PB_OWGlobal* pOwGlobal = roleEnterWorldHC.mutable_globalinfo();
	m_WorldMgr->saveGlobal(pOwGlobal);

	

	ClientPlayer *player = SANDBOX_NEW(ClientPlayer);
	
	int playerindex = ComposePlayerIndex(info->model, info->geniuslv, info->skinid);
	player->init(uin, info->nickname, playerindex, info->customjson);
	player->m_BPTitle = info->bptitle;	// 悦享赛事称号 20230606 by wuyuwang
	player->getBody()->setPlayerFrameId(info->frameid);
	player->setVipInfo(info->vipinfo);

	PB_ActionAttrStateHC actionAttrStateHC;
	unsigned int permitStates = ENABLE_NEWINITVALUE;
	if (GetWorldManagerPtr() != NULL && GetWorldManagerPtr()->getBaseSettingManager())
	{
		permitStates = GetWorldManagerPtr()->getBaseSettingManager()->getPermitStatesValue();
	}
	actionAttrStateHC.set_attr(permitStates);
	GetGameNetManagerPtr()->sendToClient(uin, PB_ACTION_ATTR_STATE_HC, actionAttrStateHC);

	player->setTeam(m_WorldMgr->getNewPlayerTeamID(player));



	WCoord pos;
	World *pworld = NULL;
	bool prepare = true;
	if(m_WorldMgr->isGameMakerRunMode())
	{
		//bool prepare = true;
		WCoord pt = m_WorldMgr->getTeamPrePoint(player);
		GameMode *gmaker = static_cast<GameMode*>(m_WorldMgr->m_RuleMgr);

		if (pt.y < 0  || gmaker->getGameStage() == CGAME_STAGE_RUN)
		{
			if ( GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->hasInitialPoint(player->getTeam()))
			{
				pt = GetWorldManagerPtr()->m_RuleMgr->getInitialPoint(player->getTeam()).y >= 0 ? \
					GetWorldManagerPtr()->m_RuleMgr->getInitialPoint(player->getTeam()) : \
					GetWorldManagerPtr()->m_RuleMgr->getInitialPoint(0);
			}
			else
				pt = m_WorldMgr->getTeamSpawnPoint(player);
			prepare = false;
		}
		int _mapid = 0;
		SDB_GET_START_MAPID(_mapid, uin)
		pworld = m_WorldMgr->getOrCreateWorld(_mapid, player);
		player->gotoBlockPos(pworld, pt, false);
		
		gmaker->initPlayerDir(player);
		bool hasrole = player->loadFromFile(m_WorldMgr->getWorldId(), uin, gmaker->getRuleOptionVal(GMRULE_SAVEMODE) == 0, m_WorldMgr->getSpecialType());
		roleEnterWorldHC.set_hasrole(hasrole);

		int countplayer = getPlayerCountWithoutJudge();
		if(gmaker->getGameStage() == CGAME_STAGE_PREPARE)
		{
			if (gmaker->getRuleOptionVal(GMRULE_STARTMODE) == 2) //不限条件
			{
				gmaker->onGameStart();
			} 
			else if (gmaker->getRuleOptionVal(GMRULE_STARTPLAYERS)<=countplayer)
			{
				if(gmaker->getRuleOptionVal(GMRULE_STARTMODE) == 1) //自动开启
				{
					gmaker->setCustomGameStage(CGAME_STAGE_COUNTDOWN);
				}
				else //房主开启
				{
				#ifdef DEDICATED_SERVER
					// 房主开启类型统一改为玩家数量达到自动开启 20210902 codeby:liusijia
					// if (ROOM_SERVER_OFFICIAL == GetGameInfoProxy()->GetRoomHostType())
						GetGameEventQue().postSimpleEvent("GIE_WAITHOST_STARTGAME");
				#endif
				}
			}
		}

		MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", "");
	}
	else
	{
		bool hasrole = player->loadFromFile(m_WorldMgr->getWorldId(), uin, true, m_WorldMgr->getSpecialType());
		int _mapid = hasrole ? player->getCurMapID():0;
		SDB_GET_START_MAPID(_mapid, uin)
		if(!hasrole)
		{
			pworld = m_WorldMgr->getOrCreateWorld(_mapid, player);
			#ifndef DEDICATED_SERVER
				SandboxResult result;
				SandboxContext sandboxContext;
				m_PlayerCtrl->Event2().Emit<SandboxResult &, SandboxContext >("revive_getRevivePointEx", result, sandboxContext);
				pos = result.GetData_UserObject<WCoord>("point");
				if(pos.y < 0) pos = m_WorldMgr->getSpawnPoint();
			#else
				pos = m_WorldMgr->getSpawnPoint();
			#endif
			player->gotoBlockPos(pworld, pos, pworld->hasSky());

			roleInit(pworld->getOWID(), player);
		}
		else
		{
			pworld = m_WorldMgr->getOrCreateWorld(_mapid, player);
		}
		roleEnterWorldHC.set_hasrole(hasrole);
	}
	
	pworld->getActorMgr()->ToCastMgr()->spawnPlayerAddRef(player);
	player->release();

	// 观察者事件接口
	ObserverEvent_Player obevent(player->getUin());
	GetObserverEventManager().OnTriggerEvent("Player.JoinTeam", &obevent);

	//家园的出生点特殊处理
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_getSpawnBlockPos", SandboxContext(nullptr).SetData_Number("SpawnPosType", 0));
	if (result.IsSuccessed())
	{
		WCoord position = result.GetData_UserObject<WCoord>("wcoord");
		player->gotoBlockPos(pworld, position, false);
	}

	info->mapid = pworld->getCurMapID();

	PlayerAttrib& playerAttrib = *player->getPlayerAttrib();

	info->overflowhp = playerAttrib.getOverflowHP();
	info->maxhp = playerAttrib.getMaxHP();
	info->hp = playerAttrib.getHP();

	info->overflowstrength = playerAttrib.getOverflowStrength();
	info->maxstrength = playerAttrib.getMaxStrength();
	info->strength = playerAttrib.getStrength();

	info->x = player->getPosition().x;
	info->y = player->getPosition().y;
	info->z = player->getPosition().z;

	PB_PlayerInfo* playerInfo = roleEnterWorldHC.mutable_playerinfo();
	playerInfo->set_objid(player->getObjId());
	playerInfo->set_anim(player->getBody()->getCurAnim(0));
	playerInfo->set_anim1(player->getBody()->getCurAnim(1));
	playerInfo->set_animweapon(player->getBody()->getCurAnimWeapon());
	playerInfo->set_bodycolor(player->getBody()->getBodyColor());
	playerInfo->set_customscale(player->getCustomScale());
	playerInfo->set_actseqid(player->getBody()->getActSeqID());
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	SANDBOX_ASSERT(player->GetNodeid() != 0);
	playerInfo->set_nodeid(player->GetNodeid());
	// LOG_INFO("[player_nodeid] send player nodeid: %d , objid:%d", player->GetNodeid(), player->getObjId());
#endif//SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	auto sawtooth_ = playerInfo->mutable_sawtooth();
	auto thornComponent = player->getThornBallComponent();
	if (thornComponent != nullptr)
	{
		for (int i = 0; i < thornComponent->getThornAnchorNum(); i++)
		{
			auto info = thornComponent->getThornAnchorAt(i);
			PB_SawtoothInfo* roleinfodata = sawtooth_->Add();
			roleinfodata->set_sawtoothid(info.anchorId);
			PB_Vector3* vec = roleinfodata->mutable_pos();
			vec->set_x(info.pos.x);
			vec->set_y(info.pos.y);
			vec->set_z(info.pos.z);
		}
	}

	PB_SkillCDData* skillCDData = roleEnterWorldHC.mutable_skillcddata();
	player->saveSkillCDCompToPB(skillCDData);

	auto iterItems = player->m_UnlockItems.begin();
	for (; iterItems != player->m_UnlockItems.end(); iterItems++)
	{
		roleEnterWorldHC.add_unlockitems(*iterItems);
	}

	WCoord realPos = player->getPosition();

	PB_RoleData* roleData = playerInfo->mutable_roledata();
	player->changeRoleData(roleData);

	PB_Pos* rolePos = roleData->mutable_pos();
	rolePos->set_x(realPos.x);
	rolePos->set_y(realPos.y);
	rolePos->set_z(realPos.z);

	fillCurWorldDesc(roleEnterWorldHC.mutable_worlddesc());

	// 基础设置，如果有开局介绍，同步给客机相关文件
	const int urllen = 255;
	char url[urllen+1] = {0};
	MINIW::ScriptVM::game()->callFunction("GetCurUploadingIntroUrl", ">s", url);
	url[urllen] = 0;
	if (strlen(url) > 1)
	{
		roleEnterWorldHC.set_url(url);
	}

	GetGameNetManagerPtr()->sendToClient(uin, PB_ROLE_ENTER_WORLD_HC, roleEnterWorldHC);
	//先同步文件信息再执行onplayerinit，不然客机第一次进房间会拿不到补给箱的机械胶囊
	//create replicator for this client
	//auto replicatorRoot = MNSandbox::GetCurrentReplicatorRoot();
	//if (replicatorRoot) {
	//	replicatorRoot->RegisterServerReplicator(uin);
	//}

	if (m_WorldMgr && m_WorldMgr->isGameMakerRunMode() && m_WorldMgr->m_RuleMgr)
	{
		m_WorldMgr->m_RuleMgr->onPlayerInit(player, false, prepare);

		////设置队伍数据
		//if (m_WorldMgr->getBaseSettingManager() && m_WorldMgr->getBaseSettingManager()->getTeamEnable(player->getTeam()))
		//{
		//	//初始物品
		//	m_WorldMgr->m_RuleMgr->initPlayerBaseItem(player, player->getShortcutStartIndex(), player->getTeam());
		//	m_WorldMgr->m_RuleMgr->initPlayerBaseItem(player, BACKPACK_START_INDEX, player->getTeam());
		//}
	}
	//设置玩家的出生点
	//player->setSpawnPoint(m_WorldMgr->getSpawnPointEx(pworld), false);
	SandboxContext context;
	context.SetData_UserObject("point", m_WorldMgr->getSpawnPointEx(pworld));
	context.SetData_Bool("bSetWorldMgr", false);
	player->Event().Emit("revive_setSpawnPoint", context);

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_sendPermitMsg",
		SandboxContext(nullptr).SetData_Number("targetuin", uin));

	if (ROOM_SERVER_RENT == GetClientInfoProxy()->getRoomHostType())
	{

		SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_sendCSAllAuthority",
			SandboxContext(nullptr).SetData_Number("uin", uin));

		SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_sendCSClientPermitMsg",
			SandboxContext(nullptr).SetData_Number("srcuin", uin));
	}
	
	MINIW::ScriptVM::game()->callFunction("ReqCurUseAchieveByUin", "i", uin);
	//2021-12-20 codeby: wangyang ��Աͼ��
	char buffer[64];
	sprintf(buffer, "%d", uin);
	MINIW::ScriptVM::game()->callFunction("OnPlayerEnter", "s", buffer);

	//ͬ同步已创建的图文信息

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("GraphicsMgr_snycAllGraphics",SandboxContext(nullptr).SetData_Number("uin", uin));
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ChangeModelMgr_updateAllActor2Client", SandboxContext(nullptr).SetData_Number("uin", uin));

	//开发者在基础设置中设置有自定义模型
	if (m_WorldMgr->isGameMakerRunMode() && m_WorldMgr->getBaseSettingManager())
	{
		GameMode *setterMgr = static_cast<GameMode*>(m_WorldMgr->getBaseSettingManager());
		for (int teamId = -1; teamId < MAX_GAME_TEAMS; ++teamId)
		{
			string modeldata = setterMgr->getPlayerBaseModelData(teamId, true);
			if (!modeldata.empty()) //主机设置有自定义模型
			{
				PB_CustomBaseModelHC baseModelHC;
				baseModelHC.set_uin(player->getUin());
				baseModelHC.set_teamid(teamId);
				baseModelHC.set_modeldata(modeldata.c_str());
				GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_PLAYER_CUSTOM_BASEMODEL_HC, baseModelHC);
			}
		}
	}

	// 调用lua 接口  同步内容生态业务逻辑入口 codeby qinpeng
	MINIW::ScriptVM::game()->callFunction("ClientPlayerEnterWorld", "i", uin);
	return player;
}

bool MpGameSurvive::load()
{
	SurviveGame::load();

	m_netHandler->load();

	return true;
}


void MpGameSurvive::updateNearPlayerInfo()
{
	OPTICK_EVENT();
	if(m_PlayerCtrl==NULL || m_PlayerCtrl->getWorld()==NULL) return;

	ClientActorMgr *actormgr = m_PlayerCtrl->getWorld()->getActorMgr()->ToCastMgr();
	for(size_t i=0; i<(size_t)actormgr->getNumPlayer(); i++)
	{
		ClientPlayer *player = actormgr->getIthPlayer(i);
		PlayerBriefInfo *info = findPlayerInfoByUin(player->getUin());
		if(info == NULL) continue;

		//info->hp = (int)player->getPlayerAttrib()->getHP();
		info->mapid = player->getCurMapID();
		info->x = player->getPosition().x;
		info->y = player->getPosition().y;
		info->z = player->getPosition().z;
	}
}

ClientPlayer *MpGameSurvive::uin2Player(int uin)
{
	if (m_WorldMgr)
	{
		IClientPlayer* iplayer = m_WorldMgr->getPlayerByUin(uin);
		return iplayer ? iplayer->GetPlayer() : nullptr;
	}
	
	return NULL;
}

int MpGameSurvive::getJudgeUin()
{
	if(m_WorldMgr == NULL) return 0;
	std::vector<IClientPlayer *> players;
	m_WorldMgr->getAllPlayers(players);
	for(int i = 0; i<(int)players.size(); i++)
	{
		if(players[i]->GetPlayer()->getTeam() == JUDGE_TEAM_ID)
		{
			return 	players[i]->getUin();
		}
	}
	return 0;
}

ClientActor *MpGameSurvive::objId2ActorOnClient(long long objId)
{
#ifndef DEDICATED_SERVER
	if (m_PlayerCtrl == nullptr)
		return nullptr;

	if (m_PlayerCtrl->getWorld() == nullptr)
		return nullptr;

	if (m_PlayerCtrl->getWorld()->getActorMgr() == nullptr)
		return nullptr;

	auto actor = m_PlayerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objId);
#else
	if (m_WorldMgr == nullptr)
		return nullptr;
	auto actor = m_WorldMgr->findActorByWID(objId);
#endif
	return actor ? actor->GetActor() : nullptr;
}

void MpGameSurvive::setupPlayerAndVM()
{
	SurviveGame::setupPlayerAndVM();
	if (MINIW::ScriptVM::game())
	{
		BaseContainer::SetLuaOpenContainer("ClientWorldContainer", m_CurOpenContainer);
	}
	if (m_CurOpenContainer)
	{
		m_CurOpenContainer->mPlayer = m_PlayerCtrl;
	}
	// 将此处初始化提前, 避免把加载或收到服务器的属性覆盖 2021.12.24 by 黄林
	if (m_PlayerCtrl) {
		//基础模型、属性
		int teamId = m_PlayerCtrl->getTeam();
		m_PlayerCtrl->setBaseModel(teamId);
		PlayerAttrib* playerAttr = dynamic_cast<PlayerAttrib*>(m_PlayerCtrl->getPlayerAttrib());
		if (playerAttr)
			playerAttr->initPlayerBaseAttr(teamId);
	}
}

void MpGameSurvive::fillCurWorldDesc(PB_WorldDesc *pWorldDesc)
{
	if (!pWorldDesc)
	{
		return;
	}
	WorldDesc *desc = GetClientInfoProxy()->getCurWorldDesc();
	if (!desc) return;
	pWorldDesc->set_owneruin(desc->owneruin);
	pWorldDesc->set_worldid(desc->worldid);
	pWorldDesc->set_worldtype(desc->worldtype);
	pWorldDesc->set_realowneruin(desc->realowneruin);
	pWorldDesc->set_worldopen(desc->open);
	pWorldDesc->set_worldname(desc->worldname);

	pWorldDesc->set_temptype(desc->TempType);
	pWorldDesc->set_pwid(desc->pwid);
	pWorldDesc->set_editorsceneswitch(desc->editorSceneSwitch);
	pWorldDesc->set_ctype(desc->CType);
	if (GetWorldArchiveMgrPtr() && GetWorldArchiveMgrPtr()->isCreateFromModPackWorld(desc))
	{
		pWorldDesc->set_fissiontype(1);
		pWorldDesc->set_fissionfrom(desc->pwid);
		pWorldDesc->set_fissionversion(desc->pver);

	}

	if (GetWorldArchiveMgrPtr())
	{
		std::string jsonStr;
		GetWorldArchiveMgrPtr()->getWorldExtraInfoJsonStr(desc->worldid, jsonStr);
		pWorldDesc->set_extrainfo(jsonStr);

	}


	int flag = (int)desc->passportflag;
	if (flag > 0) {
		flag = (flag == 10 ? 2 : 1);
		if (flag == 1 && desc->owneruin != desc->realowneruin) {
			int iEndTime = 0;
			MNSandbox::GetGlobalEvent().Emit<int&, long long>("WorldArchiveMgr_getCurWorldPassPortEndTime", iEndTime, desc->worldid);
			int iNowTime = 0;
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->IsRentServerHost())
			{
				iNowTime = GetClientInfoProxy()->getSvrTime();
			}
			else
			{
				iNowTime = GetClientInfoProxy()->getSvrTime();
				MINIW::ScriptVM::game()->callFunction("getServerTime", ">i", &iNowTime);
			}
			if (iEndTime > 0 && iNowTime >= iEndTime) {
				flag = 3;
			}
		}
		pWorldDesc->set_developerflag(flag);
	}
	pWorldDesc->set_fromowid(desc->fromowid);
	pWorldDesc->set_specialtype(desc->_specialType);

	PB_WorldCreateData* pCreateData = pWorldDesc->mutable_createdata();
	pCreateData->set_randseed1(desc->createdata.randseed1);
	pCreateData->set_randseed2(desc->createdata.randseed2);
	pCreateData->set_rolemodel(desc->createdata.rolemodel);
	pCreateData->set_terrtype(desc->createdata.terrtype);
	pCreateData->set_tilesx(desc->createdata.xtiles);
	pCreateData->set_tilesz(desc->createdata.ztiles);
	pCreateData->set_seedstr(desc->createdata.seedstr);
}

void MpGameSurvive::downloadFileProgress(int taskid, int progress) {
	MpGameSuviveLoadHandler* loadHandler = dynamic_cast<MpGameSuviveLoadHandler*>(m_loadHandler);
	if (loadHandler)
	{
		loadHandler->onDownloadGameEvent(taskid, progress);
	}
}

//void MpGameSurvive::onGameEvent(GameEvent *ge)
//{
//	/*if (ge->getype == GE_HTTP_DOWNLOADFILE_PROGRESS)
//	{
//		downloadFileProgress(ge->body.httpprogress.task_id, ge->body.httpprogress.progress);
//	}*/
//}
void MpGameSurvive::mpGameUnload(GAME_RELOAD_TYPE reloadtype)
{
	for (size_t i = 0; i < m_BriefPlayerInfo.size(); i++)
	{
		SANDBOX_DELETE(m_BriefPlayerInfo[i]);
	}
	m_BriefPlayerInfo.clear();
	if (m_PlayerCtrl)
	{
		int myuin = m_PlayerCtrl->getUin();

		if (reloadtype == NO_RELOAD) {
			GetGameNetManagerPtr()->terminateMpGame();
		}
		RoomClient *rc = GetGameNetManagerPtr()->getRoomClient();

		if(myuin > 0 && GetClientInfoProxy()->getMultiPlayer()&GAME_NET_MP_GAME_HOST)
		{
			if(rc && reloadtype == NO_RELOAD) rc->deleteRoom(myuin);
		}
		else if (myuin > 0 && reloadtype != MULTI_RELOAD)
		{
			if (rc) rc->leaveRoom(myuin, GetClientInfoProxy()->getCurRoomOwner());
			GetClientInfoProxy()->setCurRoomOwner(0);
		}
	}
}

void MpGameSurvive::unload(GAME_RELOAD_TYPE reloadtype /* = NO_RELOAD */)
{
	LOG_INFO("MpGameSurvive::unload");
	if (reloadtype != MULTI_RELOAD)
	{
#ifdef IWORLD_SERVER_BUILD
		if (g_zmqMgr && g_zmqMgr->GetPushServerAddr() != NULL)
		{
			auto cliInfo = GetClientInfoProxy();
			const char* s_uin = cliInfo->getEnterParam("account");
			const char* roomid = cliInfo->getEnterParam("room_id");
			char param[512];
			sprintf(param, "&joinable=-1&roomid=%s_%s", s_uin, roomid);
			// std::string url = "http://***********:19610/minilogic/sendfriend?cmd=roomclient.roomlocked" + param;
			std::string url = g_zmqMgr->GetPushServerAddr();
			url = url + "minilogic/sendfriend?cmd=roomclient.roomlocked" + param;
			Rainbow::Http::GetHttpManager().Request(url, "", nullptr);
			LOG_INFO("reportroomjoinable url = '%s'", url.c_str());
		}
#else
		RoomClient* rc = GameNetManager::getInstance()->getRoomClient();
		if (GetClientInfoProxy()->getIsOverseasVer() && m_PlayerCtrl && RoomClient::isValidRoomClient(rc) && rc->isHost()) // 针对线上崩溃增加有效性判断 code_by:huangfubin 2022.10.24
		{
			int myuin = m_PlayerCtrl->getUin();
			MINIW::ScriptVM::game()->callFunction("__routefriends__", "ssii", "roomclient.roomlocked", "", -1, myuin);
		}
#endif
	}

	mpGameUnload(reloadtype);

	SurviveGame::unload(reloadtype);
}

void MpGameSurvive::kickoff(int uin)
{
	LOG_INFO("master kick player :MpGameSurvive::kickoff %d", uin);
	m_netHandler->kickoff(uin);
}

void MpGameSurvive::kickoffWithErrorCode(int uin, int errcode)
{
	LOG_INFO("master kick player :MpGameSurvive::kickoff %d: errcode:%d", uin, errcode);
	//m_netHandler->kickoffWithErrorCode(uin, errcode);
	if (GetGameNetManagerPtr() && GetGameNetManagerPtr()->getConnection())
		GetGameNetManagerPtr()->getConnection()->kickoffMember(uin, errcode);
}

int MpGameSurvive::getMaxPlayerNum()
{
	if (GetGameNetManagerPtr()) 
		return GetGameNetManagerPtr()->getMaxPlayerNum();

	return 0;
}
bool MpGameSurvive::setMaxPlayerNum(int num)
{
	if (GameNetManager::getInstance())
	{
		return GameNetManager::getInstance()->setMaxPlayerNum(num);
	}

	return false;
}

int MpGameSurvive::getCurGameMaxPlayerSetLimit()
{
	if (GameNetManager::getInstance())
	{
		return GameNetManager::getInstance()->getCurGameMaxPlayerSetLimit();
	}
	return 0;
}

int MpGameSurvive::getRoomConnectMode()
{
	if (GameNetManager::getInstance())
	{
		return GameNetManager::getInstance()->getRoomConnectMode();
	}
	return 0;
}

bool MpGameSurvive::setHostPassword(const char* password)
{
#ifdef IWORLD_SERVER_BUILD
	if (GameNetManager::getInstance())
	{
		GameNetManager::getInstance()->setRentPassword(password);
		return true;
	}
#else
	if (GameNetManager::getInstance() && GameNetManager::getInstance()->isHost())
	{
		GameNetManager::getInstance()->setHostPassword(password);
		return true;
	}
#endif
	return false;
}

const char* MpGameSurvive::getHostPassword()
{
#ifdef DEDICATED_SERVER
	if (GameNetManager::getInstance())
	{
		return GameNetManager::getInstance()->getHostPassword();
	}
#else
	if (GameNetManager::getInstance() && GameNetManager::getInstance()->isHost())
	{
		return GameNetManager::getInstance()->getHostPassword();
	}
#endif
	return "";
}

void MpGameSurvive::hostUpdateRoomInfoToServer()
{
#ifndef DEDICATED_SERVER
	if (GameNetManager::getInstance() && GameNetManager::getInstance()->isHost())
	{
		RoomClient* rc = GameNetManager::getInstance()->getRoomClient();
		if (rc)
		{
			rc->updateRoomFlags(GetClientInfoProxy()->getUin());
		}
	}
#endif
}

bool MpGameSurvive::setPublicType(int publicType)
{
	if (GameNetManager::getInstance())
	{
		GameNetManager::getInstance()->setPublicType(publicType);
		return true;
	}
	return false;
}

int MpGameSurvive::getPublicType()
{
	if (GameNetManager::getInstance())
	{
		return GameNetManager::getInstance()->getPublicType();
	}

	return 0;
}

bool MpGameSurvive::setCanTrace(int canTrace)
{
	if (GameNetManager::getInstance())
	{
		GameNetManager::getInstance()->setCanTrace(canTrace);
		return true;
	}
	return false;
}

int MpGameSurvive::getCanTrace()
{
	if (GameNetManager::getInstance())
	{
		return GameNetManager::getInstance()->getCanTrace();
	}

	return 0;
}

void MpGameSurvive::applyPermits(int uin)
{
	m_netHandler->applyPermits(uin);
}

void MpGameSurvive::replyApplyPermits(int uin, int ret)
{
	m_netHandler->replyApplyPermits(uin, ret);
}

void MpGameSurvive::tick()
{
	OPTICK_EVENT();
	if (m_WorldMgr)
	{
		m_WorldMgr->tick();
	}
	updateNearPlayerInfo();

	m_uiHandler->tick();
	m_netHandler->tick();
	checkNickName();
	if (m_WorldMgr && !m_WorldMgr->isRemote())
	{
		int now_ = time(0);
		if (m_lastReportOnlineTime <= 0)
			m_lastReportOnlineTime = now_;

		if (now_ - m_lastReportOnlineTime >= m_ReportOnlineTime)
		{
			m_lastReportOnlineTime = now_;
			reportOnlinePlayer(now_);
		}
	}
}

void MpGameSurvive::endGame(bool isreload/* =false */)
{
	GetClientGameJankReport().Stop();
	GlobalCallbacks::Get().endFrameTick.Unregister(&ClientGameJankReport::OnTick);

	GetClientGameStatistics().Stop();
	GlobalCallbacks::Get().endFrameTick.Unregister(&ClientGameStatistics::OnTick);

	MNSandbox::GetGlobalEvent().Emit<>("StatisticRainforest_PlayerExitGame");
	MNSandbox::GetGlobalEvent().Emit<>("StatisticTerrgen_PlayerExitGame");
	// 存档数据保存-需在触发器释放前保存云变量
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_saveAllPlayerArchDataByLeave");

	if (m_WorldMgr && !m_WorldMgr->isRemote())
	{
		int now_ = time(0);
		m_lastReportOnlineTime = now_;
		reportOnlinePlayer(now_);
	}

	pauseGame();

	if(m_PlayerCtrl && m_PlayerCtrl->getWorld())
	{
		m_PlayerCtrl->setIsStarStationTeleporting(false);
		m_PlayerCtrl->getWorld()->getActorMgr()->ToCastMgr()->despawnActor(m_PlayerCtrl);
	}

	if (!isreload)
	{
		GetGameNetManagerPtr()->terminateMpGame();
	}
}

void MpGameSurvive::sendChat(const char *content, int type/* =0 */, int targetuin /* = 0 */, int language/* =1 */, const char* extend/*=""*/)
{
	m_netHandler->sendChat(content, type, targetuin, language, extend);
}


int MpGameSurvive::getCurOpenContanierIndex()
{
	if (m_CurOpenContainer)
		return m_CurOpenContainer->getBaseIndex();

	return -1;
}

void MpGameSurvive::summonAccountHorse(int uin, int horseid, bool isshapeshift)
{
	ClientPlayer *player = uin2Player(uin);
	if (player)
	{
		if (isshapeshift)
		{
			if (player->isDead())
				return;

			ClientActor *actor = player->summonShapeShiftHorse(horseid);

			if (actor)
				player->mountActor(actor);
		}
		else
			player->summonAccountHorse(horseid);
	}
}

PlayerBriefInfo *MpGameSurvive::getPlayerBriefInfo(int i)
{	
	if(m_PlayerCtrl && i == -1)
	{
		myBriefInfo.teamid = m_PlayerCtrl->getTeam();
		myBriefInfo.cgamevar[0] = m_PlayerCtrl->getGameScore();
		myBriefInfo.cgamevar[1] = m_PlayerCtrl->getGameResults();
		myBriefInfo.skinid = GetClientInfoProxy()->getRoleSkinModel();
		myBriefInfo.model = GetClientInfoProxy()->getRoleModel();
		myBriefInfo.uin = GetClientInfoProxy()->getUin();
		MyStringCpy(myBriefInfo.customjson, sizeof(myBriefInfo.customjson), GetClientInfoProxy()->getAccountInfo()->RoleInfo.CustomSkin);
		MyStringCpy(myBriefInfo.nickname, sizeof(myBriefInfo.nickname), GetClientInfoProxy()->getNickName());
		myBriefInfo.inSpectator = (int)m_PlayerCtrl->isInSpectatorMode();

		return &myBriefInfo;
	}
	else if(i < (int)m_BriefPlayerInfo.size() && i >= 0)
	{
		PlayerBriefInfo * info = m_BriefPlayerInfo[i];
		if (info && strlen(info->nickname) == 0) // lua 获取PlayerBriefInfo对象里面昵称为空不显示问题
		{
			ClientPlayer * player = getPlayerByUin(info->uin);
			if (player)
			{
				MyStringCpy(info->nickname, sizeof(info->nickname), player->getNickname());
			}
		}
		return info;
	}

	return NULL;
}

ClientPlayer *MpGameSurvive::getPlayerByUin(int uin)
{
	return uin2Player(uin);
}

bool MpGameSurvive::RentChangePlayerTeam(int uin, int teamid)
{
	//当前是租赁服房间 向服务器发送改变玩家队伍的消息
	//if (GetGameInfoProxy()->GetRoomHostType() == ROOM_SERVER_RENT)
	{
		PB_CSChangePlayerTeamCH	   csChangePlayerTeamCH;
		csChangePlayerTeamCH.set_uin(uin);
		csChangePlayerTeamCH.set_teamid(teamid);

		GetGameNetManagerPtr()->sendToHost(PB_CLOUDSERVER_CHANGE_TEAM_CH, csChangePlayerTeamCH);
		return true;
	}
	//return false;
}

bool MpGameSurvive::changePlayerTeam(int uin, int teamid, bool bResetAttr)
{
	if (!GetWorldManagerPtr() || !GetWorldManagerPtr()->getBaseSettingManager())
		return false;
	if (teamid != 999 && !GetWorldManagerPtr()->getBaseSettingManager()->getTeamEnable(teamid))
		return false;

	ClientPlayer *player = getPlayerByUin(uin);
	if (player)
	{
		if(player->getTeam() == JUDGE_TEAM_ID && teamid != JUDGE_TEAM_ID)
		{
			if (GetGameNetManagerPtr()->getConnection()->isMemberFull())
			{
				return false;
			}
			m_bHaveJudge = false;
			player->setTeam(teamid);

			player->ClientPlayer::setSpectatorMode(SPECTATOR_MODE_NONE);
			PB_SetSpectatorModeHC setSpectatorModeHC;
			setSpectatorModeHC.set_uin(player->getUin());
			setSpectatorModeHC.set_spectatormode(SPECTATOR_MODE_NONE);

			GetGameNetManagerPtr()->sendBroadCast(PB_SET_SPECTATORMODE_HC, setSpectatorModeHC);

			if (player == g_pPlayerCtrl)
			MINIW::ScriptVM::game()->callFunction("SpectatorModeChange", "");

			GameMode *gmaker = static_cast<GameMode*>(m_WorldMgr->m_RuleMgr);
			int countplayer = getPlayerCountWithoutJudge();
			if(gmaker->getGameStage() == CGAME_STAGE_PREPARE && gmaker->getRuleOptionVal(GMRULE_STARTPLAYERS) <= countplayer)
			{
#ifdef DEDICATED_SERVER
				if(gmaker->getRuleOptionVal(GMRULE_STARTMODE) == 1) //自动开启
				{
					gmaker->setCustomGameStage(CGAME_STAGE_COUNTDOWN);
				}
				else if (gmaker->getRuleOptionVal(GMRULE_STARTMODE) == 2)
				{
					gmaker->onGameStart();
				}
				else //房主开启
				{
					if (ROOM_SERVER_OFFICIAL == GetGameInfoProxy()->GetRoomHostType())
						GetGameEventQue().postSimpleEvent("GIE_WAITHOST_STARTGAME");
				}
#endif
				RoomClient* rc = GetGameNetManagerPtr()->getRoomClient();
				if(rc)
				{
					rc->updateRoomFlags(GetClientInfoProxy()->getUin(), gmaker->getGameStage());
				}
			}

			MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", ""); //裁判不算在玩家人数里
			return true;
		}
	    if(teamid == JUDGE_TEAM_ID)
		{
			/*int num = getNumPlayerBriefInfo();
			for(int i = 0; i<num; i++)
			{
				PlayerBriefInfo *info = getPlayerBriefInfo(i);
				if(info && info->teamid == JUDGE_TEAM_ID)
				{
					return false;
				}
			}*/
			if (m_bHaveJudge)
				return false;
			player->ClientPlayer::setSpectatorMode(SPECTATOR_MODE_JUDGE);

			PB_SetSpectatorModeHC setSpectatorModeHC;
			setSpectatorModeHC.set_uin(player->getUin());
			setSpectatorModeHC.set_spectatormode(SPECTATOR_MODE_JUDGE);

			GetGameNetManagerPtr()->sendBroadCast(PB_SET_SPECTATORMODE_HC, setSpectatorModeHC);

			if (player == g_pPlayerCtrl)
			{
				MINIW::ScriptVM::game()->callFunction("SpectatorModeChange", "");
				player->setOperate(0); //取消挖掘状态
			}
			m_bHaveJudge = true;
			player->setTeam(teamid);
			//判断人数满的条件已经改变
			GameMode *gmaker = static_cast<GameMode*>(m_WorldMgr->m_RuleMgr);
			int countplayer = m_BriefPlayerInfo.size();
			if(gmaker->getGameStage() == CGAME_STAGE_PREPARE && gmaker->getRuleOptionVal(GMRULE_STARTPLAYERS) > countplayer )
			{
				GetGameEventQue().postSimpleEvent("GIE_CLOSE_HOST_STARTGAME");
				RoomClient* rc = GetGameNetManagerPtr()->getRoomClient();
				if(rc)
				{
					rc->updateRoomFlags(GetClientInfoProxy()->getUin(), gmaker->getGameStage());
				}
			}

			MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", ""); //裁判不算在玩家人数里
			return true;
		}


		if (player->getTeam() != teamid)
		{
			player->setTeam(teamid, bResetAttr);
			if (player == g_pPlayerCtrl)
			{
				MINIW::ScriptVM::game()->callFunction("OnChangeTeam", ""); // 刷新界面
			}
			m_WorldMgr->syncBriefInfo(BIS_TEAM_ID, 0, uin);
			return true;
		}
	}
	return false;
}

void MpGameSurvive::hostStartGame()
{
	if (m_PlayerCtrl && m_PlayerCtrl->isCloudRoomServerOwner())
	{
		PB_CloudRoomOwnerStartGameCH startGameCH;

		GetGameNetManagerPtr()->sendToHost(PB_CLOUD_ROOM_OWNER_START_GAME_CH, startGameCH);
	}
	else
	{
		if (m_WorldMgr && m_WorldMgr->isGameMakerRunMode())
		{
			m_WorldMgr->m_RuleMgr->setCustomGameStage(CGAME_STAGE_COUNTDOWN);
		}
	}
}

void MpGameSurvive::clearDownloadList()
{
	MpGameSuviveLoadHandler* loadHandler = dynamic_cast<MpGameSuviveLoadHandler*>(m_loadHandler);
	if (loadHandler)
	{
		loadHandler->clearDownloadList();
	}
}

void MpGameSurvive::downloadIntroInfo(const std::string& url)
{
	MpGameSuviveLoadHandler* loadHandler = dynamic_cast<MpGameSuviveLoadHandler*>(m_loadHandler);
	if (loadHandler)
	{
		loadHandler->downloadIntroInfo(url);
	}
}

/***********************新添加***********************/

void MpGameSurvive::onPlayerLeave(int uin)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL) return;

	if (player->IsOffline())
	{
		return;
	}

#ifdef DEDICATED_SERVER
	Rainbow::GetICloudProxyPtr()->onPlayerLeave(uin);

	jsonxx::Object logger_json;
	PlayerBriefInfo *roleinfo = findPlayerInfoByUin(uin);
	logger_json << "cloud_area" << GetClientInfoProxy()->getGameData("game_env");
	logger_json << "player_end_time" << MINIW::GetTimeStamp();
	if (roleinfo) {
		logger_json << "player_start_time" << roleinfo->enterTime;
		if (roleinfo->game_session_id[0])
			logger_json << "game_session_id" << roleinfo->game_session_id;
	}
	else
	{
		logger_json << "player_start_time" << 0;
		logger_json << "error" << 1;
	}
	logger_json << "map_type" << GetClientInfoProxy()->getEnterParam("maptag");
	if (g_WorldMgr)
	{
		OWORLD* desc = GetIWorldConfigProxy()->findWorldDesc(g_WorldMgr->getWorldId());
		if (desc)
		{
			logger_json << "map_name" << desc->OWName;
		}
	}
	Rainbow::GetICloudProxyPtr()->InfoLog(uin, m_WorldMgr->getWorldId(), "player_leave", logger_json);

	if (g_zmqMgr && player->IsInPvpActivity())
	{
		miniw::pb_game_event event;
		event.set_event_id(miniw::ePBEventID::PB_EVENTID_ACTIVITY_REPORT);
		miniw::pb_pvp_activity_report* report = event.mutable_pvp_report();
		report->set_iactionid(player->GetPvpActivityId());
		report->set_toloadmapid(g_WorldMgr->getFromWorldID());
		player->PackPvpActivityData(report->add_player());

		std::string reqData;
		event.SerializeToString(&reqData);
		g_zmqMgr->PubPvpActivity(g_WorldMgr->getFromWorldID(), reqData.data(), reqData.length());
	}
	
#endif
	if (m_WorldMgr && !m_WorldMgr->isRemote())
	{
		int env = GetIWorldConfigProxy()->getGameData("game_env");
		//std::string url_;
		const char* apiid;
		const char* appkey;

		if (env == 1)
		{
			//url_ = "**************:8080/api/v1/action/exp_map";
			apiid = "1006";
			appkey = "43le2KM#M!u1KlM2l6GagOd0%Ugm4l75";
		}
		else
		{
			apiid = "1006";
			appkey = "j9973tmv8kdhpsqj3nezp9pz45wr275v";
		}
		int now_ = time(0);
		MINIW::ScriptVM::game()->callFunction("reportOnlinePlayer", "u[ClientPlayer]ssi", player, apiid, appkey, now_);
	}
#ifdef DEDICATED_SERVER
	if (g_zmqMgr && GetICloudProxyPtr()->ReportPlayerOffline())
	{
		int t_start = player->getEnterTs();
		int now_ = time(0);

		jsonxx::Object json_;
		json_ << "uin" << uin;
		json_ << "mapid" << g_WorldMgr->getFromWorldID();
		json_ << "play_time" << (now_ - t_start);
		json_ << "enter_time" << t_start;
		std::string logstr = json_.json_nospace();
		g_zmqMgr->PubNsqMsg("playeroffline", g_WorldMgr->getFromWorldID(),
			logstr.c_str(), logstr.size());
	}
#endif
	MINIW::ScriptVM::game()->callFunction("PlayerLeaveGame", "i", uin);

	char content[256];
	sprintf(content, "%s%s", player->getNickname(), GetDefManagerProxy()->getStringDef(3163));
	sendChat(content, 1);
	/*
		对应的功能：任意玩家离开游戏时
		观察者事件接口
	*/
	ObserverEvent_Player obevent(uin);
	GetObserverEventManager().OnTriggerEvent("Game.AnyPlayer.LeaveGame", &obevent);
	//先云变量 后玩家背包和自定义数据 顺序不可乱
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_saveArchDataByLeave", SandboxContext(NULL).SetData_Usertype("player", player));
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("AchievementManager_onPlayerLeaveAch", SandboxContext(NULL).SetData_Number("uin", uin));
	//AchievementManager::GetInstancePtr()->onPlayerLeaveAch(uin); 
	/*if (TaskSubSystem::GetTaskSubSystem())
	{
		TaskSubSystem::GetTaskSubSystem()->OnPlayerLeaveAch(uin);
	}*/
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("uin", uin);
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_OnPlayerLeaveAch", sContext);
	}

	if (m_WorldMgr->isGameMakerRunMode() && m_WorldMgr->m_RuleMgr && m_WorldMgr->m_RuleMgr->getGameStage() == CGAME_STAGE_RUN)
	{
		int teadId = player->getTeam();
		int lifenum = (int)m_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_LIFE_NUM);
		if (lifenum > 0)
		{
			if (teadId > 0 && m_WorldMgr->m_RuleMgr->getTeamPlayers(teadId) == 1)//队伍最后一个退出的时候，此队伍的生命数清零
				m_WorldMgr->m_RuleMgr->setTeamDieTimes(teadId, lifenum);
			else if (teadId == 0 && m_WorldMgr->m_RuleMgr->getTeamPlayers(teadId) == 2) //无队伍的时候，倒数第二个人退出，队伍死亡数999标记游戏结束，剩下的人获得胜利
				m_WorldMgr->m_RuleMgr->setTeamDieTimes(teadId, 999);
		}
	}

	if (player->getCurToolID() == ITEM_GRAVITYGUN)
	{
		auto bindAComponent = player->getBindActorCom();
		if (bindAComponent)
		{
			bindAComponent->unbind();
		}
	}

	auto RidComp = player->getRiddenComponent();
	if (RidComp && RidComp->isRiding()) player->mountActor(NULL, true);
	if (player->isSittingInStarStationCabin())
	{
		player->standUpFromChair();
	}

	StarStationTransferMgr::getSingleton().onPlayerLeave(player);

#ifdef IWORLD_SERVER_BUILD
	SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_getNeedSyncArchive");
	if (sandboxResult.IsExecSuccessed())
	{
		if (sandboxResult.GetData_Bool())
		{
			player->saveToFile(0, m_WorldMgr->m_ChunkIOMgr);
		}
	}
	player->saveUserData(m_WorldMgr->getWorldId());
	player->saveTechTree(m_WorldMgr->getWorldId());
#else
	if (m_WorldMgr->m_ChunkIOMgr && m_WorldMgr->needSave(NEEDSAVE_PLAYERS))
		player->saveToFile(0, m_WorldMgr->m_ChunkIOMgr);
#endif
	ObserverEvent obevent2;
	obevent2.SetData_EventObj(uin);
	GetObserverEventManager().OnTriggerEvent("dev.remove", &obevent2);

	if (player->getWorld()) player->getWorld()->getActorMgr()->ToCastMgr()->despawnActor(player);

	m_WorldMgr->broadcastPlayerLeave(uin);

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_leaveRoom",
		SandboxContext(nullptr).SetData_Number("uin", uin));

	this->checkGameLeader(uin, ROOM_ACTION_LEAVE);
	clearPlayerBriefInfoByUin(uin);
#ifndef IWORLD_SERVER_BUILD   
	MINIW::ScriptVM::game()->callFunction("reportPlayerLeaveWorldNativeCall", "i", uin);
	MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", "i", getNumPlayers());
	MINIW::ScriptVM::game()->callFunction("OnPlayerLeave", "i", uin);
#else
	// 云服同步 触发器/脚本日志 玩家离开房间
	MINIW::ScriptVM::game()->callFunction("SyncServerLogCheckPlayerLeave", "i", uin);
	Rainbow::GetICloudProxyPtr()->packLog(uin, "leave", "");
#endif
	AntiSetting::onPlayerLeave(uin);
}

void MpGameSurvive::terminateMPGame(int errcode)
{
	GameNetManager *gnm = GameNetManager::getInstance();
	if (gnm)
	{
		LOG_INFO("terminateMPGame:%d,%d", gnm->getMyUin(), gnm->getHostUin());

#ifndef IWORLD_SERVER_BUILD
		// 玩家主动退出游戏埋点
		int gamesession_dur = GameAnalytics::getGameSessionDuration();
		GameAnalytics::TrackEvent("player_exit_game", {
			{"uin", GameAnalytics::Value(gnm->getMyUin())},
			{"host_uin", GameAnalytics::Value(gnm->getHostUin())},
			{"error_code", GameAnalytics::Value(errcode)},
			{"room_host_type", GameAnalytics::Value(GetGameInfoProxy()->GetRoomHostType())},
			{"exit_reason", GameAnalytics::Value(errcode == 0 ? "normal" : "error")},
			{"gamesession_duration", GameAnalytics::Value(gamesession_dur)}
		});
#endif

		if (gnm->getRoomClient())
			gnm->getRoomClient()->leaveRoom(gnm->getMyUin(), gnm->getHostUin(), errcode);
		gnm->terminateMpGame();
	}

	MINIW::ScriptVM::game()->callFunction("RSConnectLost", "i", errcode);
}

unsigned int MpGameSurvive::getNetDelayTick()
{
	return m_netHandler->getNetDelayTick();
}

void MpGameSurvive::createNetHandler()
{
	if (!m_netHandler)
	{
		SANDBOX_BIND_LUATYPE(MpGameSurviveNetHandler, "MpGameSurviveNetHandler", "MpGameSurviveNetHandler", "");

		m_netHandler = ENG_NEW_LABEL(MpGameSurviveNetHandler,kMemGame);
		m_netHandler->setRoot(this);
	}	
}

void MpGameSurvive::createLoadHandler()
{
	if (!m_loadHandler)
	{
		m_loadHandler = ENG_NEW_LABEL(MpGameSuviveLoadHandler,kMemGame)(m_IsReload);
		m_loadHandler->setRoot(this);
	}
}

void MpGameSurvive::loadCacheChunkData(const ChunkIOCmd* cmd)
{
	m_netHandler->loadCacheChunkData(*cmd);
}

void MpGameSurvive::saveCacheChunkData(const ChunkIOCmd* cmd)
{
	m_netHandler->saveCacheChunkData(*cmd);
}

void MpGameSurvive::RoleEnterWorld2HostLast(int uin, ClientPlayer *player)
{
    MpGameSurviveNetHandler* handler = dynamic_cast<MpGameSurviveNetHandler*>(m_netHandler);
    if (handler)
    {
        handler->RoleEnterWorld2HostLast(uin, player);
    }
}

int MpGameSurvive::getPlayerCountWithoutJudge()
{
	// 联机m_BriefPlayerInfo不包含主机自己 这里要+1 云服包含所有客机，要减掉1 这里是要获取非观战玩家的数量
	// 裁判的信息也包含在m_BriefPlayerInfo中，减掉
	int judgesize = m_bHaveJudge ? 1 : 0;
	int countplayer = (int)m_BriefPlayerInfo.size() - judgesize + 1;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->IsRentServerHost())
	{
		countplayer = countplayer - 1;
	}
	return countplayer;
}

ClientPlayer *MpGameSurvive::onDataServerLoadPlayer(bool newPlayer, int uin, long long wid, void *data, int len, PlayerBriefInfo* briefInfo)
{
	MPGAME_SURVIVE_LOG("onDataServerLoadPlayer(): uin = %d, newPlayer = %d", uin, newPlayer);
	assert(m_WorldMgr);
	if (m_WorldMgr == NULL)
	{
#ifdef IWORLD_SERVER_BUILD
		Rainbow::GetICloudProxyPtr()->SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurvive::onDataServerLoadPlayer() no worldmgr");
#endif
		return NULL;
	}


	if (ResourceCenter::GetInstancePtr())
		ResourceCenter::GetInstancePtr()->syncClassInfoToClient(uin);

	if (CustomModelMgr::GetInstancePtr())
	{
		//CustomModelMgr::getSingletonPtr()->syncClassInfoToClient(uin);
		CustomModelMgr::GetInstancePtr()->checkSyncCustomModelData(0, 0, false, uin);
	}

	if (FullyCustomModelMgr::GetInstancePtr() && FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr())
		FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr()->syncPackingFcm(uin, 0);

	if (ImportCustomModelMgr::GetInstancePtr())
	{
		ImportCustomModelMgr::GetInstancePtr()->syncImportModel(uin, 0);
	}


	PB_RoleEnterWorldHC roleEnterWorldHC;
	PB_OWGlobal* pOwGlobal = roleEnterWorldHC.mutable_globalinfo();
	m_WorldMgr->saveGlobal(pOwGlobal);

	PlayerBriefInfo *info = findPlayerInfoByUin(uin);
	if (info == NULL)
	{
		if (briefInfo){
			info = addPlayerBriefInfo(uin);
			memcpy((void*)info, (void*)briefInfo, sizeof(PlayerBriefInfo));
		} else {
#ifdef IWORLD_SERVER_BUILD
			Rainbow::GetICloudProxyPtr()->SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurvive::onDataServerLoadPlayer() cant find player brief info");
#endif
			MPGAME_SURVIVE_LOG("cant find player brief info uin = %d", uin);
			return NULL;
		}
	}

	int set_team_id = getPreSetPlayerTeam(uin);
	if (set_team_id > 0)
	{  // 由于客户端默认发送0, 这里认为0是未指定队伍
		if (!m_WorldMgr->m_RuleMgr || !m_WorldMgr->m_RuleMgr->canAddTeam(set_team_id))
		{
			// 无法进入指定的队伍
			SLOG(INFO) << "PresetTeam load uin=" << uin <<" team not joinable teamid=" << set_team_id << "; kick";
			GameNetManager::getInstance()->getConnection()->kickoffMember(uin, ERR_CODE_TEAM_NOT_ENTERABLE);
			set_team_id = 0;
			return NULL;
        }
		else
		{
			SLOG(INFO) << "PresetTeam uin=" << uin <<" team use " << set_team_id;
		}
	}

	ClientPlayer *player = SANDBOX_NEW(ClientPlayer);

	int playerindex = ComposePlayerIndex(info->model, info->geniuslv, info->skinid);
	player->init(uin, info->nickname, playerindex, info->customjson);
	player->m_BPTitle = info->bptitle;	// 悦享赛事称号 20230616 by wuyuwang
	player->getBody()->setPlayerFrameId(info->frameid);
	player->setVipInfo(info->vipinfo);

	PB_ActionAttrStateHC actionAttrStateHC;
	unsigned int permitStates = ENABLE_NEWINITVALUE;
	if (GetWorldManagerPtr() != NULL && GetWorldManagerPtr()->getBaseSettingManager())
	{
		permitStates = GetWorldManagerPtr()->getBaseSettingManager()->getPermitStatesValue();
	}
	actionAttrStateHC.set_attr(permitStates);
	GameNetManager::getInstance()->sendToClient(uin, PB_ACTION_ATTR_STATE_HC, actionAttrStateHC);
	MPGAME_SURVIVE_LOG("send PB_ACTION_ATTR_STATE_HC to client, uin = %d", uin);

	if (set_team_id <= 0)
		set_team_id = m_WorldMgr->getNewPlayerTeamID(player);
	player->setTeam(set_team_id);
	
	PlayerAttrib* pAttrib = player->getPlayerAttrib();
	if (pAttrib) {
		MPGAME_SURVIVE_LOG("initPlayerBaseAttr, uin = %d", uin);
		pAttrib->initPlayerBaseAttr(set_team_id);
	}

	World *pworld = NULL;
	bool prepare = true;
	bool hasrole = false;
	WCoord startPt;
	GameMode* gmaker = static_cast<GameMode*>(m_WorldMgr->getGameMakerManager());
	bool random_offset = false;
	
	if (m_WorldMgr->isGameMakerRunMode())
	{
		MPGAME_SURVIVE_LOG("isGameMakerRunMode, uin = %d", uin);
		//bool prepare = true;
		startPt = m_WorldMgr->getTeamPrePoint(player);
		// if (startPt.y < 0 || gmaker->getGameStage() == CGAME_STAGE_RUN)
		// {
		// 	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->hasInitialPoint(player->getTeam()))
		// 	{
		// 		startPt = GetWorldManagerPtr()->m_RuleMgr->getInitialPoint(player->getTeam()).y >= 0 ? \
		// 			GetWorldManagerPtr()->m_RuleMgr->getInitialPoint(player->getTeam()) : \
		// 			GetWorldManagerPtr()->m_RuleMgr->getInitialPoint(0);
		// 	}
		// 	else
		// 	{
		// 		startPt = m_WorldMgr->getTeamSpawnPoint(player);
		// 	}
		// 	prepare = false;
		// }

		int _mapid = 0;
		SDB_GET_START_MAPID(_mapid, uin)
		pworld = m_WorldMgr->getOrCreateWorld(_mapid, player);

		if (startPt.y < 0 )
			startPt = g_WorldMgr->getSpawnPointEx(pworld);

		gmaker->initPlayerDir(player);

		if (!newPlayer && data && len > 0)
		{
			MPGAME_SURVIVE_LOG("old playerloadFromDataServer, uin = %d", uin);
			player->loadFromDataServer(m_WorldMgr->getWorldId(), uin, data, len, true, m_WorldMgr->getSpecialType());
			startPt = CoordDivBlock(player->getPosition());  // 使用上次下线位置
			hasrole = true;
		}
		else
		{
			startPt = pworld->getRandomRespawnPoint();//获取随机出生点  第一进入的
			if (startPt.y < 0)
				startPt = g_WorldMgr->getSpawnPointEx(pworld);
			MPGAME_SURVIVE_LOG("new player getRandomRespawnPoint, uin = %d, startPt = %s", uin, startPt.__tostring().c_str());
		}
	}
	else
	{
		if (!newPlayer)
		{
			MPGAME_SURVIVE_LOG("old player loadFromDataServer, uin = %d", uin);
			player->loadFromDataServer(m_WorldMgr->getWorldId(), uin, data, len, true, m_WorldMgr->getSpecialType());
			hasrole = true;

			int _mapid = player->getCurMapID();
			SDB_GET_START_MAPID(_mapid, uin)
			pworld = m_WorldMgr->getOrCreateWorld(_mapid, player);
			startPt = CoordDivBlock(player->getPosition());  // 使用上次下线位置
		}
		else
		{
			int _mapid = 0;
			SDB_GET_START_MAPID(_mapid, uin)
			pworld = m_WorldMgr->getOrCreateWorld(_mapid, player);
			startPt = m_WorldMgr->getRevivePointEx(pworld);
			if (startPt.y < 0)
				startPt = m_WorldMgr->getSpawnPoint();

			random_offset = true;  // 要落在y轴有实体方块的最高点
			roleInit(pworld->getOWID(), player);
			MPGAME_SURVIVE_LOG("new player roleInit, uin = %d, startPt = %s", uin, startPt.__tostring().c_str());
		}
	}

	TeleportParamInfo teleportInfo;
	if (GetRoomTeleportInfo(uin, teleportInfo))
	{
		if (teleportInfo.hasPos)
		{
			// 如果位置合法才使用
			WCoord chunkp = BlockDivSection(teleportInfo.pos);
			if (pworld->getChunkProvider() && pworld->getChunkProvider()->canProvideChunk(chunkp.x, chunkp.z))
			{
				startPt = teleportInfo.pos;
			}
		}
	}

	player->gotoBlockPos(pworld, startPt, random_offset);
	player->setNewPlayer(!hasrole);
	roleEnterWorldHC.set_hasrole(hasrole);
	roleEnterWorldHC.set_teleportmsg(teleportInfo.msg);

	pworld->getActorMgr()->ToCastMgr()->spawnPlayerAddRef(player);
	player->release();

	if (m_WorldMgr->isGameMakerRunMode())
	{
		int countplayer = getPlayerCountWithoutJudge();
		if (gmaker->getGameStage() == CGAME_STAGE_PREPARE)
		{
			onPlayerEnterByCloudServer(countplayer);
		}
		MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", "");

	}
	// 观察者事件接口
	ObserverEvent_Player obevent(player->getUin());
	ObserverEventManager::getSingleton().OnTriggerEvent("Player.JoinTeam", &obevent);

	//家园的出生点特殊处理
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_getSpawnBlockPos", SandboxContext(nullptr).SetData_Number("SpawnPosType", 0));
	if (result.IsSuccessed())
	{
		WCoord pt = result.GetData_UserObject<WCoord>("wcoord");
		player->gotoBlockPos(pworld, pt, false);
	}

	info->mapid = pworld->getCurMapID();

	PlayerAttrib& playerAttrib = *player->getPlayerAttrib();

	info->overflowhp = playerAttrib.getOverflowHP();
	info->maxhp = playerAttrib.getMaxHP();
	info->hp = playerAttrib.getHP();

	info->overflowstrength = playerAttrib.getOverflowStrength();
	info->maxstrength = playerAttrib.getMaxStrength();
	info->strength = playerAttrib.getStrength();

	info->x = player->getPosition().x;
	info->y = player->getPosition().y;
	info->z = player->getPosition().z;

	PB_PlayerInfo* playerInfo = roleEnterWorldHC.mutable_playerinfo();
	playerInfo->set_objid(player->getObjId());
	playerInfo->set_anim(player->getBody()->getCurAnim(0));
	playerInfo->set_anim1(player->getBody()->getCurAnim(1));
	playerInfo->set_animweapon(player->getBody()->getCurAnimWeapon());
	playerInfo->set_bodycolor(player->getBody()->getBodyColor());
	playerInfo->set_customscale(player->getCustomScale());
	playerInfo->set_actseqid(player->getBody()->getActSeqID());
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	SANDBOX_ASSERT(player->GetNodeid() != 0);
	playerInfo->set_nodeid(player->GetNodeid());
	// LOG_INFO("[player_nodeid] send player nodeid: %d , objid:%d", player->GetNodeid(), player->getObjId());
#endif//SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	auto sawtooth_ = playerInfo->mutable_sawtooth();
	auto thornComponent = player->getThornBallComponent();
	if (thornComponent != nullptr)
	{
		for (int i = 0; i < thornComponent->getThornAnchorNum(); i++)
		{
			auto info = thornComponent->getThornAnchorAt(i);
			PB_SawtoothInfo* roleinfodata = sawtooth_->Add();
			roleinfodata->set_sawtoothid(info.anchorId);
			PB_Vector3* vec = roleinfodata->mutable_pos();
			vec->set_x(info.pos.x);
			vec->set_y(info.pos.y);
			vec->set_z(info.pos.z);
		}
	}

	PB_SkillCDData* skillCDData = roleEnterWorldHC.mutable_skillcddata();
	player->saveSkillCDCompToPB(skillCDData);


	auto iterItems = player->m_UnlockItems.begin();
	for (; iterItems != player->m_UnlockItems.end(); iterItems++)
	{
		roleEnterWorldHC.add_unlockitems(*iterItems);
	}

	WCoord realPos = player->getPosition();

	PB_RoleData* roleData = playerInfo->mutable_roledata();
	player->changeRoleData(roleData);

	PB_Pos* rolePos = roleData->mutable_pos();
	rolePos->set_x(realPos.x);
	rolePos->set_y(realPos.y);
	rolePos->set_z(realPos.z);

	fillCurWorldDesc(roleEnterWorldHC.mutable_worlddesc());

	// 基础设置，如果有开局介绍，同步给客机相关文件
	const int urllen = 255;
	char url[urllen + 1] = { 0 };
	MINIW::ScriptVM::game()->callFunction("GetCurUploadingIntroUrl", ">s", url);
	url[urllen] = 0;
	if (strlen(url) > 1)
	{
		roleEnterWorldHC.set_url(url);
	}

	MPGAME_SURVIVE_LOG("sendToClient PB_ROLE_ENTER_WORLD_HC uin = %d, wid = %lld, data length = %d", uin, wid, len);
	GameNetManager::getInstance()->sendToClient(uin, PB_ROLE_ENTER_WORLD_HC, roleEnterWorldHC);

	SLOG(INFO) << "sendToClient PB_ROLE_ENTER_WORLD_HC uin =" << uin << ", wid = " << wid << ", data length = " << len;

	//先同步文件信息再执行onplayerinit，不然客机第一次进房间会拿不到补给箱的机械胶囊
	//create replicator for this client
	//auto replicatorRoot = MNSandbox::GetCurrentReplicatorRoot();
	//if (replicatorRoot) {
	//	replicatorRoot->RegisterServerReplicator(uin);
	//}
	
	if (m_WorldMgr && m_WorldMgr->isGameMakerRunMode() && m_WorldMgr->m_RuleMgr)
	{
		m_WorldMgr->m_RuleMgr->onPlayerInit(player, false, prepare);

		//设置队伍数据
		//if (m_WorldMgr->getBaseSettingManager() && m_WorldMgr->getBaseSettingManager()->getTeamEnable(player->getTeam()))
		//{
		//	//初始物品
		//	m_WorldMgr->m_RuleMgr->initPlayerBaseItem(player, player->getShortcutStartIndex(), player->getTeam());
		//	m_WorldMgr->m_RuleMgr->initPlayerBaseItem(player, BACKPACK_START_INDEX, player->getTeam());
		//}
	}
	//设置玩家的出生点
	//player->setSpawnPoint(m_WorldMgr->getSpawnPointEx(pworld), false);
	SandboxContext context;
	context.SetData_UserObject("point", m_WorldMgr->getSpawnPointEx(pworld));
	context.SetData_Bool("bSetWorldMgr", false);
	player->Event().Emit("revive_setSpawnPoint", context);
	

	m_WorldMgr->syncPlayerEnter(uin); 
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_sendPermitMsg",
		SandboxContext(nullptr).SetData_Number("targetuin", uin));

	if (ROOM_SERVER_RENT == GetClientInfoProxy()->getRoomHostType())
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_sendCSAllAuthority", SandboxContext(nullptr).SetData_Number("uin", uin));
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_sendCSClientPermitMsg", SandboxContext(nullptr).SetData_Number("srcuin", uin));
	}

	MPGAME_SURVIVE_LOG("Lua call ReqCurUseAchieveByUin, uin = %d", uin);
	MINIW::ScriptVM::game()->callFunction("ReqCurUseAchieveByUin", "i", uin);

	char buffer[64];
	sprintf(buffer, "%d", uin);
	MPGAME_SURVIVE_LOG("Lua call OnPlayerEnter, uin = %d", uin);
	MINIW::ScriptVM::game()->callFunction("OnPlayerEnter", "s", buffer);

	// 同步已创建的图文信息
	if (GetWorldManagerPtr())
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("GraphicsMgr_snycAllGraphics", SandboxContext(nullptr).SetData_Number("uin", uin));
	}
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ChangeModelMgr_updateAllActor2Client", SandboxContext(nullptr).SetData_Number("uin", uin));

	//开发者在基础设置中设置有自定义模型
	if (m_WorldMgr->isGameMakerRunMode() && m_WorldMgr->getBaseSettingManager())
	{
		GameMode *setterMgr = static_cast<GameMode*>(m_WorldMgr->getBaseSettingManager());
		for (int teamId = -1; teamId < MAX_GAME_TEAMS; ++teamId)
		{
			string modeldata = setterMgr->getPlayerBaseModelData(teamId, true);
			if (!modeldata.empty()) //主机设置有自定义模型
			{
				PB_CustomBaseModelHC baseModelHC;
				baseModelHC.set_uin(player->getUin());
				baseModelHC.set_teamid(teamId);
				baseModelHC.set_modeldata(modeldata.c_str());
				GameNetManager::getInstance()->sendToClient(player->getUin(), PB_PLAYER_CUSTOM_BASEMODEL_HC, baseModelHC);
			}
		}
	}
	MPGAME_SURVIVE_LOG("Lua call ClientPlayerEnterWorld, uin = %d", uin);
	// 调用lua 接口  同步内容生态业务逻辑入口 codeby qinpeng
	MINIW::ScriptVM::game()->callFunction("ClientPlayerEnterWorld", "i", uin);
#ifdef DEDICATED_SERVER
	// 云服同步 触发器/脚本日志 初始化玩家
	MPGAME_SURVIVE_LOG("Lua call SyncServerLogCheckPlayerEnter, uin = %d", uin);
	MINIW::ScriptVM::game()->callFunction("SyncServerLogCheckPlayerEnter", "i", uin);
#endif
	return player;
}

ClientPlayer* MpGameSurvive::onLoadPlayerAllData(miniw::Response* resp, PlayerBriefInfo* briefInfo)
{
	int uin = resp->uin();
	long long wid = strtoll(resp->aid().c_str(), NULL, 10);

	GetICloudProxyPtr()->SimpleSLOG("5.MpGameSurvive::onLoadPlayerAllData %d, datalen=%d", uin, resp->result().length());

	if (GetClientInfoProxy()->isClosing())
	{
		Rainbow::GetICloudProxyPtr()->SimpleErrLog(uin, 0, "role_enter_err", "server closing");
		return nullptr;
	}

	// 心跳包如果没有了，说明已经断开连接，则没有加载的意义，
	auto& heartbeats = GameNetManager::getInstance()->m_HostRecvHeartBeart;
	if (heartbeats.find(uin) == heartbeats.end())
	{
		GetICloudProxyPtr()->SimpleSLOG("5.MpGameSurvive::onLoadPlayerAllData faild, cant find heartbeat %d", uin);
		Rainbow::GetICloudProxyPtr()->SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurvive::onLoadPlayerAllData() uin not in heartbeat list");
		return nullptr;
	}

	bool newPlayer = false;
	ClientPlayer* player = NULL;

	miniw::player_data pd;
	if (!pd.ParseFromArray(resp->result().c_str(), resp->result().length()))
	{
		PROTO_PARSE_ERR(uin, "onLoadPlayerAllData");
		GetICloudProxyPtr()->SimpleSLOG("5.MpGameSurvive::onLoadPlayerAllData faild, proto parse err %d", uin);
		Rainbow::GetICloudProxyPtr()->SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurvive::onLoadPlayerAllData() player_data ParseFromArray failed");		
		newPlayer = true;
	}
#ifdef IWORLD_SERVER_BUILD
	// 检测是否允许登录
	if (AntiSetting::gCheatConfig.IsCheckMultiLogin() &&
		pd.has_online_roomid() && pd.online_roomid().size() > 0)
	{
		auto gp = GetClientInfoProxy();
		std::ostringstream os;
		os << gp->getEnterParam("room_id") << ":" << gp->getEnterParam("ip");
		if (pd.online_roomid() != os.str() && !g_zmqMgr->CheckPlayerInCurRoom(gp->getEnterParam("room_id"), pd.online_roomid().c_str()))
		{
			jsonxx::Object log;
			log << "last_room_id" << pd.online_roomid();
			log << "cur_room_id" << os.str();
			Rainbow::GetICloudProxyPtr()->InfoLog(uin, 0, "role_multi_login", log);

			GetICloudProxyPtr()->SimpleSLOG("5.MpGameSurvive::onLoadPlayerAllData faild, multi login kick %d", uin);
			GameNetManager::getInstance()->getConnection()->kickoffMember(uin, ERR_CODE_CLIENT_LOGIN_MULTI);
			return nullptr;
		}
	}
#endif
	std::ostringstream log_info;
	if (pd.has_permits())
	{
		PermitsSubSystem* permitSystem = PluginManager::GetInstancePtr()->FindSubsystem<PermitsSubSystem>();
		if (permitSystem)
		{
			permitSystem->loadCSPlayerPermits(uin, pd.permits().c_str(), pd.permits().length());
		}

		// SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("PermitsSubSystem_loadCSPlayerPermits", SandboxContext(nullptr)
		// 	.SetData_String("buf", pd.permits().c_str())
		// 	.SetData_Number("buf_len", (int)pd.permits().length())
		// 	.SetData_Number("uin", uin)
		// );
	}
	// 地图设置了存储玩家数据, 则加载数据
	bool bTrySave = false;	
	SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_getNeedSyncArchive");
	if (sandboxResult.IsExecSuccessed())
	{
		bTrySave = sandboxResult.GetData_Bool();
	}
	if (bTrySave)
	{
		log_info << "[need save]";
		log_info << "[main data len=" << pd.data().length() << "]";
		if (pd.data().length() == 0)
		{
			newPlayer = true;
		}
		if (pd.has_tasksys() && pd.tasksys().length() > 0)
		{
			log_info << "[tasksys len=" << pd.tasksys().length() << "]";
			/*if (TaskSubSystem::GetTaskSubSystem())
			{
				TaskSubSystem::GetTaskSubSystem()->OnLoadRoleWorldTask(uin, wid, (void*)pd.tasksys().c_str(), (int)pd.tasksys().length());
			}*/
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("uin", uin).
					SetData_Number("wid", wid).
					SetData_Userdata("data", (void*)pd.tasksys().c_str()).
					SetData_Number("len", (int)pd.tasksys().length());
				MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_OnLoadRoleWorldTask", sContext);
			}
		}
		else
			MNSandbox::GetGlobalEvent().Emit<int, long long, const std::string&>("AchievementManager_onLoadRoleWorldAchievements", uin, wid, pd.missions());


		void *data = (void*)pd.data().c_str();
		int len = (int)pd.data().length();
		// 云存档尝试读地图包里面得玩家数据
		if (newPlayer && GetClientInfoProxy()->isRentServerMode())
		{
			//TaskSubSystem::GetTaskSubSystem()->AddNeedSave(uin);	
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("uin", uin);
				MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_AddNeedSave", sContext);
			}
			std::string rootpath = GetWorldRootBySpecialType(m_WorldMgr->getSpecialType());

			char path[256];
			sprintf(path, "%s/w%lld/roles/u%d.p", rootpath.c_str(), m_WorldMgr->getWorldId(), uin);
			int   buflen = 0;
			void* buf    = ReadWholeFile(path, buflen);
			if (buf)
			{
				newPlayer = false;
				data = buf;
				len = buflen;
			}
		}
		player = this->onDataServerLoadPlayer(newPlayer, uin, wid, data, len, briefInfo);

		if (pd.has_cloud_lib_var()) 
		{
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_onLoadRoleCloudLibVar",SandboxContext(nullptr)
				.SetData_String("cloud_lib_var", pd.cloud_lib_var())
				.SetData_Number("wid", wid)
				.SetData_Number("uin", uin));
			log_info << "[cloud_lib_var len=" << pd.cloud_lib_var().length() << "]";
		}
	}
	else
	{
		// 地图未设置了存储玩家数据, 创建全新角色
		player = this->onDataServerLoadPlayer(true, uin, wid, NULL, 0, briefInfo);
		if (pd.has_cloud_lib_var())
		{
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_onLoadRoleCloudLibVar", SandboxContext(nullptr)
				.SetData_String("cloud_lib_var", pd.cloud_lib_var())
				.SetData_Number("wid", wid)
				.SetData_Number("uin", uin));
		}
	}
	if (player && pd.has_custom_data())
	{
		player->onLoadUserData(pd.custom_data().c_str(), (int)pd.custom_data().length());
	}
	// 加载科技数工作台
	if (player && pd.has_tech_workbench()) {
		player->onLoadTechTree(pd.custom_data().c_str(), (int)pd.custom_data().length());
	}

	GetICloudProxyPtr()->SimpleSLOG("5.MpGameSurvive::onLoadPlayerAllData load player data %d new:%d info: %s", uin, newPlayer, log_info.str().c_str());
	
	if (player)
	{
		RoleEnterWorld2HostLast(uin, player);
	}
	else
	{
		GetICloudProxyPtr()->SimpleSLOG("5.MpGameSurvive::onLoadPlayerAllData faild, create player err %d", uin);
		Rainbow::GetICloudProxyPtr()->SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurvive::onLoadPlayerAllData() player create error");
	}
	return player;
}

void MpGameSurvive::setGameLeaderUin(int uin)
{
    if (this->m_WorldMgr == nullptr) {
        return;
    }
	this->m_WorldMgr->setGameLeaderUin(uin);
}

int MpGameSurvive::getGameLeaderUin()
{
    if (this->m_WorldMgr == nullptr) {
        return 0;
    }
	return this->m_WorldMgr->getGameLeaderUin();
}

//2021115 挑选新的伪房主 codeby:liushuxin
int MpGameSurvive::selectNewGameLeader(ROOM_SELECT_LEADER selectType)
{
	if (selectType == ROOM_SELECT_LEADER_DEFAULT) 
	{
		return 0;
	}

	int uin = 0;
	if (selectType == ROOM_SELECT_LEADER_ORGANIZER)
	{
		uin = m_organizerUin;
	}
	else {
		std::vector<IClientPlayer*> players;
		m_WorldMgr->getAllPlayers(players);

		if (players.size() > 0) {
			uin = players[0]->getUin();
		}
	}
	
	m_WorldMgr->setGameLeaderUin(uin);
	if (uin == m_organizerUin) 
	{
		m_organizerFirst = false;
	}
	return uin;
}

//2021112 更新房主 codeby:liushuxin
void MpGameSurvive::checkGameLeader(int playerUin, ROOM_ACTION roomAction)
{
#ifdef IWORLD_SERVER_BUILD
	if (GetClientInfoProxy()->isRentServerMode())
	{
		return;
	}

	if (roomAction == ROOM_ACTION_INIT)
	{
		return;
	}

	int curLeaderUin = m_WorldMgr->getGameLeaderUin();
	//非房主离开
	if (roomAction == ROOM_ACTION_LEAVE && playerUin != curLeaderUin)
	{
		return;
	}

	if (GetClientInfoProxy()->isRentType(1))
	{
		if (roomAction == ROOM_ACTION_ENTER)
		{
			PB_GameLeaderSwitchHC gameLeaderSwitchHC;
			gameLeaderSwitchHC.set_uin(curLeaderUin);
			GameNetManager::getInstance()->sendToClient(playerUin, PB_GAME_LEADER_SWITCH_HC, gameLeaderSwitchHC);
		}
		return;
	}

	ClientPlayer* curLeaderPlayer = uin2Player(curLeaderUin);
	bool leaderExist = curLeaderPlayer != nullptr;

	ROOM_SELECT_LEADER selectType = getSelectLeaderType(playerUin, roomAction);
	//重新选房主
	if (selectType != ROOM_SELECT_LEADER_DEFAULT) 
	{
		PB_GameLeaderSwitchHC gameLeaderSwitchHC;
		int uin = this->selectNewGameLeader(selectType);
		gameLeaderSwitchHC.set_uin(uin);
		GameNetManager::getInstance()->sendBroadCast(PB_GAME_LEADER_SWITCH_HC, gameLeaderSwitchHC);
		return;
	}

	//新进玩家时，房主已经存在
	if (roomAction == ROOM_ACTION_ENTER && leaderExist)
	{
		PB_GameLeaderSwitchHC gameLeaderSwitchHC;
		gameLeaderSwitchHC.set_uin(curLeaderUin);
		GameNetManager::getInstance()->sendToClient(playerUin, PB_GAME_LEADER_SWITCH_HC, gameLeaderSwitchHC);
	}
#endif
}


//20211215 需要重新选房主情况 codeby:liushuxin
ROOM_SELECT_LEADER MpGameSurvive::getSelectLeaderType(int playerUin, ROOM_ACTION roomAction) {
	int curLeaderUin = m_WorldMgr->getGameLeaderUin();
	ClientPlayer* curLeaderPlayer = uin2Player(curLeaderUin);
	bool leaderExist = curLeaderPlayer != nullptr;

	//玩家加入地图当前没有房主
	if (roomAction == ROOM_ACTION_ENTER && !leaderExist) {
		return ROOM_SELECT_LEADER_NEW;
	}
	//玩家离开地图且其为房主
	if (roomAction == ROOM_ACTION_LEAVE && playerUin == curLeaderUin) {
		return ROOM_SELECT_LEADER_LEAVE;
	}
	//玩家进入房间时，其为发起者，其没当过房主
	if (roomAction == ROOM_ACTION_ENTER && playerUin != curLeaderUin && m_organizerFirst && playerUin == m_organizerUin && !leaderExist)
	{
		return ROOM_SELECT_LEADER_ORGANIZER;
	}

	return ROOM_SELECT_LEADER_DEFAULT;
}

void MpGameSurvive::onPlayerEnterByCloudServer(int playerCount)
{
	//流程图, 仅可参考不照搬https://mini1.feishu.cn/docs/doccnfeV9QqTZUpjZuMulhqbdte
	GameMode *gmaker = static_cast<GameMode*>(m_WorldMgr->m_RuleMgr);
	if (gmaker == nullptr)
	{
		LOG_INFO("get GameMode null.");
		return;
	}

	int configStartPlayer = gmaker->getRuleOptionVal(GMRULE_STARTPLAYERS);

	//开启人数 > 1
	if (configStartPlayer > 1)
	{
		switch (int(gmaker->getRuleOptionVal(GMRULE_STARTMODE)))
		{
		case 0: //房主开启
		{
			if (playerCount >= configStartPlayer)
			{//达到开启需求人数, 倒计时321开始游戏
				GetGameEventQue().postSimpleEvent("GIE_WAITHOST_STARTGAME");
			}
		}
		break;
		case 1://人数开启
		{
			if (playerCount >= configStartPlayer)
			{//达到开启需求人数, 开始游戏
				gmaker->setCustomGameStage(CGAME_STAGE_COUNTDOWN);
			}
		}
		break;
		case 2: //不限条件开启
		{
			//开始游戏
			gmaker->onGameStart();
			//中途加入游戏开关强制为开启
			gmaker->setGameRule(GMRULE_ALLOW_MIDWAYJOIN, 1, 1);
		}
		break;
		}
	}
	else //开启人数<= 1
	{
		if (playerCount >= configStartPlayer)
		{
			//中途加入游戏开关强制为开启
			gmaker->setGameRule(GMRULE_ALLOW_MIDWAYJOIN, 1, 1);
			//达到开启需求人数, 开始游戏
			gmaker->onGameStart();
		}
	}
}
/* todo huangfubin
bool MpGameSurvive::isPlayerEmpty() {
	return m_WorldMgr->getAllPlayersNum() == 0;
}*/
// 2022-01-20 codeby:liusijia 设置跨房间传送位置
// 格式为：{msgtype=2,memebers={...},pos={x=1,y=1,z=1}}
void MpGameSurvive::SetRoomTeleportInfo(const jsonxx::Object& content)
{
	if (!content.has<jsonxx::Array>("members"))
		return;

	const jsonxx::Array& members = content.get<jsonxx::Array>("members");
	for (size_t i = 0; i < members.size(); ++i)
	{
		int uin = members.get<jsonxx::Number>(i);
		TeleportParamInfo info;
		if (content.has<jsonxx::Object>("pos"))
		{
			const jsonxx::Object& position = content.get<jsonxx::Object>("pos");

			// 没有位置数据，跳过
			if (position.has<jsonxx::Number>("x") &&
				position.has<jsonxx::Number>("y") &&
				position.has<jsonxx::Number>("z"))
			{
				float x = position.get<jsonxx::Number>("x");
				float y = position.get<jsonxx::Number>("y");
				float z = position.get<jsonxx::Number>("z");
				info.hasPos = true;
				info.pos = WCoord(x, y, z);
			}
		}

		if (content.has<jsonxx::String>("teleportmsg"))
		{
			string msg = content.get<jsonxx::String>("teleportmsg");
			int len = 0;
			unsigned char szDecodedBuff[4096] = { 0 };
			if (base64_decode((unsigned char*)msg.c_str(), false, szDecodedBuff, &len))
			{
				info.msg = Rainbow::Http::CurlUrlDecode(std::string((char*)szDecodedBuff, len));
			}
		}

		m_RoomTeleportInfo[uin] = info;
	}
}

bool MpGameSurvive::GetRoomTeleportInfo(int uin, TeleportParamInfo& info)
{
	auto itr = m_RoomTeleportInfo.find(uin);
	if (itr != m_RoomTeleportInfo.end())
	{
		info = itr->second;
		m_RoomTeleportInfo.erase(itr);
		LOG_INFO("m_RoomTeleportInfo uin: %d  pos:%d,%d,%d, msg:%s", 
			uin, info.pos.x, info.pos.y, info.pos.z, info.msg.c_str());
		return true;
	}
	return false;
}
/************************
 * 云服处理启动时或运行时收到视频播放通知 2022.02.23 by huanglin
 * 为放映厅需求新增功能, 允许在房间运行过程中控制播放的开关/切换视频
 * 可以考虑换个名字改成通用功能而非专用放映功能
 * @param content 透传给lua
 */
void MpGameSurvive::onRecvMsgBusTriggerInfo(const string &content)
{
	LOG_INFO("onRecvMsgBusVideoInfo %s", content.c_str());
	MINIW::ScriptVM::game()->callFunction("OnCloudMsgTriggerEvent", "s", content.c_str());
}

void MpGameSurvive::addCheckNickName(int uin, const std::string& nickName)
{
	m_WaitCheckNickName[uin] = nickName;
}

void MpGameSurvive::checkNickName()
{
	OPTICK_EVENT();
	unsigned int now = Timer::getSystemTick();
	if (now - m_LastCheckTime > 1000)
	{
		m_LastCheckTime = now;

		if (m_WaitCheckNickName.empty())
			return;

		jsonxx::Object checkInfo;
		for (auto& pair : m_WaitCheckNickName)
		{
			std::ostringstream os;
			os << pair.first;
			checkInfo << os.str() << pair.second;
		}
		std::string r = checkInfo.json_nospace();
		MINIW::ScriptVM::game()->callFunction("CheckPlayerNickName", "s", r.c_str());

		m_WaitCheckNickName.clear();
	}
}
void MpGameSurvive::broadCorrectNickName(const std::string& ret)
{
	jsonxx::Array nicknames;
	if (nicknames.parse(ret) && nicknames.size() > 0)
	{
		PB_PlayersUpdateInfoHC playersUpdateInfoHC;
		for (int i = 0; i < nicknames.size(); ++i)
		{
			jsonxx::Array& subVal = nicknames.get<jsonxx::Array>(i);
			if (subVal.size() != 2)
				continue;
			int uin = subVal.get<jsonxx::Number>(0);
			const std::string& name = subVal.get<jsonxx::String>(1);

			IClientPlayer* iplayer = g_WorldMgr->getPlayerByUin(uin);
			if (!iplayer)
				continue;
			ClientPlayer* player = iplayer->GetPlayer();
#ifdef IWORLD_SERVER_BUILD
			jsonxx::Object cheat;
			cheat << "client_use_name" << player->getNickname();
			cheat << "server_get_name" << name;
			Rainbow::GetICloudProxyPtr()->InfoLog(uin, player->getOWID(), "cheat_nick_name", cheat);
#endif
			PlayerBriefInfo* playerBrief = findPlayerInfoByUin(uin);
			if (playerBrief)
			{
				MyStringCpy(playerBrief->nickname, sizeof(playerBrief->nickname), name.c_str());
				player->setNickName(playerBrief->nickname);
			}		

			PB_PlayerBriefInfo* briefInfo = playersUpdateInfoHC.add_players();
			briefInfo->set_uin(uin);
			briefInfo->set_nickname(name.c_str());
		}
		GameNetManager::getInstance()->sendBroadCast(PB_PLAYERS_UPDATEINFO_HC, playersUpdateInfoHC, 0, false);
	}
}


void MpGameSurvive::reportOnlinePlayer(int now)
{
	OPTICK_EVENT();
	int env = GetIWorldConfigProxy()->getGameData("game_env");
	//  std::string url_;
	const char* apiid;
	const char* appkey;

	if (env == 1)
	{
		//      url_ = "**************:8080/api/v1/action/exp_map";
		apiid = "1006";
		appkey = "43le2KM#M!u1KlM2l6GagOd0%Ugm4l75";
	}
	else
	{
		// return;
		apiid = "1006";
		appkey = "j9973tmv8kdhpsqj3nezp9pz45wr275v";
	}
	std::vector<IClientPlayer*>players;
	g_WorldMgr->getAllPlayers(players);
	
	// for (size_t i = 0; i < players.size(); i++)
	// {
	// 	ClientPlayer* player = players[i];
	// 	if (!player)
	// 		continue;
	// 	MINIW::ScriptVM::game()->callFunction("reportOnlinePlayer", "u[ClientPlayer]ssi", player, apiid, appkey, now);
	// }
	MINIW::ScriptVM::game()->callFunction("reportAllOnlinePlayer_Tmp", "ssi", apiid, appkey, now);
}

/**
 * @brief 云服功能, 设置队伍信息
 * 
 * @param uin 设置队伍的玩家id
 * @param teamid 队伍id
 */
void MpGameSurvive::onPreSetPlayerTeam(int uin, int teamid)
{
	if (!m_WorldMgr || !m_WorldMgr->m_RuleMgr)
	{
		SLOG(INFO) << "PresetTeam m_WorldMgr or m_RuleMgr is null. uin=" << uin << " teamid=" << teamid;
		return;
	}
	IClientPlayer* iplayer = g_WorldMgr->getPlayerByUin(uin);
	if (iplayer)
	{
		ClientPlayer* player = iplayer->GetPlayer();
		if (m_WorldMgr->m_RuleMgr->canAddTeam(teamid))
		{
			SLOG(INFO) << "PresetTeam team invalid. uin=" << uin << " teamid=" << teamid;
			return;
		}
		SLOG(INFO) << "PresetTeam inroom uin=" << uin << " teamid=" << teamid;
		player->setTeam(teamid);
		PlayerAttrib* pAttrib = player->getPlayerAttrib();
		if (pAttrib)
			pAttrib->initPlayerBaseAttr(teamid);
	}
	else
	{
		SLOG(INFO) << "PresetTeam pre uin=" << uin << " teamid=" << teamid;
		int now = MINIW::GetTimeStamp();
		m_PreSetTeam[uin] = {now, teamid};
	}
}

/**
 * @brief 云服功能, 获取玩家预设队伍信息
 * 
 * @param uin 设置队伍的玩家id
 * @return 玩家的预设队伍ID, -1则表示未预设队伍
 */
int MpGameSurvive::getPreSetPlayerTeam(int uin)
{
	const static int min_interval = 20;
	auto it = m_PreSetTeam.find(uin);
    if (it!= m_PreSetTeam.end())
    {
		int now = MINIW::GetTimeStamp();
		if (now - it->second.time > min_interval)
			return -1;
        return it->second.team_id;
    }
    return -1;
}

bool MpGameSurvive::getEnableTeamsInfo(std::vector<TeamInfo>& teams_info)
{
	if (!SurviveGame::getEnableTeamsInfo(teams_info))
		return false;

	if (!GameNetManager::getInstance()
		|| !GameNetManager::getInstance()->getConnection()
		|| !GameNetManager::getInstance()->getConnection()->GetRakPeerInterface()
	)
	{
		return true;
	}
	DataStructures::List<SystemAddress> addresses;
	DataStructures::List<RakNetGUID> guids;
	GameNetManager::getInstance()->getConnection()->GetRakPeerInterface()->GetSystemList(addresses, guids);
	std::map<int, std::vector<int>> pre_team_not_inited;  // 预分配了队伍且已连接, 但未初始化的玩家 map<teamid, vector<uin>>
	for (unsigned int i = 0; i < guids.Size(); i++)
	{
		int uin = guids[i].g;
		if (!uin2Player(uin))  // 玩家未初始化 且 预设了队伍
		{
			int team_id = getPreSetPlayerTeam(uin);
			if (team_id != -1)
				pre_team_not_inited[team_id].push_back(uin);
		}
	}
	if (!pre_team_not_inited.empty())
	{
		// 遍历所有的队伍, 匹配已连接未初始化的玩家
		for (auto it_teams_info = teams_info.begin(); it_teams_info!= teams_info.end(); ++it_teams_info)
		{
			auto it_pre_team_map = pre_team_not_inited.find(it_teams_info->m_TeamID);
			if (it_pre_team_map!= pre_team_not_inited.end())
			{
				for (auto it_pre_uins = it_pre_team_map->second.begin(); it_pre_uins!= it_pre_team_map->second.end(); ++it_pre_uins)
				{
					it_teams_info->m_Members.push_back(*it_pre_uins);
				}
			}
		}
	}
	return true;
}

ClientPlayer* MpGameSurvive::onLoadAINpc(int uin, const std::string name, int team_id)
{
#ifdef IWORLD_SERVER_BUILD
	if (m_WorldMgr == NULL)
	{
		Rainbow::GetICloudProxyPtr()->SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurvive::onDataServerLoadPlayer() no worldmgr");
		return NULL;
	}

	PlayerBriefInfo *info = addPlayerBriefInfo(uin);
	ClientPlayer *player = SANDBOX_NEW (AINpcPlayer);
	snprintf(info->nickname, sizeof(info->nickname), "%s", name.c_str());

	int playerindex = ComposePlayerIndex(info->model, info->geniuslv, info->skinid);
	player->init(uin, info->nickname, playerindex, info->customjson);
	player->m_Body->setPlayerFrameId(info->frameid);
	player->setVipInfo(info->vipinfo);

	if (team_id > 0)
		player->setTeam(team_id);
	else
		player->setTeam(m_WorldMgr->getNewPlayerTeamID(player));

	World *pworld = NULL;
	bool prepare = true;
	bool hasrole = false;
	WCoord startPt;
	IGameMode* gmaker = m_WorldMgr->getGameMakerManager();
	bool random_offset = false;
	if (m_WorldMgr->isGameMakerRunMode())
	{
		//bool prepare = true;
		startPt = m_WorldMgr->getTeamPrePoint(player);
		if (startPt.y < 0 || gmaker->getGameStage() == CGAME_STAGE_RUN)
		{
			if (m_WorldMgr->m_RuleMgr && m_WorldMgr->m_RuleMgr->hasInitialPoint(player->getTeam()))
			{
				startPt = m_WorldMgr->m_RuleMgr->getInitialPoint(player->getTeam()).y >= 0 ? \
					m_WorldMgr->m_RuleMgr->getInitialPoint(player->getTeam()) : \
					m_WorldMgr->m_RuleMgr->getInitialPoint(0);
			}
			else
				startPt = m_WorldMgr->getTeamSpawnPoint(player);
			prepare = false;
		}

		pworld = m_WorldMgr->getOrCreateWorld(0, player);

		gmaker->initPlayerDir(player);
	}
	else
	{
		pworld = m_WorldMgr->getOrCreateWorld(0, player);
		startPt = m_WorldMgr->getSpawnPoint();
		random_offset = true;  // 要落在y轴有实体方块的最高点
		roleInit(pworld->getOWID(), player);
	}

	player->gotoBlockPos(pworld, startPt, random_offset);
	player->setNewPlayer(true);

	pworld->getActorMgr()->ToCastMgr()->spawnPlayerAddRef(player);
	player->release();

	if (m_WorldMgr->isGameMakerRunMode())
	{
		int countplayer = m_bHaveJudge ? m_BriefPlayerInfo.size() : (m_BriefPlayerInfo.size() + 1);
		//云服判断玩家人数要减1（因为服主为客机包含在内）
		if (m_WorldMgr && m_WorldMgr->IsRentServerHost())
		{
			countplayer = countplayer - 1;
		}

		if (gmaker->getGameStage() == CGAME_STAGE_PREPARE)
		{
			onPlayerEnterByCloudServer(countplayer);
		}
		MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", "");

		// 观察者事件接口
		ObserverEvent_Player obevent(player->getUin());
		ObserverEventManager::getSingleton().OnTriggerEvent("Player.JoinTeam", &obevent);
	}
	info->mapid = pworld->getCurMapID();

	PlayerAttrib& playerAttrib = *player->getPlayerAttrib();

	info->overflowhp = playerAttrib.getOverflowHP();
	info->maxhp = playerAttrib.getMaxHP();
	info->hp = playerAttrib.getHP();

	info->overflowstrength = playerAttrib.getOverflowStrength();
	info->maxstrength = playerAttrib.getMaxStrength();
	info->strength = playerAttrib.getStrength();
	info->isAINpc = true;

	info->x = player->getPosition().x;
	info->y = player->getPosition().y;
	info->z = player->getPosition().z;

	WCoord realPos = player->getPosition();

	//先同步文件信息再执行onplayerinit，不然客机第一次进房间会拿不到补给箱的机械胶囊
	if (m_WorldMgr && m_WorldMgr->isGameMakerRunMode() && m_WorldMgr->m_RuleMgr)
	{
		m_WorldMgr->m_RuleMgr->onPlayerInit(player, false, prepare);

	}
	//设置玩家的出生点
	//player->setSpawnPoint(m_WorldMgr->getSpawnPointEx(pworld), false);
	SandboxContext context;
	context.SetData_UserObject("point", m_WorldMgr->getSpawnPointEx(pworld));
	context.SetData_Bool("bSetWorldMgr", false);
	player->Event().Emit("revive_setSpawnPoint", context);
	
	m_WorldMgr->syncPlayerEnter(uin); 

	MINIW::ScriptVM::game()->callFunction("ReqCurUseAchieveByUin", "i", uin);
	//2021-12-20 codeby: wangyang 会员图标
	char buffer[64];
	sprintf(buffer, "%d", uin);
	MINIW::ScriptVM::game()->callFunction("OnPlayerEnter", "s", buffer);
	// 云服同步 触发器/脚本日志 初始化玩家
	MINIW::ScriptVM::game()->callFunction("SyncServerLogCheckPlayerEnter", "i", uin);
	RoleEnterWorld2HostLast(uin, player);

	return player;
#endif
	return NULL;
}

// 客机网络变化的时候重连
void MpGameSurvive::OnReachabilityChanged(int network_status)
{
	LOG_INFO("LSJ OnReachabilityChanged1 %d", network_status);
	if (GetGameNetManagerPtr() && GetGameNetManagerPtr()->isClient())
	{
		LOG_INFO("LSJ OnReachabilityChanged2 %d", network_status);
		if (GetGameNetManagerPtr()->getConnection())
		{
			LOG_INFO("LSJ OnReachabilityChanged3 %d", network_status);
			GetGameNetManagerPtr()->getConnection()->reconnectHost();
		}
	}
}
