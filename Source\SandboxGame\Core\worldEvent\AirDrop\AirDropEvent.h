#pragma once
#include "worldEvent/WorldEvent.h"
#include "Math/Vector3f.h"
#include "AirDropCraft.h"
#include "AirDropChest.h"
#include <vector>
#include <memory>
#include "worldEvent/WorldEventCsv.h"

#include "SandboxGame.h"

class AirDropcraft;
class AirDropChest;
class ActorCubeChest;
class WorldStorageBox;

class AirChestInfo {
public:
    WCoord pos;
    int event_id;
    int spawn_time;
    ActorCubeChest* pActorChest;


    int getCount();
    bool isOpenning();
    WorldStorageBox* getWorldStorageBox();
};

class EXPORT_SANDBOXGAME AirDropEvent : public WorldEvent {
public:
    enum class State {
        INITIALIZED,    // 初始化完成
        ANNOUNCED,      // 已广播
        AIRCRAFT_SPAWNED, // 飞机已生成
        BOX_DROPPED,    // 空投箱已投放
        BOX_LANDED,     // 空投箱已着陆
        BOX_OPENED,     // 空投箱已打开
        COMPLETED       // 事件完成
    };

    AirDropEvent(const WorldEventDef& config);
    ~AirDropEvent() override;

    // 实现WorldEvent的虚函数
    virtual void OnStart() override;
    virtual void OnUpdate(float deltaTime) override;
    virtual void OnEnd() override;

    // 空投特有的功能接口
    const Rainbow::Vector3f& GetDropLocation() const { return m_drop_pos; }
    const Rainbow::Vector3f& GetStartLocation() const { return m_start_pos; }
    const Rainbow::Vector3f& GetEndLocation() const { return m_end_pos; }
    const Rainbow::Vector3f& GetCurrLocation() const {
        if (!m_aircraft) {
            return m_start_pos;
        }
        return m_aircraft->GetCurrentPosition();
    }
    
    State GetState() const { return m_state; }

    static std::vector<AirChestInfo> chestList;
    static void ClearChestList();
    static void RemoveChest(AirChestInfo* chest);
    static int isDaytimeStart();



private:    
    // 状态相关
    State m_state{State::INITIALIZED};
    float m_stateTimer{0.0f};
    
    // 空投位置相关
    Rainbow::Vector3f m_drop_pos;
    // 飞机起始位置相关
    Rainbow::Vector3f m_start_pos;
    // 飞机终点位置相关
    Rainbow::Vector3f m_end_pos;
    
    // 实体对象
    std::unique_ptr<AirDropCraft> m_aircraft;
    std::unique_ptr<AirDropChest> m_dropBox;
 
    // 配置参数
    std::string m_visualEffectId;
    std::string m_soundEffectId;
 
    // 内部辅助方法
    bool generateDropLocation();
    Rainbow::Vector3f generateRandomPointInRectangle(const Rainbow::Vector3f& left_top, const Rainbow::Vector3f& right_botm);
    void genStartEndPos();

     void SpawnAircraft();
    void SpawnDropBox();
    void BroadcastAnnouncement();
    void UpdateAircraftMovement(float deltaTime);
    void UpdateBoxPhysics(float deltaTime);
      
};
