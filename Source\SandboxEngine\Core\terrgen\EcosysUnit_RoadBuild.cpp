#include "EcosysUnit_RoadBuild.h"
#include "world.h"
#include "OgreBlock.h"
#include "chunk.h"
#include "GameStatic.h"
#include <random>
#include <limits>
#include "EcosysUnit_City.h"

#include "IPlayerControl.h"

static const int MIN_NODE_DISTANCE = 20;    // 减小最小节点间距，允许更密集的路网
static const int ROAD_CHECK_RADIUS = 800;   // 检查范围
static const float CITY_NODE_CHANCE = 0.2f; // 城市节点概率
static const int MAX_SLOPE = 4;            // 最大允许坡度
static const int MAX_NODES = 2000;         // 最大节点数
static const int ROAD_WIDTH = 6;           // 总道路宽度
static const int ROAD_INNER_WIDTH = 6;     // 内部宽度
static const float ROAD_EDGE_WIDTH = 0.8f; // 边缘宽度
static const int MAX_CONNECTIONS = 2;      // 每个节点最大连接数
static const float MIN_ANGLE = M_PI / 3;   // 最小分叉角度(30度)
static const float ROAD_LINE_WIDTH = 0.8f;      // 道路标线宽度
static const float ROAD_LINE_INTERVAL = 15.0f;   // 标线间隔
static const float ROAD_LINE_LENGTH = 10.0f;     // 标线长度

    // 轨道相关常量
static constexpr float TRACK_WIDTH = 3.0f;         // 轨道总宽度（双轨道，增加宽度）
static constexpr float TRACK_RAIL_WIDTH = 0.5f;    // 单侧轨道宽度
static constexpr float TRACK_GAP = 2.0f;           // 轨道之间的间距
static constexpr float TRACK_TIE_INTERVAL = 4.0f;  // 横档之间的间距

// 生物群系ID定义
#define BIOME_GRASSLAND 1   // 草地生物群系
#define BIOME_DESERT 2      // 沙漠生物群系
#define BIOME_SNOW 61       // 雪地生物群系

// 不同生物群系对应的顶层填充方块
#define TOP_FILL_BLOCK_GRASSLAND 100  // 草地顶层填充方块
#define TOP_FILL_BLOCK_DESERT 29      // 沙漠顶层填充方块
#define TOP_FILL_BLOCK_SNOW 122       // 雪地顶层填充方块

// 不同生物群系对应的下层填充方块
#define BOTTOM_FILL_BLOCK_GRASSLAND 101  // 草地下层填充方块（泥土）
#define BOTTOM_FILL_BLOCK_DESERT 29    // 沙漠下层填充方块（沙岩）
#define BOTTOM_FILL_BLOCK_SNOW 122     // 雪地下层填充方块（雪下方块）

#define BLOCK_StoneStep 674//路面
#define BLOCK_StoneStep_White 675//路中导线
#define BLOCK_SandstoneStep 677//路边
#define BLOCK_RockStep 509
#define BLOCK_CementBrickStep 510
#define BLOCK_RockBrickStep 674
#define BLOCK_SulfurBrickStep 674


static const char& GetWayBlockData(const int& blockid)
{
    static std::map<int, char> s_BlockData = {
        {BLOCK_StoneStep,0}, //
        {BLOCK_StoneStep_White,0}, // 
        {BLOCK_SandstoneStep,1}, // 
    };
    auto iter = s_BlockData.find(blockid);
    if (iter != s_BlockData.end()) return iter->second;
    return 0;
}

bool IsRoadCanPlaceBiome(World* pworld, int x, int z)
{
    int biomeId = pworld->getBiomeId(x, z);
    if (BIOME_OCEAN == biomeId || BIOME_PLAINS == biomeId || BIOME_DESERT == biomeId || BIOME_BEACH == biomeId || BIOME_ICE_PLAINS == biomeId) return true;
    return false;
}

EcosysUnitRoadBuild::EcosysUnitRoadBuild():m_ProcessedChunindex(100)
{
	
}
bool EcosysUnitRoadBuild::isValidRoadBiome(int biomeId) const
{
    // 根据生物群系ID判断是否适合生成道路
    //switch (biomeId) {
    //case BIOME_PLAINS:
    //case BIOME_DESERT:
    //case BIOME_DESERT_HILLS:
    //case BIOME_ICE_PLAINS:
    //case BIOME_ICE_PLAINS:
        return true;
    //default:
    //    // 不在以下生物群系生成道路：
    //    // - 海洋
    //    // - 陡峭的山地
    //    // - 密???森林
    //    // - 沼泽
    //    return false;
    //}
}

void EcosysUnitRoadBuild::onWorldLoaded(World* pworld)
{
    if (!m_CalculatRoad) {
        m_CalculatRoad = true;
        addToWorld(pworld);
    }
    if (!m_GenRoad) {
        m_GenRoad = true;  // 内部触发chunk加载会重复进该函数，提前设置成true
        // 生成所有道路节点的实际道路
        for (auto& node : m_RoadNodes) {
            if (!node.roadBuilt) {
                if (node.start.distanceTo(node.end) < 2) continue; // 忽略过短的路径
                
                node.start.y = calculateRoadHeight(pworld, node.start);
                node.end.y = calculateRoadHeight(pworld, node.end);
                
                if (node.roadType == ROAD_TYPE_TRACK) {
                    // 轨道生成
                    float dx = node.end.x - node.start.x;
                    float dz = node.end.z - node.start.z;
                    
                    if (dx == 0 || dz == 0) {
                        // 直线轨道
                        generateTrack(pworld, node.start, node.end);
                        
                        // 保存路径段信息
                        node.pathSegments.clear();
                        node.pathSegments.push_back({node.start, node.end});
                    } else {
                        // 有拐点的轨道
                        generateManhattanTrack(pworld, node.start, node.end);
                    }
                }
            	else {
                    // 普通道路
                    float dx = node.end.x - node.start.x;
                    float dz = node.end.z - node.start.z;
                    
                    if (dx == 0 || dz == 0) {
                        // 直线道路
                        generateStraightRoadSegment(pworld, node.start, node.end);
                        
                        // 保存路径段信息
                        node.pathSegments.clear();
                        node.pathSegments.push_back({node.start, node.end});
                    } else {
                        // 斜向路径，使用Z字形道路或曼哈顿路径
                        float lenX = fabs(dx);
                        float lenZ = fabs(dz);
                        
                        if (lenX > 2 * lenZ || lenZ > 2 * lenX) {
                            // 如果x或z方向特别长，使用Z字形道路
                            generateZShapedRoad(pworld, node.start, node.end, node.roadType);
                        } else {
                            // 否则使用曼哈顿路径
                            generateManhattanRoad(pworld, node.start, node.end);
                        }
                    }
                }
                
                node.roadBuilt = true;
            }
        }
    }
}

bool EcosysUnitRoadBuild::addToWorld(World* pworld) {
    if (!pworld) 
    {
        GEN_LOG_INFO("EcosysUnitRoadBuild addToWorld pworld is null");
        return false;
    }

    auto citymgr = pworld->getCityMgr();
    if (!citymgr)
    {
        GEN_LOG_INFO("EcosysUnitRoadBuild addToWorld citymgr is null");
        return false;
    }

    // 1. 在区块范围内随机选择位置
    const int OFFSET_RANGE = CHUNK_BLOCK_X / 2;  // 允许在区块内偏移的范围

    const auto& allcitydata = citymgr->GetAllCityData();
    
    // 为每个城市存储最短的两条连接
    struct CityConnection {
        ChunkIndex otherCity;
        CityConnectionPoints points;
        float distance;
    };
    // 使用set记录已处理的城市对
    std::set<std::pair<ChunkIndex, ChunkIndex>> processedCityPairs;
    
    // 记录每个城市的连接数量
    std::map<ChunkIndex, int> cityConnectionCount;

    // 处理单独建筑的道路连接
    const auto& allSingleBuildData = citymgr->GetAllSingleBuildData();
    
    // 为单独建筑创建临时城市数据结构，方便复用现有的道路生成逻辑
    std::vector<CityData> tempBuildCityData;
    
    // for (const auto& p : allSingleBuildData) {
    //     for (const auto& buildData : p.second.buildData) {
    //         CityData tempCity;
    //         // 将建筑位置转换为区块坐标
    //         tempCity.leftDown.x = buildData.x;
    //         tempCity.leftDown.z = buildData.z;
    //         // 设置建筑范围
    //         tempCity.range.x() = std::max(1, buildData.rangeX);
    //         tempCity.range.y() = std::max(1, buildData.rangeZ);
    //         // 添加到临时列表
    //         tempBuildCityData.push_back(tempCity);
    //     }
    // }

    for (const auto& citydata : allcitydata)
    {
        tempBuildCityData.push_back(citydata);
    }

    GEN_LOG_INFO("EcosysUnitRoadBuild addToWorld tempBuildCityData size:%d", tempBuildCityData.size());

    for (const auto& citydata : tempBuildCityData)
    {
        auto index2 = citydata.leftDown;
        int cityWidth = citydata.range.x() * CHUNK_BLOCK_X;  // 城市宽度
        int cityLength = citydata.range.y() * CHUNK_BLOCK_Z; // 城市长度
        
        // 如果当前城市已经有两条连接，跳过
        if (cityConnectionCount[citydata.leftDown] >= 2)
            continue;

        // 存储所有可能的连接
        std::vector<CityConnection> possibleConnections;

        // 为每个未处理的城市找到最优连接点
        for (const auto& otherCity : tempBuildCityData)
        {
            if (otherCity.leftDown == citydata.leftDown)
                continue;
            
            // 创建城市对（确保较小的索引在前，较大的在后）
            auto cityPair = std::minmax(citydata.leftDown, otherCity.leftDown);
            
            // 如果这对城市已经处理过，跳过
            if (processedCityPairs.find(cityPair) != processedCityPairs.end())
                continue;

            // 计算另一个城市的中心点
            int otherCityWidth = otherCity.range.x() * CHUNK_BLOCK_X;
            int otherCityLength = otherCity.range.y() * CHUNK_BLOCK_Z;

            // 在城市边界上找到最优的道路连接点
            CityConnectionPoints bestConnection = findBestRoadConnectionPoint(
                pworld,
                citydata,
                otherCity
            );

            // 添加到可能的连接列表
            possibleConnections.push_back({
                otherCity.leftDown,
                bestConnection,
                bestConnection.distance
            });
        }

        // 按距离排序所有可能的连接
        std::sort(possibleConnections.begin(), possibleConnections.end(),
            [](const CityConnection& a, const CityConnection& b) {
                return a.distance < b.distance;
            });

        // 计算当前城市还可以建立的连接数量
        int remainingConnections = citydata.roadBeginPosList.size() > 2 ? 2 : citydata.roadBeginPosList.size(); // - cityConnectionCount[citydata.leftDown];

        // 只保留最短的可用连接
        for (size_t i = 0; i < possibleConnections.size() && remainingConnections > 0; ++i)
        {
            const auto& conn = possibleConnections[i];
            
            // 自己已经连过超过2条，则可以跳过了
            if (cityConnectionCount[citydata.leftDown] >= 2)
                break;

            auto cityPair = std::minmax(citydata.leftDown, conn.otherCity);
            
            // 如果还没处理过这对城市
            if (processedCityPairs.find(cityPair) == processedCityPairs.end())
            {
                // 建立连接
                processNodePos(pworld, conn.points.point1, conn.points.point2);
                
                // 更新两个城市的连接计数
                cityConnectionCount[citydata.leftDown]++;
                cityConnectionCount[conn.otherCity]++;
                
                // 记录这对城市已经处理过
                processedCityPairs.insert(cityPair);
                
                remainingConnections--;
            }
        }
    }
    pworld->getCityMgr()->saveFile(true);
    m_CalculatRoad = true;
    GEN_LOG_INFO("EcosysUnitRoadBuild addToWorld end roads:%d", m_RoadNodes.size());
    return true;
}


CityConnectionPoints EcosysUnitRoadBuild::findBestRoadConnectionPoint(World* pworld, const CityData& citydata1, const CityData& citydata2)
{
    // 找到两个城市之间最近的采样点对
    float minDistance = FLT_MAX;
    CityConnectionPoints bestConnection;
    bestConnection.point1 = WCoord(0, -1, 0);
    bestConnection.point2 = WCoord(0, -1, 0);
    bestConnection.distance = FLT_MAX;
    const std::vector<WCoord>& samplePoints1 = citydata1.roadBeginPosList;
    const std::vector<WCoord>& samplePoints2 = citydata2.roadBeginPosList;
    for (const auto& point1 : samplePoints1) {
        for (const auto& point2 : samplePoints2) {
            float terrainScore1 = evaluateTerrainSuitability(pworld, point1);
            float terrainScore2 = evaluateTerrainSuitability(pworld, point2);
            float obstacleScore1 = evaluateObstacles(pworld, point1);
            float obstacleScore2 = evaluateObstacles(pworld, point2);
            
            // 计算两点间距离
            float dx = point2.x - point1.x;
            float dz = point2.z - point1.z;
            float distance = sqrt(dx * dx + dz * dz);
            
            // 综合评分（考虑距离和地形因素）
            float totalScore = distance * 0.4f + 
                             (terrainScore1 + terrainScore2) * 0.3f + 
                             (obstacleScore1 + obstacleScore2) * 0.3f;

            if (totalScore < minDistance) {
                minDistance = totalScore;
                bestConnection.point1 = point1;
                bestConnection.point2 = point2;
                bestConnection.distance = distance;
            }
        }
    }

    return bestConnection;
}

void EcosysUnitRoadBuild::save(flatbuffers::FlatBufferBuilder& builder, std::vector<flatbuffers::Offset<FBSave::CityRoadNodeData>>& roaddataFB)
{
    for (const auto& node : m_RoadNodes)
    {
        roaddataFB.push_back(FBSave::CreateCityRoadNodeData(
            builder,
            node.start.x,
            node.start.z,
            node.end.x,
            node.end.z,
            static_cast<int8_t>(node.roadType) // 保存道路类型
        ));
    }
}

void EcosysUnitRoadBuild::load(World* pworld, const FBSave::CityFBData* src)
{
    if (!src) return;
    const auto* roadDataFB = src->roadData();
    if (roadDataFB)
    {
        for (const auto& node : *roadDataFB)
        {
            // 创建道路节点
            WCoord start(node->startX(), 0, node->startZ());
            WCoord end(node->endX(), 0, node->endZ());
            
            // 获取道路类型，如果存在的话
            RoadType roadType = ROAD_TYPE_NORMAL; // 默认为普通道路
            if (node->roadType() >= 0 && node->roadType() <= 1) {
                roadType = static_cast<RoadType>(node->roadType());
            }
            
            // 添加新的道路节点，但不立即生成
            RoadNode newNode(start, end, false, roadType);
            m_RoadNodes.push_back(newNode);
        }
        
        if (m_RoadNodes.size() == roadDataFB->size() && m_RoadNodes.size() > 0)
            m_CalculatRoad = true;
        else
        {
            m_CalculatRoad = false;
            m_RoadNodes.clear();  // 清空异常的道路节点
        }
    }
}

// 评估地形适宜性的辅助函数
float EcosysUnitRoadBuild::evaluateTerrainSuitability(World* pworld, const WCoord& pos)
{
    const int SAMPLE_RADIUS = 5;
    float totalScore = 0;
    int sampleCount = 0;

    int centerHeight = pos.y == 0 ? pworld->getTopHeight(pos.x, pos.z) : pos.y;
    for (int dx = -SAMPLE_RADIUS; dx <= SAMPLE_RADIUS; dx++)
    {
        for (int dz = -SAMPLE_RADIUS; dz <= SAMPLE_RADIUS; dz++)
        {
            int height = pworld->getTopHeight(pos.x + dx, pos.z + dz);
            
            // 计算坡度
            float slope = abs(height - centerHeight) / sqrt(dx * dx + dz * dz);
            float slopeScore = 1.0f - std::min(1.0f, slope / MAX_SLOPE);
            
            totalScore += slopeScore;
            sampleCount++;
        }
    }

    return sampleCount > 0 ? totalScore / sampleCount : 0;
}

// 评估障碍物的辅助函数
float EcosysUnitRoadBuild::evaluateObstacles(World* pworld, const WCoord& pos)
{
    const int CHECK_HEIGHT = 5;
    int obstacleCount = 0;
    int totalChecks = CHECK_HEIGHT;

    int baseHeight = pworld->getTopHeight(pos.x, pos.z);
    //int baseHeight = pos.y == 0 ? pworld->getTopHeight(pos.x, pos.z) : pos.y;
    
    for (int y = 1; y <= CHECK_HEIGHT; y++)
    {
        if (pworld->getBlockID(pos.x, baseHeight + y, pos.z) != BLOCK_AIR)
        {
            obstacleCount++;
        }
    }

    return 1.0f - (float)obstacleCount / totalChecks;
}

bool EcosysUnitRoadBuild::processNodePos(World* pworld, const WCoord& point1, const WCoord& point2, bool load) {
    if (!pworld) return false;
    // 检查点的有效性
    if (point1 == point2) return false;

    //// 计算线路长度
    //float dx = point2.x - point1.x;
    //float dz = point2.z - point1.z;
    //float length = sqrt(dx * dx + dz * dz);

    // 如果道路过短，不处理
    // if (length < 10.0f) return false;

    // 只添加节点，不生成道路
    // 道路将在onWorldLoaded时生成
    addRoadNodes(point1, point2);
    return true;
}

bool EcosysUnitRoadBuild::canConnectNodes(World* pworld, const WCoord& start, const WCoord& end) {
    if (!pworld) return false;

    float dist = start.distanceTo(end);

    // 1. 检查距离是否在合理范围内
    if (dist < MIN_NODE_DISTANCE || dist > ROAD_CHECK_RADIUS) {
        return false;
    }

    // 2. 检查高度差是否合理
    int startHeight = pworld->getTopHeight(start.x, start.z);
    int endHeight = pworld->getTopHeight(end.x, end.z);
    if (abs(startHeight - endHeight) > MAX_SLOPE * (dist / 64)) {
        return false; // 坡度太大
    }

    // 3. 检查路径上的地形
    float dx = (end.x - start.x) / dist;
    float dz = (end.z - start.z) / dist;
    
    for (float t = 0; t < dist; t += 16.0f) {
        int checkX = start.x + t * dx;
        int checkZ = start.z + t * dz;
        
        int pathHeight = pworld->getTopHeight(checkX, checkZ);
        float progress = t / dist;
        int expectedHeight = startHeight + (endHeight - startHeight) * progress;
        
        if (abs(pathHeight - expectedHeight) > MAX_SLOPE) {
            return false;
        }
    }

    return true;
}

// 辅助函数：计算点到线段的距离
float EcosysUnitRoadBuild::pointToLineSegmentDistance(const WCoord& point,
    const WCoord& lineStart,
    const WCoord& lineEnd) const {
    float dx = lineEnd.x - lineStart.x;
    float dz = lineEnd.z - lineStart.z;
    float length2 = dx * dx + dz * dz;  // 线段长度的平方

    if (length2 == 0) {
        // 线段实际上是一个点
        return sqrt(pow(point.x - lineStart.x, 2) + pow(point.z - lineStart.z, 2));
    }

    // 计算投影点的参数 t
    float t = ((point.x - lineStart.x) * dx + (point.z - lineStart.z) * dz) / length2;
    t = std::max(0.0f, std::min(1.0f, t));  // 限制在 [0,1] 范围内

    // 计算投影点坐标
    float projX = lineStart.x + t * dx;
    float projZ = lineStart.z + t * dz;

    // 返回点到投影点的距离
    return sqrt(pow(point.x - projX, 2) + pow(point.z - projZ, 2));
}

// 修改函数签名，增加参数控制是否清理障碍物
int EcosysUnitRoadBuild::calculateRoadHeight(World* pworld, const WCoord& pos, bool clearObstacle) {
    if (!pworld) return 64;  // 默认高度

    // 2. 采样周围高度寻找最低点
    const int SAMPLE_RADIUS = 3;
    int lowestHeight = 100;

    for (int dx = -SAMPLE_RADIUS; dx <= SAMPLE_RADIUS; dx++) {
        for (int dz = -SAMPLE_RADIUS; dz <= SAMPLE_RADIUS; dz++) {
            int height = pworld->getTopHeight(pos.x + dx, pos.z + dz);
            if (height == 0)
            {
                ChunkIndex chunkIdx(BlockDivSection(pos.x + dx), BlockDivSection(pos.z + dz));
                pworld->syncLoadChunk(chunkIdx, GetIPlayerControl() ? GetIPlayerControl()->GetChunkViewerForCtrl() : nullptr);
                height = pworld->getTopHeight(pos.x + dx, pos.z + dz);
            }
            
            lowestHeight = std::min(lowestHeight, height);
        }
    }

    // 3. 计算目标高度（略高于最低点）
    int targetHeight = lowestHeight - 1;

    // 6. 清理障碍物（根据参数决定是否执行）
    if (clearObstacle) {
        clearObstacles(pworld, pos.x, targetHeight + 1, pos.z);
    }

    return targetHeight;
}

// 修改：清理障碍物的辅助函数，排除道路方块
void EcosysUnitRoadBuild::clearObstacles(World* world, int x, int targetHeight, int z) {
    // 定义道路方块ID列表
    static const std::unordered_set<int> roadBlockIds = {
        BLOCK_StoneStep,
        BLOCK_StoneStep_White,
        BLOCK_SandstoneStep,
        BLOCK_RockStep,
        BLOCK_CementBrickStep,
        BLOCK_RockBrickStep,
        BLOCK_SulfurBrickStep
    };
    
    // 清理目标高度以上的所有非道路方块
    for (int y = targetHeight + 1; y <= targetHeight + 20; y++) {
        int blockId = world->getBlockID(x, y, z);
        // 如果不是空气且不是道路方块，则清除它
        if (blockId != BLOCK_AIR && roadBlockIds.find(blockId) == roadBlockIds.end()) {
            world->setBlockAll(x, y, z, BLOCK_AIR, 0);
        }
    }
}

// 新增：检查线段相交的辅助函数
bool EcosysUnitRoadBuild::doLinesIntersect(const WCoord& p1, const WCoord& p2, const WCoord& p3, const WCoord& p4) {
    // 计算方向向量
    float dx1 = p2.x - p1.x;
    float dz1 = p2.z - p1.z;
    float dx2 = p4.x - p3.x;
    float dz2 = p4.z - p3.z;

    // 计算叉积分母
    float denominator = dx1 * dz2 - dz1 * dx2;

    // 如果分母为0，线段平行
    if (abs(denominator) < 0.0001f) return false;

    // 计算参数t和u
    float t = ((p3.x - p1.x) * dz2 - (p3.z - p1.z) * dx2) / denominator;
    float u = ((p3.x - p1.x) * dz1 - (p3.z - p1.z) * dx1) / denominator;

    // 检查t和u是否在[0,1]范围内
    return (t >= 0 && t <= 1 && u >= 0 && u <= 1);
}


WCoord EcosysUnitRoadBuild::calculateBezierPoint(const WCoord& start, const WCoord& control, const WCoord& end, float t)
{
    float mt = 1.0f - t;
    float mt2 = mt * mt;
    float t2 = t * t;

    return WCoord(
        start.x * mt2 + control.x * 2 * mt * t + end.x * t2,
        start.y * mt2 + control.y * 2 * mt * t + end.y * t2,
        start.z * mt2 + control.z * 2 * mt * t + end.z * t2
    );
}

WCoord EcosysUnitRoadBuild::calculateControlPoint(const WCoord& start, const WCoord& end) const {
    // 计算基本方向和距离
    float dx = end.x - start.x;
    float dz = end.z - start.z;
    float dist = sqrt(dx * dx + dz * dz);

    // 归一化方向向量
    float dirX = dx / dist;
    float dirZ = dz / dist;

    // 计算垂直方向
    float perpX = -dirZ;
    float perpZ = dirX;

    // 增加弯曲程度
    float curveFactor = dist * 0.4f;  // 增加到0.4，使弯曲更明显

    // 根据距离动态调整偏移量
    float baseOffset = dist / 4.0f;  // 基础偏移量
    float randomOffset = GenRandomFloatRange(-baseOffset, baseOffset);

    // 检查起点和终点的连接情况，调整弯曲方向
    bool startHasMultipleConnections = false;
    bool endHasMultipleConnections = false;
    WCoord startAvgDir(0, 0, 0);
    WCoord endAvgDir(0, 0, 0);

    // 根据连接情况调整控制点
    float offsetX = perpX * randomOffset;
    float offsetZ = perpZ * randomOffset;

    if (startHasMultipleConnections) {
        // 使控制点偏向其他连接的反方向
        float startAngle = atan2(startAvgDir.z, startAvgDir.x);
        float currentAngle = atan2(dirZ, dirX);
        float angleDiff = normalizeAngle(currentAngle - startAngle);

        // 根据角度差调整偏移
        offsetX += perpX * curveFactor * sin(angleDiff);
        offsetZ += perpZ * curveFactor * sin(angleDiff);
    }

    if (endHasMultipleConnections) {
        // 使控制点偏向其他连接的反方向
        float endAngle = atan2(endAvgDir.z, endAvgDir.x);
        float currentAngle = atan2(-dirZ, -dirX);
        float angleDiff = normalizeAngle(currentAngle - endAngle);

        // 根据角度差调整偏移
        offsetX += perpX * curveFactor * sin(angleDiff);
        offsetZ += perpZ * curveFactor * sin(angleDiff);
    }

    // 控制点位置
    WCoord control(
        start.x + dx * 0.5f + offsetX,  // 移到中点位置
        (start.y + end.y) / 2,          // 高度取中间值
        start.z + dz * 0.5f + offsetZ
    );

    return control;
}

// 辅助函数：标准化角度到 [-π, π] 范围
float EcosysUnitRoadBuild::normalizeAngle(float angle) const {
    while (angle > M_PI) angle -= 2 * M_PI;
    while (angle < -M_PI) angle += 2 * M_PI;
    return angle;
}

// 添加计算曲率的辅助函数
float EcosysUnitRoadBuild::calculateCurvature(const WCoord& start, const WCoord& control, const WCoord& end) const {
    // 计算两个向量
    float dx1 = control.x - start.x;
    float dz1 = control.z - start.z;
    float dx2 = end.x - control.x;
    float dz2 = end.z - control.z;

    // 计算向量长度
    float len1 = sqrt(dx1 * dx1 + dz1 * dz1);
    float len2 = sqrt(dx2 * dx2 + dz2 * dz2);

    if (len1 < 0.0001f || len2 < 0.0001f) return 0.0f;

    // 计算单位向量的点积
    float dotProduct = (dx1 * dx2 + dz1 * dz2) / (len1 * len2);

    // 将点积限制在[-1,1]范围内
    dotProduct = std::max(-1.0f, std::min(1.0f, dotProduct));

    // 返回曲率估计值（使用角度差作为曲率的近似）
    return 1.0f - dotProduct;  // 值范围[0,2]，0表示直线，2表示180度转弯
}

void EcosysUnitRoadBuild::generateSmoothRoadSegment(World* pworld, const WCoord& start, const WCoord& end) {
    // 计算控制点
    WCoord control = calculateControlPoint(start, end);

    // 1. 计算边界框 - 使用ROAD_WIDTH
    int minX = std::min({ start.x, control.x, end.x }) - ROAD_WIDTH * 2;
    int maxX = std::max({ start.x, control.x, end.x }) + ROAD_WIDTH * 2;
    int minZ = std::min({ start.z, control.z, end.z }) - ROAD_WIDTH * 2;
    int maxZ = std::max({ start.z, control.z, end.z }) + ROAD_WIDTH * 2;

    // 2. 预计算曲线上的点 - 增加采样精度
    float curvature = calculateCurvature(start, control, end);
    float baseStep = 0.005f;  // 增加基础采样精度
    float step = baseStep / (1.0f + curvature * 4.0f);  // 根据曲率更激进地调整步长
    
    // 存储曲线点及其累积距离
    std::vector<std::pair<WCoord, float>> curvePointsWithDist;
    float accumulatedDist = 0.0f;
    WCoord prevPoint;
    
    float blend = 0.5f; // 地形适应程度，可调整
    float lasty = start.y;
    for (float t = 0; t <= 1.0f; t += step) {
        WCoord point = calculateBezierPoint(start, control, end, t);

        // 计算目标高度
        int terrainHeight = calculateRoadHeight(pworld, point);
        float targetY = lasty * (1 - blend) + terrainHeight * blend;

        // 限制与上一个点的高度差
        const float MAX_HEIGHT_STEP = 1.0f;  // 最大允许的高度差
        if (t > 0) {
            if (targetY > lasty + MAX_HEIGHT_STEP) {
                targetY = lasty + MAX_HEIGHT_STEP;
            } else if (targetY < lasty - MAX_HEIGHT_STEP) {
                targetY = lasty - MAX_HEIGHT_STEP;
            }
        }

        point.y = targetY;
        lasty = point.y;

        if (t > 0) {
            accumulatedDist += point.distanceTo(prevPoint);
        }
        curvePointsWithDist.push_back({point, accumulatedDist});
        prevPoint = point;
    }
    // 确保终点被添加
    if (curvePointsWithDist.empty() || curvePointsWithDist.back().first != end) {
        accumulatedDist += end.distanceTo(prevPoint);
        curvePointsWithDist.push_back({end, accumulatedDist});
    }

    // 用于收集被修改的区块
    std::set<ChunkIndex> modifiedChunks;

    // 遍历区域
    for (int x = minX; x <= maxX; x++) {
        for (int z = minZ; z <= maxZ; z++) {
            float minDist = FLT_MAX;
            WCoord nearestPoint;
            float roadDist = 0.0f;

            // 找到最近的曲线点和在道路上的累积距离
            for (size_t i = 0; i < curvePointsWithDist.size(); i++) {
                const auto& point = curvePointsWithDist[i].first;
                float dist = sqrt(pow(x - point.x, 2) + pow(z - point.z, 2));
                
                if (dist < minDist) {
                    minDist = dist;
                    nearestPoint = point;
                    roadDist = curvePointsWithDist[i].second;
                }
            }

            if (minDist <= ROAD_WIDTH) {
                // 记录当前方块所在的区块
                ChunkIndex chunkIdx(BlockDivSection(x), BlockDivSection(z));
                modifiedChunks.insert(chunkIdx);

                int targetY = nearestPoint.y;
                clearObstacles(pworld, x, targetY, z);

                if (minDist <= ROAD_INNER_WIDTH) {
                    // 内部区域
                    if (minDist > ROAD_INNER_WIDTH - ROAD_EDGE_WIDTH) {
                        // 边缘的砖块
                        pworld->setBlockAll(x, targetY, z, BLOCK_SandstoneStep, GetWayBlockData(BLOCK_SandstoneStep));
                    } else {
                        // 使用累积距离来判断标线位置
                        bool isOnLine = false;
                        if (minDist < ROAD_LINE_WIDTH) {
                            const float totalLength = ROAD_LINE_LENGTH + ROAD_LINE_INTERVAL;
                            float modDist = fmod(roadDist, totalLength);
                            isOnLine = modDist < ROAD_LINE_LENGTH;
                        }

                        int blockid = BLOCK_StoneStep;
                        if (isOnLine) {
                            // 绘制白色标线
                            blockid = BLOCK_StoneStep_White;
                        }
                        pworld->setBlockAll(x, targetY, z, blockid, GetWayBlockData(blockid));
                    }
                }
                else {
                    // 外部边缘区域使用砖块
                    pworld->setBlockAll(x, targetY, z, BLOCK_RockStep, GetWayBlockData(BLOCK_RockStep));
                }

                // 在特定位置放置不同颜色的混凝土
                if (abs(minDist - 2.5f) < 0.2f) {
                    pworld->setBlockAll(x, targetY, z, BLOCK_CementBrickStep, 0);
                }
                else if (abs(minDist - 3.0f) < 0.2f) {
                    pworld->setBlockAll(x, targetY, z, BLOCK_RockBrickStep, 0);
                }
                else if (abs(minDist - 3.5f) < 0.2f) {
                    pworld->setBlockAll(x, targetY, z, BLOCK_SulfurBrickStep, 0);
                }
            }
        }
    }

    // 重新触发被修改区块的加载
    for (const auto& chunkIdx : modifiedChunks) {
        pworld->tryLoadChunk(chunkIdx, nullptr, true);
    }
}

void EcosysUnitRoadBuild::leaveWorld()
{
    m_CalculatRoad = false;
    m_GenRoad = false;
    m_RoadNodes.clear();
    m_ProcessedChunindex.clear();
}

IMPLEMENT_GETMETHOD_MANUAL_INIT(EcosysUnitRoadBuild)

void EcosysUnitRoadBuild::generateManhattanRoad(World* pworld, const WCoord& start, const WCoord& end) {
    // L形路径
    WCoord midPoint(end.x, (start.y + end.y) / 2, start.z);
    midPoint.y = calculateRoadHeight(pworld, midPoint);
    
    // 查找对应的RoadNode并保存路径段信息
    for (auto& node : m_RoadNodes) {
        if ((node.start == start && node.end == end) || 
            (node.start == end && node.end == start)) {
            // 清空之前的路径段信息
            node.pathSegments.clear();
            // 添加新的路径段
            node.pathSegments.push_back({start, midPoint});
            node.pathSegments.push_back({midPoint, end});
            break;
        }
    }
    
    // 先生成水平段（确保精确连接）
    generateStraightRoadSegment(pworld, start, midPoint);

    // 再生成垂直段（确保精确连接）
    generateStraightRoadSegment(pworld, end, midPoint);

    // 生成转角连接
    generateRoadCorner(pworld, midPoint);
}

// 修改为直角转角处理
void EcosysUnitRoadBuild::generateRoadCorner(World* pworld, const WCoord& cornerPoint) {
    // 为转角处定义一个正方形区域
    const int CORNER_SIZE = ROAD_WIDTH;
    
    // 确定L形方向 - 假设是从左下到右上的L形
    // 可以根据实际需要调整这个逻辑
    
    // 遍历转角区域
    for (int dx = -CORNER_SIZE; dx <= CORNER_SIZE; dx++) {
        for (int dz = -CORNER_SIZE; dz <= CORNER_SIZE; dz++) {
            int x = cornerPoint.x + dx;
            int z = cornerPoint.z + dz;
            
            float distToRoad = std::max(abs(dx), abs(dz));  // 这里用最大距离，为了让四边都生成边界

            // 道路高度
            float targetY = cornerPoint.y;
            
            // 放置道路方块，不生成标线
            placeRoadBlock(pworld, x, targetY, z, distToRoad, false);
        }
    }

    for (int dx = -CORNER_SIZE; dx <= CORNER_SIZE; dx++) {
        for (int dz = -CORNER_SIZE; dz <= CORNER_SIZE; dz++) {
            if (dx == -CORNER_SIZE || dx == CORNER_SIZE || dz == -CORNER_SIZE || dz == CORNER_SIZE) {
                int x = cornerPoint.x + dx;
                int z = cornerPoint.z + dz;
                
                processRoadEdge(pworld, x, cornerPoint.y, z);
            }
        }
    }
}

// 检查并填补道路边缘缺口
void EcosysUnitRoadBuild::processRoadEdge(World* pworld, int x, float targetY, int z) {
    // 检查X轴两侧是否都是道路方块
    bool hasRoad = checkAxisHasRoad(pworld, x, targetY, z, true);
    if (!hasRoad) {
        hasRoad = checkAxisHasRoad(pworld, x, targetY, z, false);
    }
    
    if (hasRoad) {
        pworld->setBlockAll(x, targetY, z, BLOCK_StoneStep, GetWayBlockData(BLOCK_StoneStep));
    }
}

// 检查指定轴向的两侧是否都是道路方块
bool EcosysUnitRoadBuild::checkAxisHasRoad(World* pworld, int x, float targetY, int z, bool isXAxis) {
    // 预先确定检查位置
    int x1 = isXAxis ? x - 1 : x;
    int x2 = isXAxis ? x + 1 : x;
    int z1 = isXAxis ? z : z - 1;
    int z2 = isXAxis ? z : z + 1;
    
    // 在两个相邻位置检查道路方块
    // 首先在同一高度检查，如果未找到，则向上下各检查一格
    int block1 = checkBlockWithHeight(pworld, x1, targetY, z1);
    int block2 = checkBlockWithHeight(pworld, x2, targetY, z2);
    
    // 两侧都需要是道路方块
    return isRoadBlock(block1) && isRoadBlock(block2);
}

// 辅助函数：在指定位置及其上下各一格检查方块
int EcosysUnitRoadBuild::checkBlockWithHeight(World* pworld, int x, float baseY, int z) {
    // 先检查指定高度
    int blockId = pworld->getBlockID(x, baseY, z);
    if (blockId != 0) return blockId;
    
    // 向上检查一格
    blockId = pworld->getBlockID(x, baseY + 1, z);
    if (blockId != 0) return blockId;
    
    // 向下检查一格
    return pworld->getBlockID(x, baseY - 1, z);
}

// 判断方块ID是否为道路方块
bool EcosysUnitRoadBuild::isRoadBlock(int blockId) {
    return blockId == BLOCK_StoneStep || 
           blockId == BLOCK_StoneStep_White || 
           blockId == BLOCK_SandstoneStep;
}

// 计算道路边界框的辅助函数
void EcosysUnitRoadBuild::calculateRoadBoundingBox(const WCoord& start, const WCoord& end, 
                                                  int& minX, int& maxX, int& minZ, int& maxZ) {
    if (start.x == end.x) {
        // 垂直道路段（X坐标相同）
        minX = start.x - ROAD_WIDTH;
        maxX = start.x + ROAD_WIDTH;

        if (start.z < end.z) {
            // 从南到北方向
            minZ = start.z;
            maxZ = end.z + ROAD_WIDTH;
        } else {
            // 从北到南方向
            minZ = end.z - ROAD_WIDTH;
            maxZ = start.z;
        }
    } else if (start.z == end.z) {
        // 水平道路段（Z坐标相同）
        minZ = start.z - ROAD_WIDTH;
        maxZ = start.z + ROAD_WIDTH;

        if (start.x < end.x) {
            // 从西到东方向
            minX = start.x;
            maxX = end.x + ROAD_WIDTH;
        } else {
            // 从东到西方向
            minX = end.x - ROAD_WIDTH;
            maxX = start.x;
        }
    } else {
        // 处理非正交道路段（虽然Manhattan路径不应该有这种情况）
        minX = std::min(start.x, end.x) - ROAD_WIDTH;
        maxX = std::max(start.x, end.x) + ROAD_WIDTH;
        minZ = std::min(start.z, end.z) - ROAD_WIDTH;
        maxZ = std::max(start.z, end.z) + ROAD_WIDTH;
    }
}

// 修改直线道路段生成，使用新的辅助函数
void EcosysUnitRoadBuild::generateStraightRoadSegment(World* pworld, const WCoord& start, const WCoord& end) {
    // 计算方向和距离
    float dx = end.x - start.x;
    float dz = end.z - start.z;
    float dist = sqrt(dx * dx + dz * dz);
    
    if (dist < 1.0f) return; // 距离太短不处理
    
    // 计算边界框
    int minX, maxX, minZ, maxZ;
    calculateRoadBoundingBox(start, end, minX, maxX, minZ, maxZ);
    
    // 使用投影参数作为缓存键
    std::map<float, int> heightCache;
    // 处理该区域内的每个方块
    for (int x = minX; x <= maxX; x++) {
        for (int z = minZ; z <= maxZ; z++) {
            // 计算点在线段上的投影参数t
            float projectionT = std::max(0.0f, std::min(1.0f, calculateProjection(x, z, start, end)));
            // 计算投影点的坐标
            float projX = start.x + projectionT * (end.x - start.x);
            float projZ = start.z + projectionT * (end.z - start.z);
            
            // 尝试从缓存中获取高度
            auto cacheIt = heightCache.find(projectionT);
            int roadHeight = 0;
            if (cacheIt != heightCache.end()) {
                // 缓存命中，直接使用缓存的高度值
                roadHeight = cacheIt->second;
            } else {
                // 缓存未命中，计算高度并存入缓存
                // 计算投影点的道路高度
                WCoord projPoint(projX, 0, projZ);
                roadHeight = calculateRoadHeight(pworld, projPoint, false);
                heightCache[projectionT] = roadHeight;
            }
            
            // // 严格限制在[0,1]范围内
            // if (projectionT < 0.0f || projectionT > 1.0f) {
            //     continue; // 跳过不在线段上投影的点
            // }
            
            // 计算点到线段的距离
            float distToLine = pointToLineDistance(x, z, start, end);
            
            // 只处理在道路宽度范围内的点
            if (distToLine <= ROAD_WIDTH) {
                // 插值计算高度
                float targetY = start.y + (end.y - start.y) * projectionT;
                
                // 获取地形高度并适当融合
                float blend = 0.0f; // 调整这个值来控制道路对地形的适应程度
                targetY = targetY * (1.0f - blend) + roadHeight * blend;
                
                // 放置道路方块
                placeRoadBlock(pworld, x, targetY, z, distToLine);
            }
        }
    }
}

// 计算点到直线的距离
float EcosysUnitRoadBuild::pointToLineDistance(int x, int z, const WCoord& lineStart, const WCoord& lineEnd) {
    float A = lineEnd.x - lineStart.x;
    float B = lineEnd.z - lineStart.z;
    float C = lineStart.x * lineEnd.z - lineEnd.x * lineStart.z;
    
    return abs(A * z - B * x + C) / sqrt(A * A + B * B);
}

// 计算点在线段上的投影参数t
float EcosysUnitRoadBuild::calculateProjection(int x, int z, const WCoord& lineStart, const WCoord& lineEnd) {
    float dx = lineEnd.x - lineStart.x;
    float dz = lineEnd.z - lineStart.z;
    float length2 = dx * dx + dz * dz;
    
    if (length2 == 0) return 0; // 避免除以零
    
    return ((x - lineStart.x) * dx + (z - lineStart.z) * dz) / length2;
}

// 在指定位置放置道路方块
void EcosysUnitRoadBuild::placeRoadBlock(World* pworld, int x, float targetY, int z, float distFromCenter, bool generateLine) {
    ChunkIndex chunkindex = ChunkIndex(BlockDivSection(x), BlockDivSection(z));
    
    // 检查区块是否已加载，如果未加载则先加载区块
    pworld->syncLoadChunk(chunkindex, GetIPlayerControl() ? GetIPlayerControl()->GetChunkViewerForCtrl() : nullptr);
    Chunk* chunk = pworld->getChunk(chunkindex);

    // 清理障碍物
    clearObstacles(pworld, x, targetY, z);

    // 填充道路下方的空洞
    pworld->FillBlockBelowByBiome(x, targetY, z);

    // 原始道路的放置逻辑
    if (distFromCenter <= ROAD_WIDTH) {
        // 内部区域
        if (distFromCenter > ROAD_INNER_WIDTH - ROAD_EDGE_WIDTH) {
            // 内部边缘的砖块
            pworld->setBlockAll(x, targetY, z, BLOCK_SandstoneStep, GetWayBlockData(BLOCK_SandstoneStep));
        } else {
            // 中心区域，使用标准道路砖
            int blockid = BLOCK_StoneStep;
            
            // 如果在中心线附近且允许生成标线，考虑绘制白色标线
            if (generateLine && distFromCenter < ROAD_LINE_WIDTH) {
                // 使用固定间隔创建标线图案
                // 计算在道路方向上的投影长度，考虑标线
                int roadPosition = std::abs(x + z); // 简单的位置计算方式
                const float totalLength = ROAD_LINE_LENGTH + ROAD_LINE_INTERVAL;
                float modPos = fmod(roadPosition, totalLength);
                
                if (modPos < ROAD_LINE_LENGTH) {
                    blockid = BLOCK_StoneStep_White; // 使用白色石英作为标线
                }
            }
            
            pworld->setBlockAll(x, targetY, z, blockid, GetWayBlockData(blockid));
        }
        
        // 处理道路边缘高度，确保平滑过渡
        processRoadEdge(pworld, x, targetY, z);
    }
}

// 生成轨道
void EcosysUnitRoadBuild::generateTrack(World* pworld, const WCoord& start, const WCoord& end) {
    if (!pworld) return;
    
    // 计算方向和距离
    float dx = end.x - start.x;
    float dz = end.z - start.z;
    float dist = sqrt(dx * dx + dz * dz);
    
    // 如果长度为0，直接返回
    if (dist < 0.1f) return;
    
    // 计算边界框
    int minX, maxX, minZ, maxZ;
    
    // 轨道总宽度调整为TRACK_WIDTH，适应双轨道
    if (start.x == end.x) {
        // 垂直轨道段（X坐标相同）
        minX = start.x - TRACK_WIDTH;  
        maxX = start.x + TRACK_WIDTH;  

        if (start.z < end.z) {
            // 从南到北方向
            minZ = start.z;            
            maxZ = end.z + TRACK_WIDTH; 
        } else {
            // 从北到南方向
            minZ = end.z - TRACK_WIDTH; 
            maxZ = start.z;            
        }
    } else if (start.z == end.z) {
        // 水平轨道段（Z坐标相同）
        minZ = start.z - TRACK_WIDTH;  
        maxZ = start.z + TRACK_WIDTH;  

        if (start.x < end.x) {
            // 从西到东方向
            minX = start.x - 1;            
            maxX = end.x + TRACK_WIDTH; 
        } else {
            // 从东到西方向
            minX = end.x - TRACK_WIDTH; 
            maxX = start.x;            
        }
    } else {
        // 处理非正交轨道段
        minX = std::min(start.x, end.x) - TRACK_WIDTH - 1; 
        maxX = std::max(start.x, end.x) + TRACK_WIDTH + 1; 
        minZ = std::min(start.z, end.z) - TRACK_WIDTH - 1; 
        maxZ = std::max(start.z, end.z) + TRACK_WIDTH + 1; 
    }
    
    // 使用投影参数作为缓存键
    std::map<float, int> heightCache;
    
    // 处理该区域内的每个方块
    for (int x = minX; x <= maxX; x++) {
        for (int z = minZ; z <= maxZ; z++) {
            // 计算点在线段上的投影参数t
            float projectionT = std::max(0.0f, std::min(1.0f, calculateProjection(x, z, start, end)));
            
            // 计算投影点的坐标
            float projX = start.x + projectionT * (end.x - start.x);
            float projZ = start.z + projectionT * (end.z - start.z);
            
            // 计算在轨道方向上的位置，用于确定是否放置枕木
            // 使用投影长度计算轨道位置，更准确
            float trackPosition = projectionT * dist;
            
            // 尝试从缓存中获取高度
            auto cacheIt = heightCache.find(projectionT);
            int trackHeight = 0;
            if (cacheIt != heightCache.end()) {
                // 缓存命中，直接使用缓存的高度值
                trackHeight = cacheIt->second;
            } else {
                // 缓存未命中，计算高度并存入缓存
                WCoord projPoint(projX, 0, projZ);
                trackHeight = calculateTrackHeight(pworld, projPoint);
                heightCache[projectionT] = trackHeight;
            }
            
            // 计算点到线段的距离
            float distToLine = pointToLineDistance(x, z, start, end);
            
            // 只处理在轨道宽度范围内的点
            if (distToLine <= TRACK_WIDTH) {
                // 放置轨道方块
                placeTrackBlock(pworld, x, trackHeight, z, distToLine, trackPosition);
            }
        }
    }
}

// 放置轨道方块
void EcosysUnitRoadBuild::placeTrackBlock(World* pworld, int x, float targetY, int z, float distFromCenter, float trackPosition) {
    if (!pworld) return;
    
    // 轨道方块 - 使用黑色石头或煤炭块模拟轨道
    int trackBlock = BLOCK_SandstoneStep; // 煤炭块，黑色
    int supportBlock = BLOCK_StoneStep; // 石砖楼梯，作为支撑
    int tieBlock = BLOCK_StoneStep_White; // 石英楼梯，作为横档
    
    // 检查是否在轨道宽度范围内
    if (distFromCenter <= TRACK_WIDTH) {
        // 双轨道设计
        // 计算到中心线的相对距离
        float distToCenterLine = abs(distFromCenter);
        
        // 只在精确的位置放置轨道，确保每条轨道只有一个方块宽
        // 左轨道位置 - 只在整数位置1放置
        if (round(distToCenterLine) == 1) {
            // 放置左轨道
            pworld->setBlockAll(x, targetY, z, trackBlock, GetWayBlockData(trackBlock));
        }
        
        // // 右轨道位置 - 只在整数位置2放置
        // if (round(distToCenterLine) == 2) {
        //     // 放置右轨道
        //     pworld->setBlockAll(x, targetY, z, trackBlock, GetWayBlockData(trackBlock));
        // }
        
        // 枕木（横档）
        if (fmod(trackPosition, TRACK_TIE_INTERVAL) < 1.0f) {
            // 在整个轨道宽度范围内放置枕木（连接两边的轨道）
            if (distToCenterLine <= 2.5f) { 
                // 在轨道下方放置支撑（枕木）
                pworld->setBlockAll(x, targetY - 1, z, supportBlock, GetWayBlockData(supportBlock));
            }
        }
    }
}


// 检查是否为轨道方块
bool EcosysUnitRoadBuild::isTrackBlock(int blockId) {
    return blockId == BLOCK_StoneStep ||
           blockId == BLOCK_SandstoneStep ||
           blockId == BLOCK_StoneStep_White;
}

// 计算轨道高度
int EcosysUnitRoadBuild::calculateTrackHeight(World* pworld, const WCoord& pos) {
    // 使用现有的道路高度计算函数，但可以根据需要调整
    // 轨道可能需要更高的高度以适应支撑结构
    return calculateRoadHeight(pworld, pos, true) + 2; // 比普通道路高2个单位
}

// 添加道路节点，随机决定是道路还是轨道
void EcosysUnitRoadBuild::addRoadNodes(const WCoord& start, const WCoord& end) {
    // 检查是否已存在这条道路
    for (const auto& node : m_RoadNodes) {
        if ((node.start == start && node.end == end) ||
            (node.start == end && node.end == start)) {
            return;  // 道路已存在，不重复添加
        }
    }
    
    // 随机决定道路类型
    ChunkRandGen randGen;
    randGen.setSeed64((start.x << 16) | (start.z & 0xFFFF));  // 使用起点坐标作为种子
    
    // 75%的概率为普通道路，25%的概率为轨道
    RoadType roadType = ROAD_TYPE_NORMAL; // (randGen.nextInt(100) < 10) ? ROAD_TYPE_NORMAL : ROAD_TYPE_TRACK;
    // 记录道路类型信息
    if (roadType == ROAD_TYPE_NORMAL) {
        GEN_LOG_INFO("RoadBuild: Adding normal road node: from(%d,%d,%d) to(%d,%d,%d)", 
                 start.x, start.y, start.z, 
                 end.x, end.y, end.z);
    } else {
        GEN_LOG_INFO("RoadBuild: Adding rail node: from(%d,%d,%d) to(%d,%d,%d)", 
                 start.x, start.y, start.z, 
                 end.x, end.y, end.z);
    }
    
    // 添加新的道路节点，但不立即生成
    RoadNode newNode(start, end, false, roadType);
    m_RoadNodes.push_back(newNode);
}

void EcosysUnitRoadBuild::generateZShapedRoad(World* pworld, const WCoord& start, const WCoord& end, RoadType roadType) {
    if (!pworld) return;
    
    // 不再需要从m_RoadNodes中查询道路类型，直接使用传入的参数
    
    // 计算Z形路径的中间点
    // Z形路径由三段组成：从起点到中间点1，从中间点1到中间点2，从中间点2到终点
    WCoord middlePoint1, middlePoint2;
    
    // 计算起点到终点的距离在x和z方向上的差值
    int dx = end.x - start.x;
    int dz = end.z - start.z;
    
    // 根据起点和终点的相对位置计算中间点
    if (abs(dx) > abs(dz)) {
        // 如果x方向的距离更大，我们在x方向上移动2/3，在z方向上不变
        middlePoint1 = WCoord(start.x + dx * 2 / 3, 0, start.z);
        // 然后从中间点1到达与终点相同的z坐标
        middlePoint2 = WCoord(middlePoint1.x, 0, end.z);
    } else {
        // 如果z方向的距离更大，我们在z方向上移动2/3，在x方向上不变
        middlePoint1 = WCoord(start.x, 0, start.z + dz * 2 / 3);
        // 然后从中间点1到达与终点相同的x坐标
        middlePoint2 = WCoord(end.x, 0, middlePoint1.z);
    }
    
    // 计算适当的y坐标（高度）
    middlePoint1.y = calculateRoadHeight(pworld, middlePoint1);
    middlePoint2.y = calculateRoadHeight(pworld, middlePoint2);
    
    // 查找对应的RoadNode并保存路径段信息
    for (auto& node : m_RoadNodes) {
        if ((node.start == start && node.end == end) || 
            (node.start == end && node.end == start)) {
            // 清空之前的路径段信息
            node.pathSegments.clear();
            // 添加新的路径段
            node.pathSegments.push_back({start, middlePoint1});
            node.pathSegments.push_back({middlePoint1, middlePoint2});
            node.pathSegments.push_back({middlePoint2, end});
            break;
        }
    }
    
    // 根据道路类型生成道路段
    if (roadType == ROAD_TYPE_TRACK) {
        // 生成轨道段
        generateTrack(pworld, start, middlePoint1);
        generateTrack(pworld, middlePoint1, middlePoint2);
        generateTrack(pworld, middlePoint2, end);
    } else {
        // 生成普通道路段
        generateStraightRoadSegment(pworld, start, middlePoint1);
        generateStraightRoadSegment(pworld, middlePoint1, middlePoint2);
        generateStraightRoadSegment(pworld, middlePoint2, end);
        
        // 在转角处生成道路拐角
        generateRoadCorner(pworld, middlePoint1);
        generateRoadCorner(pworld, middlePoint2);
    }
}

// 添加新函数：曼哈顿型(L型)轨道生成
void EcosysUnitRoadBuild::generateManhattanTrack(World* pworld, const WCoord& start, const WCoord& end) {
    if (!pworld) return;
    
    // L形路径，中间点采用相同的逻辑
    WCoord midPoint(end.x, (start.y + end.y) / 2, start.z);
    midPoint.y = calculateTrackHeight(pworld, midPoint);
    
    // 先生成水平段轨道
    generateTrack(pworld, start, midPoint);

    // 再生成垂直段轨道
    generateTrack(pworld, end, midPoint);
    
    // 处理转角处的轨道连接 - 在拐角处放置特殊的转角轨道方块
    // 清理转角位置的障碍物
    clearObstacles(pworld, midPoint.x, midPoint.y, midPoint.z);
    
    // 获取转角处所在的区块
    ChunkIndex cornerChunkIndex = ChunkIndex(BlockDivSection(midPoint.x), BlockDivSection(midPoint.z));
    
    // 检查区块是否已加载，如果未加载则先加载区块
    pworld->syncLoadChunk(cornerChunkIndex, GetIPlayerControl() ? GetIPlayerControl()->GetChunkViewerForCtrl() : nullptr);
    Chunk* cornerChunk = pworld->getChunk(cornerChunkIndex);

    // 对转角处的轨道进行特殊处理，确保转角处的轨道正确连接
    int cornerBlock = BLOCK_SandstoneStep; // 使用相同的轨道方块
    pworld->setBlockAll(midPoint.x, midPoint.y, midPoint.z, cornerBlock, GetWayBlockData(cornerBlock));
    
    // 根据需要在转角下方放置支撑方块
    int supportBlock = BLOCK_StoneStep;
    pworld->setBlockAll(midPoint.x, midPoint.y - 1, midPoint.z, supportBlock, GetWayBlockData(supportBlock));
}

/**
 * 在所有道路两侧生成适合放置建筑的点
 * @param world 世界指针
 * @param minDistance 建筑之间的最小距离
 * @param buildingSideOffset 建筑距离道路的侧向偏移距离
 * @param totalCount 需要生成的建筑点总数
 * @return 返回可放置建筑的点列表，仅包含世界坐标
 */
std::vector<WCoord> EcosysUnitRoadBuild::generateBuildingPointsAlongRoads(
    World* world,
    float minDistance,
    float buildingSideOffset,
    int totalCount)

{
    std::vector<WCoord> buildingPoints;
    if (!world || m_RoadNodes.empty()) return buildingPoints;
    
    // 已生成建筑点的列表，用于检查距离
    std::vector<WCoord> existingPoints;
    
    // 记录生成的点数
    int generatedCount = 0;
    
    // 按道路长度排序，优先处理较长的道路
    std::vector<std::pair<size_t, float>> roadLengths;
    for (size_t i = 0; i < m_RoadNodes.size(); ++i) {
        const auto& node = m_RoadNodes[i];
        float dx = node.end.x - node.start.x;
        float dz = node.end.z - node.start.z;
        float length = sqrt(dx * dx + dz * dz);
        roadLengths.push_back({i, length});
    }
    
    // 按道路长度从长到短排序
    std::sort(roadLengths.begin(), roadLengths.end(), 
        [](const auto& a, const auto& b) { return a.second > b.second; });
    
    // 全局随机数生成器，用于初始化种子
    ChunkRandGen globalRandGen;
    // 获取世界种子
    unsigned int seed1, seed2;
    world->getRandomSeedRaw(seed1, seed2);
    globalRandGen.setSeed64(((WORLD_SEED)seed1 << 32) | seed2);
    
    GEN_LOG_INFO("EcosysUnitRoadBuild generateBuildingPointsAlongRoads start roadLengths size:%d roadNodes size:%d", roadLengths.size(), m_RoadNodes.size());
    // 遍历排序后的道路节点
    for (const auto& roadPair : roadLengths) {
        if (generatedCount >= totalCount) break;

        const auto& roadNode = m_RoadNodes[roadPair.first];
        
        // 根据道路起点和终点创建随机数生成器，确保同一段道路的随机性是稳定的
        ChunkRandGen roadRandGen;
        roadRandGen.setSeed64((roadNode.start.x << 32) | (roadNode.start.z << 16) | (roadNode.end.x + roadNode.end.z));
        
        // 获取道路段列表
        std::vector<std::pair<WCoord, WCoord>> roadSegments;
        
        // 使用预先计算好的路径段，而不是重新计算
        if (!roadNode.pathSegments.empty()) {
            // 使用保存的路径段
            roadSegments = roadNode.pathSegments;
        } else {
            // 如果没有保存的路径段，使用起点和终点创建一个直线段
            roadSegments.push_back({roadNode.start, roadNode.end});
        }

        int smallBuildMaxSize = GetCityConfigInterface()->GetSOCSmallBuildMaxSize();
        int smallBuildChunkSize = ceil(smallBuildMaxSize / SECTION_BLOCK_DIM);

        GEN_LOG_INFO("EcosysUnitRoadBuild generateBuildingPointsAlongRoads roadSegments size:%d roadNodes:%d, %d roadend:%d, %d", roadSegments.size(), roadNode.start.x, roadNode.start.z, roadNode.end.x, roadNode.end.z);
        // 在道路两侧生成建筑点
        for (const auto& p: roadSegments) {
            // 计算路段长度
            float segDx = p.second.x - p.first.x;
            float segDz = p.second.z - p.first.z;
            float segLength = sqrt(segDx * segDx + segDz * segDz);

            // 路段方向矢量
            float dirX = segDx / segLength;
            float dirZ = segDz / segLength;
            dirX = dirX == 0 ? 1 : dirX;
            dirZ = dirZ == 0 ? 1 : dirZ;
            
            // 计算垂直于道路方向的向量
            float perpX = -dirZ;
            float perpZ = dirX;
            
            // 根据段长确定在该段上生成的建筑点个数
            int pointsPerSegment = std::max(1, static_cast<int>(segLength / 40.0f));
            
            // 为每个路段创建独立的随机数生成器
            ChunkRandGen segRandGen;
            segRandGen.setSeed64((p.first.x << 32) | (p.first.z << 16) | (p.second.x + p.second.z));
            
            // 估计建筑的平均尺寸
            // 这里假设建筑平均为14x14方块
            const float avgBuildingSizeX = 14.0f;
            const float avgBuildingSizeZ = 14.0f;
            
            for (int i = 0; i < pointsPerSegment; ++i) {
                if (generatedCount >= totalCount) break;
                
                // 为每个点创建随机生成器，使用路段坐标和索引作为种子
                ChunkRandGen pointRandGen;
                pointRandGen.setSeed64((p.first.x << 24) | (p.first.z << 16) | (p.second.x << 8) | p.second.z | (i << 4));
                
                // 在路段上均匀分布点，并添加一些随机偏移
                float posVariation = (pointRandGen.getFloat() * 0.2f) - 0.1f; // -0.1到0.1之间的随机值
                float tRandom = (i + 0.5f + posVariation) / pointsPerSegment;
                tRandom = std::max(0.05f, std::min(0.95f, tRandom)); // 确保不要太靠近路段的端点
                
                // 在路段上的位置
                float posX = p.first.x + tRandom * segDx;
                float posZ = p.first.z + tRandom * segDz;
                
                // 在道路两侧生成点
                for (int side = -1; side <= 1; side += 2) {
                    if (generatedCount >= totalCount) break;

                    // 为每个点生成随机偏移量 (0.8到1.5之间)
                    float offsetVariationValue = 0.8f + pointRandGen.getFloat() * 0.7f;
                    float currentSideOffset = buildingSideOffset * offsetVariationValue;
                    
                    // 计算建筑点的基础位置（在道路两侧，垂直于道路方向）
                    float baseBuildingX = posX + side * perpX * currentSideOffset;
                    float baseBuildingZ = posZ + side * perpZ * currentSideOffset;
                    
                    // 为了避免建筑与道路重叠，需要根据建筑物大小和道路方向计算额外偏移
                    // 当perpX < 0时，建筑向-x方向延伸，需要向-x方向额外偏移
                    // 当perpZ < 0时，建筑向-z方向延伸，需要向-z方向额外偏移
                    float adjustedBuildingX = baseBuildingX;
                    float adjustedBuildingZ = baseBuildingZ;
                    
                    // 如果道路垂直方向指向-x，则需要在-x方向额外偏移建筑尺寸
                    if (perpX * side < 0) {
                        adjustedBuildingX -= avgBuildingSizeX;
                    }
                    
                    // 如果道路垂直方向指向-z，则需要在-z方向额外偏移建筑尺寸
                    if (perpZ * side < 0) {
                        adjustedBuildingZ -= avgBuildingSizeZ;
                    }

                    ChunkIndex chunkStart = ChunkIndex(BlockDivSection(adjustedBuildingX), BlockDivSection(adjustedBuildingZ));
                    ChunkIndex chunkEnd = ChunkIndex(BlockDivSection(adjustedBuildingX + smallBuildMaxSize), BlockDivSection(adjustedBuildingZ + smallBuildMaxSize));
                    for (int cx = chunkStart.x; cx <= chunkEnd.x; cx++)
                    {
	                    for (int cz = chunkStart.z; cz <= chunkEnd.z; cz++)
	                    {
                            world->syncLoadChunk(ChunkIndex(cx, cz));
	                    }
                    }
                    bool isChunkNull = false;
                	for (int cx = chunkStart.x; cx <= chunkEnd.x; cx++)
                    {
                        for (int cz = chunkStart.z; cz <= chunkEnd.z; cz++)
                        {
                            Chunk* chunk = world->getChunk(ChunkIndex(cx, cz));
                            if (!chunk) {
                                GEN_LOG_INFO("EcosysUnitRoadBuild generateBuildingPointsAlongRoads chunk is null");
                                isChunkNull = true;
                                break;
                            }
                        }
                    }
                    if (isChunkNull) continue;

                    bool isBuildInRoad = false;
                    bool isNotBuildBiome = false;
                    int maxPosx = adjustedBuildingX + smallBuildMaxSize;
                    int maxPosz = adjustedBuildingZ + smallBuildMaxSize;
                    for (int posx = adjustedBuildingX; posx <= maxPosx; posx++)
                    {
	                    for (int posz = adjustedBuildingZ; posz <= maxPosz; posz++)
	                    {
                            auto biomeId = world->getBiomeId(posx, posz);
                            if (biomeId != BIOME_PLAINS && biomeId != BIOME_DESERT && biomeId != BIOME_ICE_PLAINS)
                            {
                                isNotBuildBiome = true;
                                break;
                            }
                            int buildingY = world->getTopHeight(adjustedBuildingX, adjustedBuildingZ);
                            auto blockid = world->getBlockID(WCoord(posx, buildingY, posz));
                            if (isRoadBlock(blockid))
                            {
                                isBuildInRoad = true;
                                break;
                            }
	                    }
                    }
                    if (isBuildInRoad || isNotBuildBiome) continue;
                    // 获取区块随机生成器，获取该建筑位置对应的区块随机值
                    //ChunkIndex chunkIdx = ChunkIndex(BlockDivSection(adjustedBuildingX), BlockDivSection(adjustedBuildingZ));
                    //world->syncLoadChunk(chunkIdx, GetIPlayerControl() ? GetIPlayerControl()->GetChunkViewerForCtrl() : nullptr);
                    //Chunk* chunk = world->getChunk(chunkIdx);
                    //if (!chunk) {
                    //    GEN_LOG_INFO("EcosysUnitRoadBuild generateBuildingPointsAlongRoads chunk is null");
                    //    continue;
                    //}
                    ChunkRandGen buildingRandGen;
                    buildingRandGen.setSeed64(world->getChunkSeed(chunkStart.x, chunkStart.z));
                    
                    // 根据世界坐标获取高度
                    int buildingY = world->getTopHeight(adjustedBuildingX, adjustedBuildingZ);
                    
                    // 创建建筑点
                    WCoord buildingPoint(adjustedBuildingX, buildingY, adjustedBuildingZ);
                    GEN_LOG_INFO("EcosysUnitRoadBuild generateBuildingPointsAlongRoads buildingPoint:%d, %d, %d", buildingPoint.x, buildingPoint.y, buildingPoint.z);
                    // 检查与现有点的距离
                    bool tooClose = false;
                    for (const auto& existing : existingPoints) {
                        // 在水平面上计算距离，忽略高度差异
                        float dx = existing.x - buildingPoint.x;
                        float dz = existing.z - buildingPoint.z;
                        float distSq = dx * dx + dz * dz;
                        
                        if (distSq < minDistance * minDistance) {
                            tooClose = true;
                            GEN_LOG_INFO("EcosysUnitRoadBuild generateBuildingPointsAlongRoads tooClose buildingPoint:%d, %d, %d existPoint:%d, %d, %d dist:%f", buildingPoint.x, buildingPoint.y, buildingPoint.z, existing.x, existing.y, existing.z, sqrt(distSq));
                            break;
                        }
                    }
                    
                    // 检查与所有道路的距离，确保建筑点不与其他道路相交
                    if (!tooClose) {
                        // 计算建筑的边界框（近似为矩形）
                        float buildingMinX = buildingPoint.x - avgBuildingSizeX/2;
                        float buildingMaxX = buildingPoint.x + avgBuildingSizeX/2;
                        float buildingMinZ = buildingPoint.z - avgBuildingSizeZ/2;
                        float buildingMaxZ = buildingPoint.z + avgBuildingSizeZ/2;
                        
                        // 检查与所有道路的交叉情况
                        for (const auto& otherRoad : m_RoadNodes) {
                            // 获取其他道路的路段
                            const auto& otherSegments = otherRoad.pathSegments.empty() ? 
                                std::vector<std::pair<WCoord, WCoord>>{{otherRoad.start, otherRoad.end}} : 
                                otherRoad.pathSegments;
                            
                            for (const auto& otherSeg : otherSegments) {
                                // 计算道路安全距离（道路宽度 + 一些安全边距）
                                const float roadSafetyDistance = 6.0f; // 道路宽度加上安全边距
                                
                                // 检查建筑与道路的最小距离
                                float minDistToRoad = std::numeric_limits<float>::max();
                                // 建筑四个角的坐标
                                WCoord corners[4] = {
                                    WCoord(buildingMinX, buildingY, buildingMinZ),
                                    WCoord(buildingMaxX, buildingY, buildingMinZ),
                                    WCoord(buildingMaxX, buildingY, buildingMaxZ),
                                    WCoord(buildingMinX, buildingY, buildingMaxZ)
                                };
                                
                                // 检查建筑四个角与道路的距离
                                for (const auto& corner : corners) {
                                    // 检查是否在保护区内
                                    if (world->getCityMgr()->isInProtectedArea(corner))
                                    {
                                        tooClose = true;
                                        GEN_LOG_INFO("EcosysUnitRoadBuild generateBuildingPointsAlongRoads tooClose buildingPoint:%d, %d, %d corner:%d, %d, %d", buildingPoint.x, buildingPoint.y, buildingPoint.z, corner.x, corner.y, corner.z);
                                        break;
                                    }

                                    float distToRoad = pointToLineDistance(corner.x, corner.z, otherSeg.first, otherSeg.second);
                                    minDistToRoad = std::min(minDistToRoad, distToRoad);  
                                }
                                
                                // 如果任何一个角太靠近道路，则认为建筑与道路相交
                                if (minDistToRoad < roadSafetyDistance) {
                                    tooClose = true;
                                    GEN_LOG_INFO("EcosysUnitRoadBuild generateBuildingPointsAlongRoads tooClose buildingPoint:%d, %d, %d otherSeg:%d, %d, %d, %d dist:%f", buildingPoint.x, buildingPoint.y, buildingPoint.z, otherSeg.first.x, otherSeg.first.y, otherSeg.first.z, otherSeg.second.x, otherSeg.second.y, otherSeg.second.z, minDistToRoad);
                                    break;
                                }
                            }
                            
                            if (tooClose) break;
                        }
                    }
                    
                    // 距离检查通过，添加到结果列表
                    if (!tooClose) {
                        buildingPoints.push_back(buildingPoint);
                        existingPoints.push_back(buildingPoint);
                        generatedCount++;
                    }
                }
            }
        }
    }
    
    GEN_LOG_INFO("RoadBuild: Generated %d building points along roads", buildingPoints.size());
    return buildingPoints;
}