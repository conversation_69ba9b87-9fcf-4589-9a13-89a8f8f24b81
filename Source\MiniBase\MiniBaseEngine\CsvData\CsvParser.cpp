#include <vector>
#include <assert.h>
#include "CsvParser.h"
//#include "File/FileManager.h"
#include "File/FileManager.h"
#include "Common/OgreStringUtil.h"
#include "File/FileUtilities.h"
#include "Utilities/Logs/LogAssert.h"

using namespace MINIW;
using namespace Rainbow;
#define assertion(condition, format, ...) \
	assert(condition)

//----------------------------------------------------------------------------
CSVParser::TableItem::TableItem(const char* szItemString)
{
	mItemString = szItemString;
}
//----------------------------------------------------------------------------	
CSVParser::TableLine::TableLine() 
{
}
//----------------------------------------------------------------------------
CSVParser::CSVParser()
{
	mSeparator = ',';
	mContent = 0;
	mItems = 0;
	mLines = 0;
	mInvalidLine.SetLine(this, -1);
	m_IgnoreQuotationMark = false;
}
//----------------------------------------------------------------------------
CSVParser::~CSVParser()
{
	Clear();
}
//----------------------------------------------------------------------------
bool CSVParser::Load(const std::string &filename, bool is_utf8 )
{
	Clear();

	mFilename = filename;
	AutoRefPtr<DataStream> fp= GetFileManager().OpenFile(filename.c_str(), true, Rainbow::FileOpType::kFileOpAll);
	if(fp)
	{
		size_t len = fp->Size();

		mContent = new char[len+1];
		fp->Read(mContent, len);
		mContent[len] = 0;

#if PLATFORM_WIN
		if (is_utf8 == true) {
			//no need change
		}
		else
		{
			wchar_t *pwchar = new wchar_t[len + 2];
			StringUtil::AnsiToUnicode(pwchar, len + 1, mContent);
			char *putf8 = new char[len * 4 + 1];
			StringUtil::UnicodeToUTF8(putf8, len * 4, pwchar);

			len = strlen(putf8);
			delete[] mContent;
			mContent = new char[len + 1];
			memcpy(mContent, putf8, len + 1);

			delete[] pwchar;
			delete[] putf8;
		}
#endif

	}
	else{return false;}

	if (!ParseTextTable())
	{
		Clear();
		return false;
	}

	mItems = new char*[mNumLines*mNumCols];
	memset(mItems, 0, sizeof(char*)*mNumLines*mNumCols);

	mLines = new TableLine[mNumLines];
	for (int i=0; i<mNumLines; ++i)
	{
		mLines[i].SetLine(this, i);
	}

	ParseTextTable();

	return true;
}

bool CSVParser::LoadBuffer(char* buffer, int len, bool is_utf8)
{
	Clear();

	mContent = new char[len + 1];
	memcpy(mContent, buffer, len);
	mContent[len] = 0;

	//下完全同Load宏定义开始部分
#if PLATFORM_WIN
	if (is_utf8 == true) {
		//no need change
	}
	else
	{
		wchar_t *pwchar = new wchar_t[len + 2];
		StringUtil::AnsiToUnicode(pwchar, len + 1, mContent);
		char *putf8 = new char[len * 4 + 1];
		StringUtil::UnicodeToUTF8(putf8, len * 4, pwchar);

		len = strlen(putf8);
		delete[] mContent;
		mContent = new char[len + 1];
		memcpy(mContent, putf8, len + 1);

		delete[] pwchar;
		delete[] putf8;
	}
#endif

	if (!ParseTextTable())
	{
		Clear();
		return false;
	}

	mItems = new char*[mNumLines*mNumCols];
	memset(mItems, 0, sizeof(char*)*mNumLines*mNumCols);

	mLines = new TableLine[mNumLines];
	for (int i = 0; i < mNumLines; ++i)
	{
		mLines[i].SetLine(this, i);
	}

	ParseTextTable();

	return true;
}
//----------------------------------------------------------------------------
void CSVParser::Clear()
{
	mTitleLine = 0;
	mTitleCol = 0;
	mNumCols = 0;
	mNumLines = 0;
	if(mContent)
	{
		delete[] mContent;
		mContent = 0;
		delete[] mItems;
		mItems = 0;
		delete[] mLines;
		mLines = 0;
	}
}
//----------------------------------------------------------------------------
bool CSVParser::ParseTextTable()
{
	assertion(0!=mContent, "mContent must not be 0.\n");

	int state = 0;
	int line = 0;
	int col = 0;

	char *pWord = mContent;
	char *pCur = mContent;
	char *pd = mContent;

	while (*pCur)
	{
		if (state==0)
		{
			if (*pCur=='"' && !m_IgnoreQuotationMark)
			{
				state = 1;
			}
			else if (*pCur==mSeparator)
			{
				if (mItems)
				{
					*pd = 0;
					assertion(line<mNumLines && col<mNumCols, "must in right range.\n");
					if (line >= mNumLines || col >= mNumCols)
						LOG_INFO("CsvParser::ParseTextTable error: line=%d, numlines=%d, col=%d, numcols=%d, filename=%s", line, mNumLines, col, mNumCols, mFilename.c_str());
					mItems[line*mNumCols + col] = pWord;
				}
				col ++;
				pd = pWord = pCur + 1;
				if (mItems==0)
				{
					if (mNumCols<col) mNumCols=col;
				}
			}
			else if (*pCur==0x0A || *pCur==0x0D)
			{
				if (pCur[1]==0x0A || pCur[1]==0x0D)
				{
					pCur ++;
				}
				if (mItems)
				{
					*pd = 0;
					assertion(line<mNumLines && col<mNumCols, "line and col should be in right range.\n");
					if (line>=mNumLines || col >= mNumCols)
						LOG_INFO("CsvParser::ParseTextTable error: line=%d, numlines=%d, col=%d, numcols=%d, filename=%s", line, mNumLines, col, mNumCols, mFilename.c_str());
					mItems[line*mNumCols + col] = pWord;
				}
				col ++;
				if (mItems==0)
				{
					if (mNumCols<col) mNumCols=col;
				}
				line ++;
				col = 0;
				pd = pWord = pCur + 1;
			}
			else
			{
				if (mItems)
				{
					if (pCur!=pd) *pd = *pCur;
					pd ++;
				}
			}
		}
		else if (state==1 && !m_IgnoreQuotationMark)
		{
			if (*pCur=='"')
			{
				if (pCur[1]=='"')
				{
					// 还是双引号
					pCur ++;
					if (mItems)
					{
						if (pCur!=pd) *pd = *pCur;
						pd ++;
					}
				}else
				{
					// 结束
					state = 0;
				}
			}
			else
			{
				if (mItems)
				{
					if (pCur!=pd) *pd = *pCur;
					pd ++;
				}
			}
		}
		pCur ++;
	}
	if (pWord!=pCur)
	{
		if (mItems)
		{
			*pd = 0;
			assertion(line<mNumLines && col<mNumCols, 
				"line must in right range.\n");
			mItems[line*mNumCols + col] = pWord;
		}
		col ++;
		if (mItems==0)
		{
			if (mNumCols<col) mNumCols=col;
		}
		line ++;
	}

	mNumLines = line;
	return true;
}
//----------------------------------------------------------------------------
bool CSVParser::FindPosByString(const char* str, int& line, int& col)
{
	char* p = 0;
	for (int i=0; i<mNumLines; i++)
	{
		for (int j=0; j<mNumCols; j++)
		{
			p = mItems[i*mNumCols+j];
			if (p)
			{
				if (strcmp(p, str)==0)
				{
					line = (int)i;
					col = (int)j;
					return true;
				}
			}
		}
	}
	return false;
}
//----------------------------------------------------------------------------
int CSVParser::FindLineByString(const char* str)
{
	int line, col;
	if (FindPosByString(str, line, col))
	{
		return line;
	}
	return -1;
}
//----------------------------------------------------------------------------
int CSVParser::FindColByString(const char* str)
{
	int line, col;
	if (FindPosByString(str, line, col))
	{
		return col;
	}
	return -1;
}
//----------------------------------------------------------------------------
const CSVParser::TableLine& CSVParser::operator[](const char* szIdx) const
{
	for (int i=0; i<mNumLines; i++)
	{
		if (strcmp(mItems[i*mNumCols+mTitleCol], szIdx)==0)
		{
			return mLines[i];
		}
	}
	return mInvalidLine;
}
//----------------------------------------------------------------------------
bool CSVParser::HasColumn(const char *szColIdx) const
{
	/*
	for (int i=0; i<mNumCols; i++)
	{
		if (strcmp(mItems[mTitleLine*mNumCols+i], szColIdx)==0)
		{
			return true;
		}
	}
	*/
	//优化判断效率
	if (-1 != mTableTitle.index(szColIdx))
	{
		return true;
	}
	return false;
}
//----------------------------------------------------------------------------
//L:获取字段名列号
int   CSVParser::GetColumn(const char* szColIdx) const
{
    //标题越界
    if (mTitleLine > mNumLines)
        return -1;

    //空列
    if (NULL == szColIdx || strlen(szColIdx) <= 0)
        return -1;
	/*
    for (int i = 0; i < mNumCols; i++)
    {
        if (strcmp(mItems[mTitleLine*mNumCols + i], szColIdx) == 0)
        {
            return i;
        }
    }
    return -1;
	*/
	//优化遍历效率
	return mTableTitle.index(szColIdx);
}

//----------------------------------------------------------------------------
const char* CSVParser::GetString(int line, const char* szColIdx) const
{
	char* pStr = 0;
	static char szNull[] = "";

	if (line < 0) 
		return szNull;
	/*
	for (int i=0; i<mNumCols; i++)
	{
		if (strcmp(mItems[mTitleLine*mNumCols+i], szColIdx)==0)
		{
			pStr = mItems[line*mNumCols+i];

			if (pStr==0)
				pStr = szNull;

			return pStr;
		}
	}
	*/

	//优化内容读取效率
	int i = mTableTitle.index(szColIdx);
	if (i != -1) {
		pStr = mItems[line * mNumCols + i];
		if (pStr == 0)
			pStr = szNull;
		return pStr;
	}

//	assertion(false, "GetString error.\n");

	return szNull;
}
//----------------------------------------------------------------------------
const char* CSVParser::GetString(const char* szLineIdx, const char* szColIdx)
	const
{
	return (*this)[szLineIdx][szColIdx];
}
//----------------------------------------------------------------------------
const char* CSVParser::GetString (int line, int col) const
{
	if (line<0 || line>=mNumLines || col<0 || col>=mNumCols) return 0;
	if (mItems[line*mNumCols+col]==0) return "";
	return (const char*)mItems[line*mNumCols+col];
}
//----------------------------------------------------------------------------
bool CSVParser::Char (size_t line, size_t col, char &val) const
{
	if (mItems[line*mNumCols+col]==0) return false;
	val = (char)atoi(mItems[line*mNumCols+col]);
	return true;
}
//----------------------------------------------------------------------------
bool CSVParser::Byte (size_t line, size_t col, unsigned char &val) const
{
	if (mItems[line*mNumCols+col]==0) return false;
	val = (unsigned char)atoi(mItems[line*mNumCols+col]);
	return true;
}
//----------------------------------------------------------------------------
bool CSVParser::Short(size_t line, size_t col, short &val) const
{
	if (mItems[line*mNumCols+col]==0) return false;
	val = (short)atoi(mItems[line*mNumCols+col]);
	return true;
}
//----------------------------------------------------------------------------
bool CSVParser::Word (size_t line, size_t col, unsigned short&val) const
{
	if (mItems[line*mNumCols+col]==0) return false;
	val = (unsigned short)atoi(mItems[line*mNumCols+col]);
	return true;
}
//----------------------------------------------------------------------------
bool CSVParser::Int  (size_t line, size_t col, int &val) const
{
	if (mItems[line*mNumCols+col]==0) return false;
	val = (int)atoi(mItems[line*mNumCols+col]);
	return true;
}
//----------------------------------------------------------------------------
bool CSVParser::UInt (size_t line, size_t col, unsigned int &val) const
{
	if (mItems[line*mNumCols+col]==0) return false;
	val = (unsigned int)atoi(mItems[line*mNumCols+col]);
	return true;
}
//----------------------------------------------------------------------------
bool CSVParser::Long (size_t line, size_t col, long &val) const
{
	if (mItems[line*mNumCols+col]==0) return false;
	val = (long)atol(mItems[line*mNumCols+col]);
	return true;
}
//----------------------------------------------------------------------------
bool CSVParser::DWord(size_t line, size_t col, unsigned long &val) const
{
	if (mItems[line*mNumCols+col]==0) return false;
	val = (unsigned long)atol(mItems[line*mNumCols+col]);
	return true;
}
//----------------------------------------------------------------------------
bool CSVParser::Float (size_t line, size_t col, float &val) const
{
	if (mItems[line*mNumCols+col]==0) return false;
	val = (float)atof(mItems[line*mNumCols+col]);
	return true;
}
//----------------------------------------------------------------------------
bool CSVParser::Double(size_t line, size_t col, double &val) const
{
	if (mItems[line*mNumCols+col]==0) return false;
	val = atof(mItems[line*mNumCols+col]);
	return true;
}

void CSVParser::SetIgnoreQuotationMark(bool bingnore)
{
	m_IgnoreQuotationMark = bingnore;
}
//----------------------------------------------------------------------------