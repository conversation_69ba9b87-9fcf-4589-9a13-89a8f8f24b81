#include "container_sandboxGame.h"
#include "LuaInterfaceProxy.h"
#include "SandboxIdDef.h"
#include "world.h"
#include <WorldManager.h>
#include "BlockScene.h"
#include <BlockMaterialMgr.h>
#include "TerritoryManager.h"
#include "container_territory.h"
#include "DefManagerProxy.h"
#include "ClientPlayer.h"
#include "backpack.h"
#include "ClientPlayer.h"


const int CollectWaterTickToSecond = 5;
//-----------------------------------------------------containerWaterStorage--------------------------------------------------------------------------
/**********************************************************************************************
类    名：containerWaterStorage
功    能：蓄水方块container
********************************************************************************************* */
containerWaterStorage::containerWaterStorage()
{
}

containerWaterStorage::containerWaterStorage(const WCoord& blockpos, int blockId) :ErosionContainer(blockpos, blockId)
{
}

void containerWaterStorage::initCollectData(World* pworld)
{
	auto biome = pworld->getBiome(m_BlockPos.x, m_BlockPos.z);
	if (biome)
	{
		m_nOnceTickMax = biome->WaterStorageCollectOnceTime * CollectWaterTickToSecond;
		m_nOnceVolume = biome->WaterStorageCollectOnceVolume;
	}
	auto blockid = pworld->getBlockID(m_BlockPos);
	auto pitemdef = GetDefManagerProxy()->getItemDef(blockid);
	if (pitemdef)
	{
		m_nWaterVolumeMax = atoi(pitemdef->para.c_str());
	}
}

bool containerWaterStorage::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerWaterStorage*>(srcdata);
	loadErosionContainer(src->basedata());

	m_nCurWaterVolume = src->watervolume();

	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> containerWaterStorage::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerErosion(builder);

	auto actor = FBSave::CreateContainerWaterStorage(builder, basedata, m_nCurWaterVolume);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerWaterStorage, actor.Union());
}

void containerWaterStorage::enterWorld(World* pworld)
{
	ErosionContainer::enterWorld(pworld);
	initCollectData(pworld);
	registerUpdateTick();
	if (GetWorldManagerPtr())
	{
		
	}
	//updateEffect();
	pworld->markBlockForUpdate(m_BlockPos, true);
}

void containerWaterStorage::leaveWorld() {
	WorldContainer::leaveWorld();
	//stopEffect();
}

void containerWaterStorage::updateTick()
{
	ErosionContainer::updateTick();
	if (m_nOnceTickMax <= m_nTickCounter)
	{
		m_nTickCounter = 0;
		addWater(m_nOnceVolume);
	}
	else m_nTickCounter++;
	//m_desertSleepCount = -1;
}

void containerWaterStorage::addWater(int value)
{
	m_nCurWaterVolume += value;
	if (value < 0)
	{
		m_nCurWaterVolume = std::max(0, (int)m_nCurWaterVolume);
	}
	else
	{
		m_nCurWaterVolume = std::min((int)m_nWaterVolumeMax, (int)m_nCurWaterVolume);
	}
}

//void containerWaterStorage::updateEffect() {
//	if (m_World)
//	{
//		int blockdata = m_World->getBlockData(m_BlockPos);
//		if ((blockdata & 4) != 0)
//		{
//			return;
//		}
//		WCoord effectPos = getEffectPos(blockdata);
//		int maxage = Rainbow::MAX_INT;
//		m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_agonal.ent", effectPos, maxage);
//
//	}
//}

//void containerWaterStorage::stopEffect()
//{
//	if (m_World)
//	{
//		int blockdata = m_World->getBlockData(m_BlockPos);
//		stopEffectByBlockdata(blockdata);
//	}
//}

//void containerWaterStorage::stopEffectByBlockdata(int blockdata)
//{
//	if ((blockdata & 4) != 0)
//	{
//		return;
//	}
//	WCoord effectPos = getEffectPos(blockdata);
//	m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_agonal.ent", effectPos);
//}

//WCoord containerWaterStorage::getEffectPos(int blockdata)
//{
//	int placedir = blockdata & 3;
//	WCoord effectPos = NeighborCoord(m_BlockPos, ReverseDirection(placedir));
//	if (placedir == DIR_NEG_X)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(30, 100, 0);
//	}
//	else if (placedir == DIR_POS_X)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(-30, 100, 0);
//	}
//	else if (placedir == DIR_NEG_Z)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, 30);
//	}
//	else if (placedir == DIR_POS_Z)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, -30);
//	}
//	return effectPos;
//}

//-----------------------------------------------------containerArchitecture--------------------------------------------------------------------------
/**********************************************************************************************
类    名：containerArchitecture
功    能：建筑类型方块container
********************************************************************************************* */
containerArchitecture::containerArchitecture()
{
}

containerArchitecture::containerArchitecture(const WCoord& blockpos, int blockId, int bptypeid, int bplevel) 
	:ErosionContainer(blockpos, blockId)
	,m_nBluePrintLevel(bplevel)
	,m_nBluePrintTypeId(bptypeid)
{
}

 bool containerArchitecture::load(const void* srcdata)
 {
 	auto src = reinterpret_cast<const FBSave::ContainerArchitecture*>(srcdata);
 	loadErosionContainer(src->basedata());

 	m_nBluePrintTypeId = src->blueprinttype();
	m_nBluePrintLevel = src->architecturelv();

 	return true;
 }

 flatbuffers::Offset<FBSave::ChunkContainer> containerArchitecture::save(SAVE_BUFFER_BUILDER& builder)
 {
 	auto basedata = saveContainerErosion(builder);

 	auto actor = FBSave::CreateContainerArchitecture(builder, basedata, m_nBluePrintTypeId, m_nBluePrintLevel);

 	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerArchitecture, actor.Union());
 }

void containerArchitecture::enterWorld(World* pworld)
{
	ErosionContainer::enterWorld(pworld);
	registerUpdateTick();
	if (GetWorldManagerPtr())
	{

	}
	//updateEffect();
	pworld->markBlockForUpdate(m_BlockPos, true);
}

void containerArchitecture::leaveWorld() {
	WorldContainer::leaveWorld();
	//stopEffect();
}

void containerArchitecture::updateTick()
{
	ErosionContainer::updateTick();
	//m_desertSleepCount = -1;
}

//void containerArchitecture::initBluePrintData()
//{
//	if (m_BlockID >= 2501 && m_BlockID <= 2508)
//	{
//		m_nBluePrintLevel = (m_BlockID - 2499) / 2;
//		m_nBluePrintTypeId = (m_BlockID - 2501) % 2 + 1;
//	}
//	else if (m_BlockID >= 2530 && m_BlockID <= 2541)
//	{
//		m_nBluePrintLevel = ((m_BlockID - 2530) / 3) + 1;
//		m_nBluePrintTypeId = (m_BlockID - 2530) % 3 + 3;
//	}
//}

int containerArchitecture::checkArchitectureResEnough(int type, int upgradeLevel, ClientPlayer* player, bool deal)
{
	// 检查是否有有效的世界和蓝图数据
	if (m_nBluePrintTypeId == 0 || m_nBluePrintLevel == 0 || !player) {
		return -3;
	}
	// 获取建筑蓝图数据
	const ArchitecturalBlueprintCsvDef* blueprintDef = GetDefManagerProxy()->getArchitecturalBlueprintCsvDef(m_nBluePrintTypeId);
	if (!blueprintDef) {
		return -3;
	}

	float fProportion = 1.0f;
	int curLevel = m_nBluePrintLevel;
	if (type == 1)
	{
		if (m_nBluePrintLevel >= upgradeLevel)
		{
			return -2;
		}
		curLevel = upgradeLevel;
	}
	else if (type == 2)
	{
		fProportion = 0.1f;
	}
	
	// 查找目标方块ID和所需材料
	int targetBlockId = -3;
	std::map<int, int> requiredMaterials;

	if (curLevel == WoodStyle)
	{
		if (blueprintDef->Wood.ProduceItemID > 0)
		{
			targetBlockId = blueprintDef->Wood.ProduceItemID;
			if (blueprintDef->Wood.ConsumeItemID > 0 && blueprintDef->Wood.Count > 0) {
				requiredMaterials[blueprintDef->Wood.ConsumeItemID] = blueprintDef->Wood.Count;
			}
		}
	}
	else if (curLevel == StoneStyle)
	{
		if (blueprintDef->Stone.ProduceItemID > 0)
		{
			targetBlockId = blueprintDef->Stone.ProduceItemID;
			if (blueprintDef->Stone.ConsumeItemID > 0 && blueprintDef->Stone.Count > 0) {
				requiredMaterials[blueprintDef->Stone.ConsumeItemID] = blueprintDef->Stone.Count;
			}
		}
	}
	else if (curLevel == IronStyle)
	{
		if (blueprintDef->Iron.ProduceItemID > 0)
		{
			targetBlockId = blueprintDef->Iron.ProduceItemID;
			if (blueprintDef->Iron.ConsumeItemID > 0 && blueprintDef->Iron.Count > 0) {
				requiredMaterials[blueprintDef->Iron.ConsumeItemID] = blueprintDef->Iron.Count;
			}
		}
	}
	else if (curLevel == SteelStyle)
	{
		if (blueprintDef->Steel.ProduceItemID > 0)
		{
			targetBlockId = blueprintDef->Steel.ProduceItemID;
			if (blueprintDef->Steel.ConsumeItemID > 0 && blueprintDef->Steel.Count > 0) {
				requiredMaterials[blueprintDef->Steel.ConsumeItemID] = blueprintDef->Steel.Count;
			}
		}
	}


	// 如果没有有效的目标方块ID或所需材料，返回失败
	if (targetBlockId <= 0 || requiredMaterials.empty()) {
		return -3;
	}

	// 如果是上帝模式，直接返回0 成功
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) {
		return targetBlockId;
	}


	BackPack* backpack = player->getBackPack();
	if (!backpack) {
		return -3;
	}
	// 检查玩家背包中是否有足够的材料
	for (const auto& material : requiredMaterials) {
		int itemId = material.first;
		int count = material.second * fProportion;
		int haveNum = backpack->getItemCountInNormalPack(itemId);
		if (haveNum < count) {
			// 材料不足
			return -1;
		}
	}
	if (deal)
	{
		// 处理资源消耗
		for (const auto& material : requiredMaterials) {
			int itemId = material.first;
			int count = material.second * fProportion;
			backpack->removeItemInNormalPack(itemId, count);	
		}
	}	
	return targetBlockId;
}

int containerArchitecture::onUpgradeBlock(int upgradeNum, ClientPlayer* player, bool deal)
{
	int retItemId = checkArchitectureResEnough(1, upgradeNum, player, deal);
	if (retItemId >= 0)
	{
		auto pworld = player->getWorld();
		if (pworld)
		{
			int olddata = pworld->getBlockData(m_BlockPos);
			pworld->setBlockAll(m_BlockPos, retItemId, olddata);
		}
	}
	return retItemId;
}
