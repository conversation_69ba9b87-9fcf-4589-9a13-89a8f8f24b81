enum BLOCK_MINE_TYPE {};

$#include "Core/defdata.h"
$#include "Core/CoreCommonDef.h"
$#include "Core/worldData/world_types.h"
$cfile "../SandboxEngine/Core/blocks/special_blockid.h"
$cfile "../SandboxEngine/Core/blocks/BlockMaterialDef.h"
$cfile "../SandboxEngine/Core/blocks/BlockMaterialBase.h"
$cfile "../SandboxEngine/Core/blocks/BlockMaterial.h"
$cfile "Core/blocks/BlockMaterialUtil.h"
$cfile "../SandboxEngine/Core/blocks/container.h"
$cfile "../SandboxEngine/Core/blocks/container_world.h"
$cfile "Core/blocks/container_altar.h"
$cfile "Core/blocks/container_backpack.h"
$cfile "Core/blocks/container_honorFrame.h"
$cfile "../SandboxEngine/Core/blocks/container_buildblueprint.h"
$cfile "Core/blocks/container_pot.h"
$cfile "Core/blocks/container_signs.h"
$cfile "Core/blocks/container_arrowsigns.h"
$cfile "Core/blocks/container_fishframe.h"
$cfile "Core/blocks/FurnaceContainer.h"
$cfile "../SandboxEngine/Core/blocks/BlockMaterialMgr.h"
$cfile "Core/blocks/container_colorpalette.h"
$cfile "Core/blocks/container_tombstone.h"
$cfile "Core/blocks/container_fullycustommodel.h"
$cfile "Core/blocks/container_peristele.h"
$cfile "Core/blocks/BlockThicket.h"
$cfile "Core/blocks/ContainerStove.h"
$cfile "Core/blocks/container_polaroid.h"
$cfile "Core/blocks/container_newpot.h"
$cfile "Core/blocks/container_hydarm.h"
$cfile "Core/blocks/container_wireless_hydarm.h"
$cfile "Core/blocks/container_stone_core.h"
$cfile "Core/blocks/container_worldselectmobspawner.h"
$cfile "Core/blocks/container_socdoor.h"
$cfile "Core/blocks/container_socautodoor.h"
$cfile "Core/blocks/container_decomposition.h"
$cfile "Core/blocks/BlockWaterStorage.h"
$cfile "Core/blocks/container_sandboxGame.h"