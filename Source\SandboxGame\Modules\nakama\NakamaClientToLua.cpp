/*
** Lua binding: NakamaClientToLua
*/

#ifndef __cplusplus
#include "stdlib.h"
#endif
#include "string.h"

#include "Minitolua.h"

#include "ui_common.h"

/* Exported function */
TOLUA_API int  tolua_NakamaClientToLua_open (lua_State* tolua_S);

#include "NakamaClient.h"

/* function to release collected object via destructor */
#ifdef __cplusplus

static int tolua_collect_NakamaClient (lua_State* tolua_S)
{
 NakamaClient* self = (NakamaClient*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}
#endif


/* function to register type */
static void tolua_reg_types (lua_State* tolua_S)
{
 tolua_usertype(tolua_S,"NakamaClient");
 tolua_usertype(tolua_S,"TimerHandler");
}

/* method: new of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_new00
static int tolua_NakamaClientToLua_NakamaClient_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::string serverKey = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string host = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
  int port = ((int)  tolua_tonumber(tolua_S,4,0));
  bool ssl = ((bool)  tolua_toboolean(tolua_S,5,true));
  {
   NakamaClient* tolua_ret = (NakamaClient*)  Mtolua_new((NakamaClient)(serverKey,host,port,ssl));
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"NakamaClient");
   tolua_pushcppstring(tolua_S,(const char*)serverKey);
   tolua_pushcppstring(tolua_S,(const char*)host);
  }
 }
 return 3;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_new00_local
static int tolua_NakamaClientToLua_NakamaClient_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::string serverKey = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string host = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
  int port = ((int)  tolua_tonumber(tolua_S,4,0));
  bool ssl = ((bool)  tolua_toboolean(tolua_S,5,true));
  {
   NakamaClient* tolua_ret = (NakamaClient*)  Mtolua_new((NakamaClient)(serverKey,host,port,ssl));
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"NakamaClient");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
   tolua_pushcppstring(tolua_S,(const char*)serverKey);
   tolua_pushcppstring(tolua_S,(const char*)host);
  }
 }
 return 3;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_delete00
static int tolua_NakamaClientToLua_NakamaClient_delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetLuaCallback of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_SetLuaCallback00
static int tolua_NakamaClientToLua_NakamaClient_SetLuaCallback00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string fun = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetLuaCallback'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetLuaCallback(fun);
   tolua_pushcppstring(tolua_S,(const char*)fun);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetLuaCallback'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CrateRTClient of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_CrateRTClient00
static int tolua_NakamaClientToLua_NakamaClient_CrateRTClient00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CrateRTClient'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->CrateRTClient();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CrateRTClient'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: ConnectRTClient of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_ConnectRTClient00
static int tolua_NakamaClientToLua_NakamaClient_ConnectRTClient00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'ConnectRTClient'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->ConnectRTClient();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'ConnectRTClient'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: AuthenticateSteam of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_AuthenticateSteam00
static int tolua_NakamaClientToLua_NakamaClient_AuthenticateSteam00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string token = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string username = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'AuthenticateSteam'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->AuthenticateSteam(token,username);
   tolua_pushcppstring(tolua_S,(const char*)token);
   tolua_pushcppstring(tolua_S,(const char*)username);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'AuthenticateSteam'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: AuthenticateEmail of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_AuthenticateEmail00
static int tolua_NakamaClientToLua_NakamaClient_AuthenticateEmail00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string email = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string passwd = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
  const std::string username = ((const std::string)  tolua_tocppstring(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'AuthenticateEmail'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->AuthenticateEmail(email,passwd,username);
   tolua_pushcppstring(tolua_S,(const char*)passwd);
   tolua_pushcppstring(tolua_S,(const char*)username);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'AuthenticateEmail'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetAuthToken of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_GetAuthToken00
static int tolua_NakamaClientToLua_NakamaClient_GetAuthToken00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetAuthToken'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->GetAuthToken();
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetAuthToken'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: ListNotifications of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_ListNotifications00
static int tolua_NakamaClientToLua_NakamaClient_ListNotifications00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  int num = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'ListNotifications'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->ListNotifications(num);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'ListNotifications'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: DeleteNotification of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_DeleteNotification00
static int tolua_NakamaClientToLua_NakamaClient_DeleteNotification00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string notificationId = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'DeleteNotification'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->DeleteNotification(notificationId);
   tolua_pushcppstring(tolua_S,(const char*)notificationId);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'DeleteNotification'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetAccount of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_GetAccount00
static int tolua_NakamaClientToLua_NakamaClient_GetAccount00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetAccount'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->GetAccount();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetAccount'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: Rpc of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_Rpc00
static int tolua_NakamaClientToLua_NakamaClient_Rpc00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string id = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string payload = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'Rpc'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->Rpc(id,payload);
   tolua_pushcppstring(tolua_S,(const char*)id);
   tolua_pushcppstring(tolua_S,(const char*)payload);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'Rpc'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: AddFriend of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_AddFriend00
static int tolua_NakamaClientToLua_NakamaClient_AddFriend00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string id = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'AddFriend'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->AddFriend(id);
   tolua_pushcppstring(tolua_S,(const char*)id);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'AddFriend'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: DeleteFriend of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_DeleteFriend00
static int tolua_NakamaClientToLua_NakamaClient_DeleteFriend00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string id = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'DeleteFriend'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->DeleteFriend(id);
   tolua_pushcppstring(tolua_S,(const char*)id);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'DeleteFriend'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: BlockFriend of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_BlockFriend00
static int tolua_NakamaClientToLua_NakamaClient_BlockFriend00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string id = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'BlockFriend'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->BlockFriend(id);
   tolua_pushcppstring(tolua_S,(const char*)id);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'BlockFriend'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: ListFriend of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_ListFriend00
static int tolua_NakamaClientToLua_NakamaClient_ListFriend00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  int limit = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'ListFriend'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->ListFriend(limit);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'ListFriend'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: ListFriendRequest of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_ListFriendRequest00
static int tolua_NakamaClientToLua_NakamaClient_ListFriendRequest00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  int limit = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'ListFriendRequest'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->ListFriendRequest(limit);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'ListFriendRequest'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: ListBlockFriend of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_ListBlockFriend00
static int tolua_NakamaClientToLua_NakamaClient_ListBlockFriend00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  int limit = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'ListBlockFriend'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->ListBlockFriend(limit);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'ListBlockFriend'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: JoinRoom of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_JoinRoom00
static int tolua_NakamaClientToLua_NakamaClient_JoinRoom00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string roomName = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'JoinRoom'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->JoinRoom(roomName);
   tolua_pushcppstring(tolua_S,(const char*)roomName);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'JoinRoom'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: JoinGroup of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_JoinGroup00
static int tolua_NakamaClientToLua_NakamaClient_JoinGroup00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string groupId = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'JoinGroup'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->JoinGroup(groupId);
   tolua_pushcppstring(tolua_S,(const char*)groupId);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'JoinGroup'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: JoinDirectMessage of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_JoinDirectMessage00
static int tolua_NakamaClientToLua_NakamaClient_JoinDirectMessage00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string userId = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'JoinDirectMessage'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->JoinDirectMessage(userId);
   tolua_pushcppstring(tolua_S,(const char*)userId);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'JoinDirectMessage'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: WriteChatMessage of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_WriteChatMessage00
static int tolua_NakamaClientToLua_NakamaClient_WriteChatMessage00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string channelId = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string content = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'WriteChatMessage'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->WriteChatMessage(channelId,content);
   tolua_pushcppstring(tolua_S,(const char*)channelId);
   tolua_pushcppstring(tolua_S,(const char*)content);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'WriteChatMessage'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: LeaveChat of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_LeaveChat00
static int tolua_NakamaClientToLua_NakamaClient_LeaveChat00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string roomName = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'LeaveChat'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->LeaveChat(roomName);
   tolua_pushcppstring(tolua_S,(const char*)roomName);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'LeaveChat'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: ListChannelMessages of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_ListChannelMessages00
static int tolua_NakamaClientToLua_NakamaClient_ListChannelMessages00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,4,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string groupId = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  int limit = ((int)  tolua_tonumber(tolua_S,3,0));
  const std::string cursor = ((const std::string)  tolua_tocppstring(tolua_S,4,0));
  bool forward = ((bool)  tolua_toboolean(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'ListChannelMessages'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->ListChannelMessages(groupId,limit,cursor,forward);
   tolua_pushcppstring(tolua_S,(const char*)groupId);
   tolua_pushcppstring(tolua_S,(const char*)cursor);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'ListChannelMessages'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: UpdataName of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_UpdataName00
static int tolua_NakamaClientToLua_NakamaClient_UpdataName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
  const std::string Name = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'UpdataName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->UpdataName(Name);
   tolua_pushcppstring(tolua_S,(const char*)Name);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'UpdataName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: IsCreate of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_IsCreate00
static int tolua_NakamaClientToLua_NakamaClient_IsCreate00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'IsCreate'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->IsCreate();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsCreate'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: StartProcess of class  NakamaClient */
#ifndef TOLUA_DISABLE_tolua_NakamaClientToLua_NakamaClient_StartProcess00
static int tolua_NakamaClientToLua_NakamaClient_StartProcess00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"NakamaClient",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  NakamaClient* self = (NakamaClient*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'StartProcess'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->StartProcess();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'StartProcess'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* Open function */
TOLUA_API int tolua_NakamaClientToLua_open (lua_State* tolua_S)
{
 tolua_open(tolua_S);
 tolua_reg_types(tolua_S);
 tolua_module(tolua_S,NULL,0);
 tolua_beginmodule(tolua_S,NULL);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"NakamaClient","NakamaClient","TimerHandler",tolua_collect_NakamaClient);
  #else
  tolua_cclass(tolua_S,"NakamaClient","NakamaClient","TimerHandler",NULL);
  #endif
  tolua_beginmodule(tolua_S,"NakamaClient");
   tolua_constant(tolua_S,"AuthentMsg",NakamaClient::AuthentMsg);
   tolua_constant(tolua_S,"CrateRTMsg",NakamaClient::CrateRTMsg);
   tolua_constant(tolua_S,"AddFriendMsg",NakamaClient::AddFriendMsg);
   tolua_constant(tolua_S,"DeleteFriendMsg",NakamaClient::DeleteFriendMsg);
   tolua_constant(tolua_S,"BlockFriendMsg",NakamaClient::BlockFriendMsg);
   tolua_constant(tolua_S,"ListFriendMsg",NakamaClient::ListFriendMsg);
   tolua_constant(tolua_S,"ListNotificationMsg",NakamaClient::ListNotificationMsg);
   tolua_constant(tolua_S,"AccountMsg",NakamaClient::AccountMsg);
   tolua_constant(tolua_S,"NotificationsMsg",NakamaClient::NotificationsMsg);
   tolua_constant(tolua_S,"DeleteNotificationMsg",NakamaClient::DeleteNotificationMsg);
   tolua_constant(tolua_S,"BlockListFriendMsg",NakamaClient::BlockListFriendMsg);
   tolua_constant(tolua_S,"ListFriendRequestMsg",NakamaClient::ListFriendRequestMsg);
   tolua_constant(tolua_S,"RPCMsg",NakamaClient::RPCMsg);
   tolua_constant(tolua_S,"JoinRoomMsg",NakamaClient::JoinRoomMsg);
   tolua_constant(tolua_S,"JoinGroupMsg",NakamaClient::JoinGroupMsg);
   tolua_constant(tolua_S,"JoinDirectMessageMsg",NakamaClient::JoinDirectMessageMsg);
   tolua_constant(tolua_S,"ChannelMessageMsg",NakamaClient::ChannelMessageMsg);
   tolua_constant(tolua_S,"WriteChatMessageMsg",NakamaClient::WriteChatMessageMsg);
   tolua_constant(tolua_S,"RTDisconnectMsg",NakamaClient::RTDisconnectMsg);
   tolua_constant(tolua_S,"ListChannelMessagesMsg",NakamaClient::ListChannelMessagesMsg);
   tolua_constant(tolua_S,"CallbackOK",NakamaClient::CallbackOK);
   tolua_constant(tolua_S,"CallbackError",NakamaClient::CallbackError);
   tolua_function(tolua_S,"new",tolua_NakamaClientToLua_NakamaClient_new00);
   tolua_function(tolua_S,"new_local",tolua_NakamaClientToLua_NakamaClient_new00_local);
   tolua_function(tolua_S,".call",tolua_NakamaClientToLua_NakamaClient_new00_local);
   tolua_function(tolua_S,"delete",tolua_NakamaClientToLua_NakamaClient_delete00);
   tolua_function(tolua_S,"SetLuaCallback",tolua_NakamaClientToLua_NakamaClient_SetLuaCallback00);
   tolua_function(tolua_S,"CrateRTClient",tolua_NakamaClientToLua_NakamaClient_CrateRTClient00);
   tolua_function(tolua_S,"ConnectRTClient",tolua_NakamaClientToLua_NakamaClient_ConnectRTClient00);
   tolua_function(tolua_S,"AuthenticateSteam",tolua_NakamaClientToLua_NakamaClient_AuthenticateSteam00);
   tolua_function(tolua_S,"AuthenticateEmail",tolua_NakamaClientToLua_NakamaClient_AuthenticateEmail00);
   tolua_function(tolua_S,"GetAuthToken",tolua_NakamaClientToLua_NakamaClient_GetAuthToken00);
   tolua_function(tolua_S,"ListNotifications",tolua_NakamaClientToLua_NakamaClient_ListNotifications00);
   tolua_function(tolua_S,"DeleteNotification",tolua_NakamaClientToLua_NakamaClient_DeleteNotification00);
   tolua_function(tolua_S,"GetAccount",tolua_NakamaClientToLua_NakamaClient_GetAccount00);
   tolua_function(tolua_S,"Rpc",tolua_NakamaClientToLua_NakamaClient_Rpc00);
   tolua_function(tolua_S,"AddFriend",tolua_NakamaClientToLua_NakamaClient_AddFriend00);
   tolua_function(tolua_S,"DeleteFriend",tolua_NakamaClientToLua_NakamaClient_DeleteFriend00);
   tolua_function(tolua_S,"BlockFriend",tolua_NakamaClientToLua_NakamaClient_BlockFriend00);
   tolua_function(tolua_S,"ListFriend",tolua_NakamaClientToLua_NakamaClient_ListFriend00);
   tolua_function(tolua_S,"ListFriendRequest",tolua_NakamaClientToLua_NakamaClient_ListFriendRequest00);
   tolua_function(tolua_S,"ListBlockFriend",tolua_NakamaClientToLua_NakamaClient_ListBlockFriend00);
   tolua_function(tolua_S,"JoinRoom",tolua_NakamaClientToLua_NakamaClient_JoinRoom00);
   tolua_function(tolua_S,"JoinGroup",tolua_NakamaClientToLua_NakamaClient_JoinGroup00);
   tolua_function(tolua_S,"JoinDirectMessage",tolua_NakamaClientToLua_NakamaClient_JoinDirectMessage00);
   tolua_function(tolua_S,"WriteChatMessage",tolua_NakamaClientToLua_NakamaClient_WriteChatMessage00);
   tolua_function(tolua_S,"LeaveChat",tolua_NakamaClientToLua_NakamaClient_LeaveChat00);
   tolua_function(tolua_S,"ListChannelMessages",tolua_NakamaClientToLua_NakamaClient_ListChannelMessages00);
   tolua_function(tolua_S,"UpdataName",tolua_NakamaClientToLua_NakamaClient_UpdataName00);
   tolua_function(tolua_S,"IsCreate",tolua_NakamaClientToLua_NakamaClient_IsCreate00);
   tolua_function(tolua_S,"StartProcess",tolua_NakamaClientToLua_NakamaClient_StartProcess00);
  tolua_endmodule(tolua_S);
 tolua_endmodule(tolua_S);
 return 1;
}


#if defined(LUA_VERSION_NUM) && LUA_VERSION_NUM >= 501
 TOLUA_API int luaopen_NakamaClientToLua (lua_State* tolua_S) {
 return tolua_NakamaClientToLua_open(tolua_S);
};
#endif

